version: '3.8'

services:
  # In prod, we don't expose database or meilisearch ports for security
  database:
    ports:
      - "5433:5432" # Expose port for local database tools

  meilisearch:
    environment:
      - MEILI_ENV=production

  frontend: {} # No overrides needed

  proxy:
    depends_on:
      - frontend
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./tools/nginx/default.conf:/etc/nginx/templates/default.conf.template
      - ./certbot_conf:/etc/letsencrypt
      - ./certbot_www:/var/www/certbot
      - ./tools/nginx/502.html:/var/www/html/maintenance/502.html
      - ./static-dir:/var/www/static-dir