# Stage 1: Install dependencies
FROM node:lts-alpine AS deps
# Install security updates for the OS
WORKDIR /app
COPY package.json yarn.lock* ./
RUN yarn install --frozen-lockfile

# Stage 2: Build the application
FROM node:lts-alpine AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN ln -s /usr/lib/libssl.so.3 /lib/libssl.so.3
# Add these two lines
ARG DATABASE_URL
ENV DATABASE_URL=$DATABASE_URL
RUN yarn prisma generate
RUN yarn build

# Stage 3: Production image
FROM node:lts-alpine AS runner
WORKDIR /app
ENV NODE_ENV=production

# Install security updates in the final production image as well
RUN ln -s /usr/lib/libssl.so.3 /lib/libssl.so.3
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

EXPOSE 3000
CMD ["node", "server.js"]