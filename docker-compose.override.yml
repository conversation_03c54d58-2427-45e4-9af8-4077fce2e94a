version: '3.8'

services:
  database:
    ports:
      - "5433:5432" # Expose port for local database tools

  meilisearch:
    environment:
      - MEILI_ENV=development # Override for dev

  frontend:
    restart: "no"
    ports:
      - "3000:3000" # Expose app port for direct browser access
  # volumes:
  #   - .:/app # Mount code for live-reloading

  proxy:
    ports:
      - "80:80"
    volumes:
      - ./tools/nginx/dev.conf:/etc/nginx/templates/default.conf.template