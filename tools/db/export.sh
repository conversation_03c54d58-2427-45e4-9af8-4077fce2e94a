#!/bin/bash

# A robust script to back up the PostgreSQL database running via Docker Compose.
# All status messages are redirected to stderr to keep stdout clean for the backup data.

# Exit immediately if a command exits with a non-zero status.
set -e

# --- Configuration ---
# The generic service name for the database in your docker-compose.yml file.
DB_SERVICE_NAME="database"

# --- Script Logic ---

# 1. Find the running container ID for the database service.
DB_CONTAINER_ID=$(docker compose ps -q "$DB_SERVICE_NAME")

# 2. Check if the container ID was found.
if [ -z "$DB_CONTAINER_ID" ]; then
    # Redirect error messages to stderr
    echo "Error: Could not find a running container for the '${DB_SERVICE_NAME}' service." >&2
    exit 1
fi

# Redirect status messages to stderr
echo "✅ Found database container: ${DB_CONTAINER_ID}" >&2

# 3. Source environment variables from the .env file to get the database credentials.
if [ ! -f .env ]; then
    echo "Error: .env file not found. Please run this script from the project root." >&2
    exit 1
fi
export $(grep -v "^#" .env | xargs)

echo "🚀 Creating database backup..." >&2

# 4. Run pg_dump inside the container.
# The output of this command is the actual backup data, which goes to stdout.
docker exec -e PGPASSWORD="$POSTGRES_PASS" "$DB_CONTAINER_ID" \
    pg_dump -h "$DB_SERVICE_NAME" -U "$POSTGRES_USERNAME" -d "$POSTGRES_DATABASE" -F c -C

echo "✅ Backup command finished. Data sent to standard output." >&2