#!/bin/bash

# A robust script to restore a PostgreSQL database backup into a container running via Docker Compose.

# Exit immediately if a command exits with a non-zero status.
set -e

# --- Configuration ---
# The generic service name for the database in your docker-compose.yml file.
DB_SERVICE_NAME="database"

# --- Script Logic ---

# 1. Find the running container ID for the database service.
# The `docker-compose ps -q <service_name>` command is the most reliable way to do this.
DB_CONTAINER_ID=$(docker compose ps -q "$DB_SERVICE_NAME")

# 2. Check if the container ID was found.
if [ -z "$DB_CONTAINER_ID" ]; then
    echo "Error: Could not find a running container for the '${DB_SERVICE_NAME}' service."
    exit 1
fi

echo "✅ Found database container: ${DB_CONTAINER_ID}"

# 3. Source environment variables from the .env file to get the database credentials.
# This assumes the script is run from the project root where .env is located.
if [ ! -f .env ]; then
    echo "Error: .env file not found. Please run this script from the project root."
    exit 1
fi
export $(grep -v "^#" .env | xargs)

echo "🚀 Restoring database from backup..."
echo "Note: This will read the backup data from standard input."

# 4. Run pg_restore inside the container.
# We connect using the service name 'database' which works inside the Docker network.
# The backup data is piped into this command from your package.json script.
docker exec -i -e PGPASSWORD="$POSTGRES_PASS" "$DB_CONTAINER_ID" \
    pg_restore --verbose --clean --no-acl --no-owner -h "$DB_SERVICE_NAME" -U "$POSTGRES_USERNAME" -d "$POSTGRES_DATABASE"

echo "✅ Restore command executed successfully."