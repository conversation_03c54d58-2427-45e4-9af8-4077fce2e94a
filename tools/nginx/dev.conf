# This is your Nginx configuration template.
# It will be processed by envsubst at container startup to substitute variables like ${DOCKER_HOST_IP} and ${NGINX_PORT}.
# The processed file will be placed in /etc/nginx/conf.d/default.conf by the Nginx image's entrypoint.

server {
    # Nginx will listen on the port specified by the NGINX_PORT environment variable (set to 80 in docker-compose.yml)
    listen 80;
    
    # You can replace '_' with your actual domain name if you have one
    server_name _;

    #charset utf-8;

    # Max upload size
    client_max_body_size 75M;

    # Proxy requests to the Next.js application
    location / {
        # The DOCKER_HOST_IP variable should be the IP of your host machine where Next.js is running on port 3000.
        # If your Next.js app were another Docker service named 'nextjs-app', you would use:
        # proxy_pass http://nextjs-app:3000;
        proxy_pass http://*************:3000;
        
        # Necessary headers for Next.js (especially for WebSockets if used)
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade; # Bypass cache for upgraded connections
    }

    # Optional: Serve static files directly from Nginx for better performance
    # Ensure the path '/var/www/static-dir/' matches the volume mount in docker-compose.yml
    # And that your Next.js app serves static assets from a '/static/' URL path or similar.
    location /uploads {
       alias /var/www/static-dir/uploads;
       expires 30d; # Cache static files for 30 days
       add_header Cache-Control "public";
    }

    # Optional: Custom error pages
    # This uses the 502.html you've mounted.
    error_page 500 502 503 504 /502.html; # You can map more error codes if needed
    location = /502.html {
        root /var/www/html/maintenance/;
        internal; # Prevents direct access to the error page
    }
}