# Server block for handling ALL HTTP traffic and redirecting to https://udoymart.com
server {
    listen 80 default_server;
    listen [::]:80 default_server; # Listen on IPv6 as well
    server_name _ ; # Catch-all for any hostname on port 80, including IP address and www

    # Location for Let's Encrypt ACME challenge
    location /.well-known/acme-challenge/ {
        root /var/www/certbot; # Path for Certbot challenges
    }

    # Redirect all other HTTP traffic to the canonical HTTPS non-www domain
    location / {
        return 301 https://udoymart.com$request_uri;
    }
}

# Server block for HTTPS (handles udoymart.com, www.udoymart.com, and IP)
server {
    proxy_buffer_size   128k;
    proxy_buffers   4 256k;
    proxy_busy_buffers_size   256k;

    listen 443 ssl http2 default_server; # default_server helps catch direct IP access
    listen [::]:443 ssl http2 default_server; # Listen on IPv6 as well
    server_name udoymart.com www.udoymart.com **************; # Explicitly list names it handles

    # SSL Certificate paths - ensure this is the correct certificate covering both domains
    ssl_certificate /etc/letsencrypt/live/udoymart.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/udoymart.com/privkey.pem;

    # SSL Configuration (your existing secure settings)
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers off;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_ecdh_curve secp384r1; # Or prime256v1
    ssl_session_timeout  10m;
    ssl_session_cache shared:SSL:10m;
    ssl_session_tickets off;
    ssl_stapling on;
    ssl_stapling_verify on;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;
    # add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always; # Uncomment after thorough testing
    # ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # Uncomment if you have this file

    # Max upload size
    client_max_body_size 75M;

    # Redirect www to non-www for HTTPS
    if ($host = www.udoymart.com) {
        return 301 https://udoymart.com$request_uri;
    }

    # Redirect HTTPS IP access to the domain
    # Note: Browser SSL warning will appear first for https://IP_ADDRESS, then redirect.
    if ($host = **************) {
        return 301 https://udoymart.com$request_uri;
    }

    # Location for Let's Encrypt ACME challenge (can be useful if cert renewal ever tries HTTPS challenge)
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # Proxy requests to the Next.js application (only if host is udoymart.com)
    # The if conditions above will handle redirects for www and IP.
    location / {
        proxy_pass http://frontend:3000; # Or your Next.js container name
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host; # This will be udoymart.com after redirects
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    location /uploads {
       alias /var/www/static-dir/uploads;
       expires 30d;
       add_header Cache-Control "public";
    }

    error_page 500 502 503 504 /502.html;
    location = /502.html {
        root /var/www/html/maintenance/;
        internal;
    }
}