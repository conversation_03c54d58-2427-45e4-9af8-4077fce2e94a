self.addEventListener('push', function (event) {
  if (event.data) {
    const data = event.data.json()
    const options = {
      body: data.body,
      icon: data.icon || 'https://udoymart.com/icon-192x192.png',
      badge: "https://udoymart.com/udoymart-logo-small.png",
      vibrate: [200, 100, 200, 100, 200, 100, 200],
      image: data.image,
      requireInteraction: true,
      data: {
        dateOfArrival: Date.now(),
        primaryKey: '2',
        url: data.url || "https://udoymart.com",
        notificationId: data.notificationId,
      },
    }
    event.waitUntil(self.registration.showNotification(data.title, options))
  }
})

self.addEventListener('notificationclick', function (event) {
  const data = event.notification.data;
  const notificationId = data.notificationId;
  
  // Close the notification
  event.notification.close()
  
  // Track the click by sending a request to the server
  if (notificationId) {
    const trackClickPromise = fetch('/api/notifications/track-click', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        notificationId: notificationId,
      }),
    }).catch(err => console.error('Failed to track notification click:', err));
    
    // Open the target URL and track the click in parallel
    event.waitUntil(Promise.all([
      clients.openWindow(data.url),
      trackClickPromise
    ]));
  } else {
    // If no notification ID is available, just open the URL
    event.waitUntil(clients.openWindow(data.url));
  }
})
