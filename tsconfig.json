{"compilerOptions": {"experimentalDecorators": true, "target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@udoy/*": ["./src/*"], "@dashboard/*": ["./src/app/[locale]/dashboard/*"]}}, "include": ["**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "next-env.d.ts", "temp/types/**/*.ts", "public/sw.js"], "exclude": ["node_modules"]}