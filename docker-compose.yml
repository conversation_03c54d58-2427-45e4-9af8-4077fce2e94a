version: '3.8'

services:
  database:
    image: bitnami/postgresql:latest
    restart: always
    env_file: .env # All services will use the single .env file
    volumes:
      - postgresql:/bitnami/postgresql
    tmpfs:
      - /var/lib/pg_stat_tmp
    networks:
      - app-network
    environment:
      - POSTGRESQL_USERNAME=${POSTGRES_USERNAME}
      - POSTGRESQL_PASSWORD=${POSTGRES_PASS}
      - POSTGRESQL_DATABASE=${POSTGRES_DATABASE}
      - POSTGRESQL_POSTGRES_PASS=${POSTGRES_ADMIN}

  meilisearch:
    image: getmeili/meilisearch:latest
    restart: always
    env_file: .env
    volumes:
      - meilisearch_data:/data.ms
    networks:
      - app-network
    environment:
      - MEILI_MASTER_KEY=${MEILI_MASTER_KEY} # Add to your .env file
    ports:
      - "7700:7700"

  frontend:
    build:
      context: .
      network: host
      args:
        # This build-time URL uses the generic service name 'database'.
        # It requires the database to be running before you build the frontend.
        DATABASE_URL: "postgresql://${POSTGRES_USERNAME}:${POSTGRES_PASS}@localhost:5433/${POSTGRES_DATABASE}"
    restart: always
    env_file: .env
    networks:
      - app-network
    depends_on:
      - database
      - meilisearch
    volumes:
      - ./static-dir:/app/static-dir

  proxy:
    image: nginx
    restart: always
    volumes:
      - ./tools/nginx/502.html:/var/www/html/maintenance/502.html
      - ./static-dir:/var/www/static-dir  
    networks:
      - app-network

volumes:
  postgresql:
  meilisearch_data:

networks:
  app-network:
    driver: bridge