// @ts-nocheck

import { PrismaClient } from "@prisma/client";
import data from "../data.json";
import { writeFileSync } from "fs";
import OpenAI from "openai";

async function main() {
  const prisma = new PrismaClient();
  const client = new OpenAI({
    apiKey: "",
  });
  try {
    const products = await prisma.product.findMany({
      where: {
        hide: true,
      },
      include: {
        category: true,
      },
    });

    console.log(`Total Products: ${products.length}`);
    let count = products.length;

    for (const product of products) {
      console.log(`Processing ${product.name} (${count--})`);

      if (data[product.id]) {
        console.log("Found", product.name);
        continue;
      }

      const response = await client.responses.create({
        model: "gpt-4o-mini",
        input: `You are an expert Bengali copywriter specializing in creating compelling product descriptions for the Bangladeshi e-commerce market. Your goal is to generate a description that is natural, appealing, and drives sales.

**Strict Instructions:**
1.  **Language:** Generate the response **only** in natural-sounding, modern Bengali.
2.  **Avoid Literal Translations:** Do not use awkward, literal translations from English. For instance, instead of "high-quality," use terms like "উন্নত মানের" or "চমৎকার." Instead of the generic "fresh" for food, use "সতেজ," "টাটকা," or describe its origin (e.g., "খামার থেকে আনা").
3.  **Tone & Style:** The tone should be Friendly and Informative. The writing style should be persuasive, easy to read, and build trust with the customer.

Generate the following in JSON format: 
1.  An appealing English description ("description_en"), around 20-30 words. 
2.  A corresponding appealing Bengali description ("description_bn"), around 20-30 words. 
3.  A romanized "Banglish" version of the Bengali title ("title_bn_roman"). For example, 'তরমুজ' becomes 'tormuj'. 
4. A Url and seo friendly slug for the product ("product_slug"), always try to keep the unit as there could be multiple version of the same product.

Return only valid JSON. Do not include Markdown, explanations, or code block delimiters like \`\`\`json.

Dynamic Part:
"Product Title (English):"${product.name}" 
Product Title (Bengali): "${product.nam || ""}" 
Product Category: "${product.category?.slug || ""}" 
Product Category (Bengali): "${product.category?.nam || ""}"`,
      });

      try {
        const result = JSON.parse(response.output_text);
        data[product.id] = result;
        console.log(`Generated for: ${product.name}`);

        // Save after each product to avoid losing progress
        writeFileSync("data.json", JSON.stringify(data, null, 2));
      } catch (error) {
        console.error(`Failed to parse JSON for ${product.name}:`, error);
        // Store raw response to fix manually later
        data[product.id] = { raw: response.output_text };
        writeFileSync("data.json", JSON.stringify(data, null, 2));
      }

      // Add a small delay to avoid rate limits
      await new Promise((resolve) => setTimeout(resolve, 300));
    }
  } catch (error) {
    console.error("Error processing products:", error);
  } finally {
    prisma.$disconnect();
    writeFileSync("data.json", JSON.stringify(data, null, 2));
    console.log("Process completed. Data saved to data.json");
  }
}

main();
