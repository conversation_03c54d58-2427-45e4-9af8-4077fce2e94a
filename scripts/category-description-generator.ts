// @ts-nocheck

import { PrismaClient } from "@prisma/client";
import { writeFileSync, readFileSync, existsSync } from "fs";
import OpenAI from "openai";

// Initialize data file if it doesn't exist
const DATA_FILE = "category-descriptions.json";
let categoryData = {};
if (existsSync(DATA_FILE)) {
  try {
    categoryData = JSON.parse(readFileSync(DATA_FILE, "utf-8"));
  } catch (error) {
    console.error("Error reading data file:", error);
  }
}

async function main() {
  const prisma = new PrismaClient();
  const client = new OpenAI({
    apiKey: "",
  });

  try {
    // Get all categories
    const categories = await prisma.category.findMany({
      include: {
        subCategories: {
          orderBy: [
            {
              hide: "asc",
            },
            {
              featured: "desc",
            },
            {
              position: "asc",
            },
          ],
          select: {
            id: true,
            name: true,
            nam: true,
            slug: true,
          },
        },
        products: {
          orderBy: [
            {
              hide: "asc",
            },
            {
              featured: "desc",
            },
            {
              position: "asc",
            },
          ],
          take: 5,
          select: {
            name: true,
            nam: true,
          },
        },
      },
      // take: 10,
    });

    console.log(`Total Categories: ${categories.length}`);

    for (const category of categories) {
      console.log(`Processing: ${category.name}`);

      // Skip if already processed
      if (categoryData[category.id]) {
        console.log(`Already processed: ${category.name}`);
        continue;
      }

      // Determine if this is a parent category or base category
      const isParentCategory =
        category.subCategories && category.subCategories.length > 0;

      let prompt;
      if (isParentCategory) {
        // Create sub-categories list for parent category prompt
        const subCategoriesList = category.subCategories
          .map((sub) => `${sub.name}->${sub.nam || ""}`)
          .join(", ");

        prompt = `You are an expert E-commerce SEO Specialist and a senior Bengali Copywriter for the Bangladeshi market. Your mission is to create a compelling, SEO-optimized title and description for a parent product category that contains several sub-categories.

Core Instructions:

    Goal: Generate a welcoming overview that clearly communicates the breadth of products available. The content must encourage users to explore the sub-categories.
    SEO-Friendly Titles: Titles must be clear, concise, and include primary keywords people would use to search for this entire group of products in Bangladesh.
    Sales-Friendly Descriptions: The description (20-35 words) must summarize the main sub-categories from the provided list. Frame the category as a complete solution for the customer's needs, focusing on convenience and variety.
    Language: Use natural, modern Bengali. The Banglish title should be a simple, phonetic romanization.
    Output: Generate a single, clean JSON object with the specified keys.

Required JSON Output Format:

{
"title_bn": "Bangla SEO Friendly Title",
"title_en": "English SEO Friendly Title",
"description_bn": "Bangla Description",
"description_en": "English Description",
"title_bn_roman": "Banglish Title"
}

CATEGORY INFORMATION:
category_name_en: "${category.name}"
category_name_bn: "${category.nam || ""}"
sub_categories_list: "${subCategoriesList}"`;
      } else {
        // Create product examples for base category prompt
        const productExamples = category.products
          .map((product) => product.name)
          .join(", ");

        prompt = `You are an expert E-commerce SEO Specialist and a senior Bengali Copywriter for the Bangladeshi market. Your mission is to create a compelling, SEO-optimized title and description for a base product category that contains a list of products.

**Core Instructions:**
1.  **Goal:** Entice users to browse the products by highlighting the variety, quality, and types of items available.
2.  **SEO-Friendly Titles:** Titles must be specific and rich with keywords related to the products in this category. Use terms people in Bangladesh would search for.
3.  **Sales-Friendly Descriptions:** The description (20-35 words) must give a clear overview of the *types* of products inside, using the provided product examples as a guide. Focus on benefits, quality, and specific uses (e.g., daily cooking, special occasions).
4.  **Language:** Use natural, modern Bengali. The Banglish title should be a simple, phonetic romanization.
5.  **Output:** Generate a single, clean JSON object with the specified keys.


**Required JSON Output Format:**

{
  "title_bn": "Bangla SEO Friendly Title",
  "title_en": "English SEO Friendly Title",
  "description_bn": "Bangla Description",
  "description_en": "English Description",
  "title_bn_roman": "Banglish Title"
}

Some example:
English: Perfect for your baking and cooking needs. Choose from our high-quality selection of flour (atta) and all-purpose flour (maida) for the softest rotis and fluffiest baked goods.
Bengali: নরম রুটি অথবা তুলতুলে পরোটার জন্য সেরা মানের আটা ও ময়দা কিনুন। আপনার সকালের নাস্তা বা বিকেলের সব আয়োজনে যোগ করুন নতুন মাত্রা।

English: Get your kitchen essentials right here. From pure, refined sugar to iodized salt, we have the quality basics you need to perfect the taste of every dish.
Bengali: আপনার রান্নার অপরিহার্য উপকরণ। খাবারের সঠিক স্বাদ নিশ্চিত করতে সেরা মানের পরিশোধিত চিনি এবং আয়োডিনযুক্ত লবণ কিনুন আমাদের কাছ থেকে।

CATEGORY INFORMATION:
category_name_en: "${category.name}"
category_name_bn: "${category.nam || ""}"
product_examples:"${productExamples}"`;
      }

      try {
        const response = await client.chat.completions.create({
          model: "gpt-4o-mini",
          messages: [
            {
              role: "system",
              content:
                "You are an expert E-commerce SEO Specialist and Bengali Copywriter.",
            },
            {
              role: "user",
              content: prompt,
            },
          ],
          response_format: { type: "json_object" },
        });

        const result = JSON.parse(response.choices[0].message.content);
        categoryData[category.id] = result;
        console.log(`Generated description for: ${category.name}`);

        // Save after each category to avoid losing progress
        writeFileSync(DATA_FILE, JSON.stringify(categoryData, null, 2));

        // Add a small delay to avoid rate limits
        await new Promise((resolve) => setTimeout(resolve, 1000));
      } catch (error) {
        console.error(
          `Failed to generate description for ${category.name}:`,
          error
        );
        categoryData[category.id] = { error: true, message: error.message };
        writeFileSync(DATA_FILE, JSON.stringify(categoryData, null, 2));
      }
    }

    console.log("All categories processed successfully!");
  } catch (error) {
    console.error("Error processing categories:", error);
  } finally {
    await prisma.$disconnect();
    writeFileSync(DATA_FILE, JSON.stringify(categoryData, null, 2));
    console.log(`Process completed. Data saved to ${DATA_FILE}`);
  }
}

main();
