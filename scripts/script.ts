import { PrismaClient } from "@prisma/client";
import * as readline from "readline";

const prisma = new PrismaClient();

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});
async function main() {

  const name = await new Promise<string>((resolve) => {
    rl.question("Enter product name to search: ", (answer) => {
      resolve(answer);
    });
  });

  if (!name) {
    console.log("No Name Provided");
    return;
  }

  const products = await prisma.product.findMany({
    where: {
      companyId: null,
      name: {
        contains: name,
      },
    },
  });

  console.log(products.map((item) => item.name));
  console.log(`Total: ${products.length}`);

  //   Take user consent if he wants to update products
  const confirm = await new Promise<string>((resolve) => {
    rl.question("Do you want to update these products? (y/n): ", (answer) => {
      resolve(answer);
    });
  });

  if (confirm.toLowerCase() !== "y") {
    console.log("Aborted");
    return main();
  }

  const companySlug = await new Promise<string>((resolve) => {
    rl.question("Enter Company Slug: ", (answer) => {
      resolve(answer);
    });
  });

  if (!companySlug) {
    console.log("No Company Slug Provided");
    return;
  }

  const company = await prisma.company.findUnique({
    where: {
      slug: companySlug,
    },
  });

  if (!company) {
    console.log("No Company Found");
    return main();
  }

  await prisma.product.updateMany({
    where: {
      id: {
        in: products.map((item) => item.id),
      },
    },
    data: {
      companyId: company.id,
    },
  });

  console.log("Updated Products");

  main();
}

main();
