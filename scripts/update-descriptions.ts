// @ts-nocheck

import { PrismaClient } from "@prisma/client";
import { readFileSync, existsSync } from "fs";
import categoryData from "/home/<USER>/Documents/process-images/category-descriptions.json";
import productData from "/home/<USER>/Documents/process-images/product-descriptions.json";

// File paths

async function main() {
  // Check if data files exist
  if (
    !existsSync(
      "/home/<USER>/Documents/process-images/product-descriptions.json"
    )
  ) {
    console.error(`Product data file not found!`);
    return;
  }

  if (
    !existsSync(
      "/home/<USER>/Documents/process-images/category-descriptions.json"
    )
  ) {
    console.error(`Category data file not found!`);
    return;
  }

  // Load data from files
  try {
    console.log(
      `Loaded product descriptions for ${
        Object.keys(productData).length
      } products`
    );
  } catch (error) {
    console.error(`Error reading product data file: ${error.message}`);
    return;
  }

  try {
    console.log(
      `Loaded category descriptions for ${
        Object.keys(categoryData).length
      } categories`
    );
  } catch (error) {
    console.error(`Error reading category data file: ${error.message}`);
    return;
  }

  // Initialize Prisma client
  const prisma = new PrismaClient();

  try {
    // Update product descriptions
    console.log("Updating product descriptions...");
    let productUpdateCount = 0;

    for (const [productId, item] of Object.entries(productData)) {
      try {
        await prisma.product.update({
          where: { id: productId },
          data: {
            description: item.description_en || "",
            biboron: item.description_bn || "",
            titleBe: item.title_bn_roman || "",
            slug: item.product_slug || undefined,
          },
        });
        productUpdateCount++;
      } catch (error) {
        console.error(
          `Failed to update product ${productId}: ${error.message}`
        );
      }
    }

    console.log(`Updated descriptions for ${productUpdateCount} products`);

    // Update category descriptions
    console.log("Updating category descriptions...");
    let categoryUpdateCount = 0;

    for (const [categoryId, data] of Object.entries(categoryData)) {
      if (data?.error) {
        console.log(`Skipping category ${categoryId} due to invalid data`);
        continue;
      }

      try {
        await prisma.category.update({
          where: { id: categoryId },
          data: {
            titleEn: data.title_en || "",
            titleBn: data.title_bn || "",
            titleBe: data.title_bn_roman || "",
            descriptionEn: data.description_en || "",
            descriptionBn: data.description_bn || "",
          },
        });
        categoryUpdateCount++;
      } catch (error) {
        console.error(
          `Failed to update category ${categoryId}: ${error.message}`
        );
      }
    }

    console.log(`Updated descriptions for ${categoryUpdateCount} categories`);
  } catch (error) {
    console.error(`Error during update process: ${error.message}`);
  } finally {
    await prisma.$disconnect();
    console.log("Update process completed");
  }
}

main();
