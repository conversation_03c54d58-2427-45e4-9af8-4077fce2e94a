{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prisma:studio": "prisma studio", "vercel-build": "prisma generate && prisma migrate deploy && next build", "gen:icons": "fantasticon src/assets/font-icons/svgs -o src/assets/font-icons --normalize", "gen:env": "npx gen-env-types example.env -o src/libs/env/env.d.ts", "index:products": "npx jiti tools/search/indexProducts.ts", "backup:db": "./tools/db/export.sh > ../backup/udoymart.bin", "backup:image": "zip -r9 backup.zip static-dir/ && mv backup.zip ../backup/"}, "prisma": {"seed": "jiti prisma/seed.ts"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.0.1", "@next/third-parties": "^15.3.5", "@paralleldrive/cuid2": "^2.2.2", "@popperjs/core": "^2.11.8", "@prisma/client": "5.4.2", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.4", "@react-oauth/google": "^0.12.1", "@tanstack/react-query": "^5.12.2", "@tanstack/react-table": "^8.21.3", "@uploadthing/react": "^6.0.2", "@xstate/store": "^3.5.1", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.0", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.5.2", "googleapis": "^128.0.0", "immer": "^10.1.1", "jotai": "^2.12.4", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lucide-react": "^0.507.0", "meilisearch": "^0.50.0", "next": "^15.3.0-canary.33", "next-intl": "^4.0.2", "next-themes": "^0.4.6", "openai": "^5.3.0", "pdfkit": "^0.17.1", "prisma": "^5.4.2", "react": "19.1.0", "react-confetti": "^6.4.0", "react-countup": "^6.5.3", "react-day-picker": "8.10.1", "react-dom": "19.1.0", "react-hook-form": "^7.56.1", "react-popper": "^2.3.0", "react-toastify": "^9.1.3", "react-use": "^17.6.0", "recharts": "^2.15.3", "recoil": "^0.7.7", "reflect-metadata": "^0.1.13", "sass": "^1.69.5", "server-only": "^0.0.1", "sharp": "^0.33.0", "sonner": "^2.0.3", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "uploadthing": "^6.0.3", "uuid": "^11.1.0", "web-push": "^3.6.7", "zod": "^3.24.3"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.4", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/pdfkit": "^0.13.9", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "@types/web-push": "^3.6.4", "autoprefixer": "^10", "chalk": "^5.4.1", "dotenv": "^16.5.0", "eslint": "^9.25.1", "eslint-config-next": "15.2.4", "fantasticon": "^2.0.0", "jiti": "^1.20.0", "postcss": "^8", "tailwindcss": "3.4.17", "typescript": "^5.8.3", "zx": "^8.5.4"}, "resolutions": {"@types/react": "19.1.0", "@types/react-dom": "19.1.1"}}