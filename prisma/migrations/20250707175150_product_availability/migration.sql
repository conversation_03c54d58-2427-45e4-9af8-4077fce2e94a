-- Create<PERSON><PERSON>
CREATE TYPE "DayOfWeek" AS ENUM ('SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY');

-- CreateEnum
CREATE TYPE "AvailabilityType" AS ENUM ('DAILY_RECURRING', 'WEEKLY_RECURRING', 'DATE_RANGE', 'ONE_TIME');

-- AlterTable
ALTER TABLE "Product" ADD COLUMN     "alwaysAvailable" BOOLEAN NOT NULL DEFAULT true;

-- CreateTable
CREATE TABLE "ProductAvailability" (
    "id" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "type" "AvailabilityType" NOT NULL DEFAULT 'DAILY_RECURRING',
    "dayOfWeek" "DayOfWeek",
    "startTime" TEXT,
    "endTime" TEXT,
    "startDate" TIMESTAMP(3),
    "endDate" TIMESTAMP(3),
    "isAvailable" BOOLEAN NOT NULL DEFAULT true,
    "beforeMessage" TEXT,
    "afterMessage" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3),

    CONSTRAINT "ProductAvailability_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ProductAvailability_productId_type_dayOfWeek_startTime_endT_key" ON "ProductAvailability"("productId", "type", "dayOfWeek", "startTime", "endTime");

-- CreateIndex
CREATE UNIQUE INDEX "ProductAvailability_productId_type_startDate_endDate_key" ON "ProductAvailability"("productId", "type", "startDate", "endDate");

-- AddForeignKey
ALTER TABLE "ProductAvailability" ADD CONSTRAINT "ProductAvailability_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE CASCADE ON UPDATE CASCADE;
