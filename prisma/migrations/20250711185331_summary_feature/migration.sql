-- CreateEnum
CREATE TYPE "SummaryType" AS ENUM ('DAILY', 'WEEKLY', 'MONTHLY', 'YEARLY');

-- AlterTable
ALTER TABLE "Order" ADD COLUMN     "profit" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "summaryId" TEXT;

-- AlterTable
ALTER TABLE "OrderItem" ADD COLUMN     "sourcePrice" INTEGER NOT NULL DEFAULT 0;

-- CreateTable
CREATE TABLE "Summary" (
    "id" TEXT NOT NULL,
    "type" "SummaryType" NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "totalOrders" INTEGER NOT NULL,
    "totalRevenue" INTEGER NOT NULL,
    "totalDeliveryCharges" INTEGER NOT NULL,
    "totalProfit" INTEGER NOT NULL,
    "notes" TEXT,
    "isFinalized" BOOLEAN NOT NULL DEFAULT false,
    "generatedById" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "parentId" TEXT,

    CONSTRAINT "Summary_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Summary_type_startDate_key" ON "Summary"("type", "startDate");

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_summaryId_fkey" FOREIGN KEY ("summaryId") REFERENCES "Summary"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Summary" ADD CONSTRAINT "Summary_generatedById_fkey" FOREIGN KEY ("generatedById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Summary" ADD CONSTRAINT "Summary_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "Summary"("id") ON DELETE SET NULL ON UPDATE CASCADE;
