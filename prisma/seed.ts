import { PrismaClient, Prisma } from "@prisma/client";
import { seedUnit, seedUsers } from "./scripts/seed-unit";
import { seedCategories } from "./scripts/seed-categories";
import { seedProducts } from "./scripts/seed-products";
import { seedZones } from "./scripts/seed-zones";

const prisma = new PrismaClient();

async function main() {
  await Promise.all([
    seedUnit(prisma),
    seedUsers(prisma),
    seedCategories(prisma),
    seedZones(prisma),
  ]);

  await seedProducts(prisma);
}

main();
