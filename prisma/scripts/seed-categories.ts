import { PrismaClient } from "@prisma/client";
import categories from "./data/categories.json";
import { FileManager } from "../../src/libs/backend/image-util";
import { readFile } from "fs/promises";

export async function seedCategories(prisma: PrismaClient) {
  for (let main of categories) {
    const image = await readFile(main.image);
    const imageName = main.image.split("/").at(-1);
    const imageFile = new File([image], imageName!, { type: "image/png" });
    const url = await FileManager.imageUpload(imageFile, `categories`);
    const mainCategory = await prisma.category.create({
      data: {
        name: main.title,
        slug: main.slug,
        nam: main.nam,
        image: url,
        isBase: !Boolean(main.subCategories),
      },
    });

    console.log("Main Category Created ->", mainCategory.name);

    for (let sub of main.subCategories) {
      const image = await readFile(sub.image);
      const imageName = sub.image.split("/").at(-1);
      const imageFile = new File([image], imageName!, { type: "image/jpeg" });
      const url = await FileManager.imageUpload(imageFile, `categories`);
      const subCategory = await prisma.category.create({
        data: {
          name: sub.title,
          slug: sub.slug,
          nam: sub.nam,
          image: url,
          isBase: !Boolean((sub as any).subCategories),
          parentId: mainCategory.id,
        },
      });
      console.log("Sub Category Created ->", subCategory.name);

      for (let subsub of (sub as any)?.subCategories || []) {
        const image = await readFile(subsub.image);
        const imageName = subsub.image.split("/").at(-1);
        const imageFile = new File([image], imageName!, { type: "image/jpeg" });
        const url = await FileManager.imageUpload(
          imageFile,
          `categories/${imageName}`
        );
        await prisma.category.create({
          data: {
            name: subsub.title,
            slug: subsub.slug,
            nam: subsub.nam,
            image: url,
            parentId: subCategory.id,
            isBase: true,
          },
        });

        console.log("SubSub Category Created ->", subsub.title);
      }
    }
  }
}
