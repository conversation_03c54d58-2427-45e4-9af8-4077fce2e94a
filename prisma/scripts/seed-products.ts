import { PrismaClient } from "@prisma/client";
import data from "./data/products.json";
import { UnitUtil } from "@udoy/utils/product-unit";
import { readFile } from "fs/promises";
import { basename } from "path";
import { FileManager } from "../../src/libs/backend/image-util";

function predictUnit(name: string, nam: string) {
  if (nam.includes(" টি") || nam.includes(" পিস")) {
    return "PCS";
  }

  if (name.includes("ml") || nam.includes(" মিলি") || nam.includes(" লিটার")) {
    return "ML";
  }

  if (name.includes(" kg") || nam.includes(" কেজি") || nam.includes(" গ্রাম")) {
    return "GM";
  }

  return "PCS";
}

interface ProductMeasurement {
  amount: number;
  unit: string;
  quantity: number;
}

interface UnitDefinition {
  full: string;
  slug: string;
  bangla: string;
}

const units: UnitDefinition[] = [
  { full: "Gram", slug: "GM", bangla: "গ্রাম" },
  { full: "Kilogram", slug: "KG", bangla: "কেজি" },
  { full: "Liter", slug: "L<PERSON>", bangla: "লিটার" },
  { full: "Milliliter", slug: "ML", bangla: "মিলি" },
  { full: "Pieces", slug: "PCS", bangla: "টি" },
];

const bengaliDigitMap: { [key: string]: number } = {
  "০": 0,
  "১": 1,
  "২": 2,
  "৩": 3,
  "৪": 4,
  "৫": 5,
  "৬": 6,
  "৭": 7,
  "৮": 8,
  "৯": 9,
};

function bengaliToEnglishNumber(bengaliNumber: string): number {
  // Only remove ± symbols, not decimal points or zeros
  const cleaned = bengaliNumber.replace(/[±]/g, "");
  return parseInt(
    cleaned
      .split("")
      .map((c) => (bengaliDigitMap[c] !== undefined ? bengaliDigitMap[c] : c))
      .join(""),
    10
  );
}

function parseMeasurement(text: string): ProductMeasurement {
  const result: ProductMeasurement = {
    amount: 1,
    unit: "PCS",
    quantity: 1,
  };

  // Parse amount and unit (excluding pieces)
  const measurementUnits = units.filter((u) => u.slug !== "PCS");
  const amountRegex = new RegExp(
    `(\\d+|[\u09E6-\u09EF]+)\\s*(${measurementUnits
      .map((u) => u.bangla)
      .join("|")})`,
    "g"
  );

  // Find all valid measurements ignoring ± cases
  const validMeasurements = Array.from(text.matchAll(amountRegex)).filter(
    (match) => {
      const precedingText = text.slice(0, match.index).trim();
      return !precedingText.endsWith("±");
    }
  );

  if (validMeasurements.length > 0) {
    const lastMatch = validMeasurements[validMeasurements.length - 1];
    const unit = measurementUnits.find((u) => u.bangla === lastMatch[2]);
    if (unit) {
      result.amount = bengaliToEnglishNumber(lastMatch[1]);
      result.unit = unit.slug;
    }
  }

  // Parse quantity (pieces)
  const quantityRegex = /(\d+|[\u09E6-\u09EF]+)\s*(টি|প্যাক)/g;
  const quantityMatches = Array.from(text.matchAll(quantityRegex));
  if (quantityMatches.length > 0) {
    result.quantity = bengaliToEnglishNumber(
      quantityMatches[quantityMatches.length - 1][1]
    );
  }

  // Handle default cases
  if (result.unit === "PCS" && result.amount === 1) {
    result.amount = result.quantity;
  }

  return result;
}

export async function seedProducts(prisma: PrismaClient) {
  for (const category in data) {
    const products = data[category as keyof typeof data];

    for (const product of products) {
      const measurement = parseMeasurement(product.nam);
      try {
        const image = await readFile(product.image);
        const imageName = basename(product.image);
        const imageFile = new File([image], imageName!, { type: "image/jpeg" });
        const url = await FileManager.imageUpload(imageFile, `products`);
        const result = await prisma.product.create({
          data: {
            name: product.name,
            description: "",
            price: product.price,
            sourcePrice: product.price,
            nam: product.nam,
            images: {
              create: {
                url,
              },
            },
            amount: measurement.amount,
            quantity: measurement.quantity,
            supply: 100,
            discount: 0,
            unit: {
              connect: {
                slug: measurement.unit,
              },
            },
            category: {
              connect: {
                slug: category,
              },
            },
          },
        });

        console.log("Product Created ->", result?.name);
      } catch (err: any) {
        console.log(`Failed to create product: ${product.name}`, err);
      }
    }
  }
}
