import { PrismaClient } from "@prisma/client";

export async function seedUnit(prisma: PrismaClient) {
  const units = await prisma.quantityUnit.createMany({
    data: [
      { full: "Gram", slug: "GM", bangla: "গ্রাম" },
      { full: "Kilogram", slug: "KG", bangla: "কেজি" },
      { full: "Liter", slug: "LTR", bangla: "লিটার" },
      { full: "Pieces", slug: "PCS", bangla: "টি" },
      { full: "Milliliter", slug: "ML", bangla: "মিলি" },
    ],
    skipDuplicates: true,
  });

  console.log("Units Seeded!");

  return units;
}

export async function seedUsers(prisma: PrismaClient) {
  const users = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {
      name: "<PERSON><PERSON><PERSON> <PERSON>",
      email: "<EMAIL>",
      role: "SUPER_ADMIN",
    },
    create: {
      name: "<PERSON><PERSON><PERSON>",
      email: "<EMAIL>",
      role: "SUPER_ADMIN",
      cart: {
        create: {},
      },
    },
  });

  console.log("Admin Seeded!");

  return users;
}
