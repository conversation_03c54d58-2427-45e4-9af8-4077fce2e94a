import { ActionErrorType, isActionError } from "@udoy/utils/app-error";
import { useActionState } from "react";

export interface FormOptions<T> {
  default?: T;
  onError?(error: { message: string }): void;
  onStart?(): void;
  onComplete?(data: T): T;
  modifier?(form: FormData): FormData;
}

function useForm<T>(
  action: (state: T, data: FormData) => Promise<T | ActionErrorType>,
  options?: FormOptions<T>
) {
  return useActionState<T, FormData>(async (state: T, form: FormData) => {
    options?.onStart?.();
   
    const modified = options?.modifier?.(form) || form;
    const result = await action(state, modified);

    if (isActionError(result)) {
      options?.onError?.(result.error);

      return state;
    }
    const data = await options?.onComplete?.(result);

    return data ?? result ?? state;
  }, options?.default as Awaited<T>);
}

export default useForm;
