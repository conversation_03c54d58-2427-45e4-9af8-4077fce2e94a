import { useRef, useState } from "react";

function useControlledState<T>(state?: T, setState?: (value: T) => void) {
  const isControlled = useRef(setState !== undefined);
  const [localState, setLocalState] = useState(state);

  function handleUpdate(value: T) {
    if (isControlled.current) {
      setState?.(value);
    } else {
      setLocalState(value);
    }
  }

  if (isControlled.current) {
    return [state, handleUpdate] as const;
  } else {
    return [localState, handleUpdate] as const;
  }
}

export { useControlledState };
