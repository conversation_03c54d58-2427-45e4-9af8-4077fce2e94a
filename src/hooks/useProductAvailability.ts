import {
  Product,
  ProductAvailability,
  AvailabilityType,
  DayOfWeek,
} from "@prisma/client";
import { useMemo } from "react";

function useProductAvailability(
  product: Product & { availability: ProductAvailability[] }
) {
  const { isAvailable, message, isBefore, title } = useMemo(() => {
    const now = new Date();
    const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const currentTimeInMinutes = now.getHours() * 60 + now.getMinutes();

    // Helper function to convert "HH:MM" string to minutes since midnight
    const timeStringToMinutes = (timeString: string): number => {
      const [hours, minutes] = timeString.split(":").map(Number);
      return hours * 60 + minutes;
    };

    // Map JavaScript day numbers to DayOfWeek enum
    const dayOfWeekMap: Record<number, DayOfWeek> = {
      0: DayOfWeek.SUNDAY,
      1: DayOfWeek.MONDAY,
      2: DayOfWeek.TUESDAY,
      3: DayOfWeek.WEDNESDAY,
      4: DayOfWeek.THURSDAY,
      5: DayOfWeek.FRIDAY,
      6: DayOfWeek.SATURDAY,
    };

    const currentDayOfWeek = dayOfWeekMap[currentDay];

    // Start with the product's default availability
    let productIsAvailable = product.alwaysAvailable;
    let availabilityMessage =
      "দুঃখিত পণ্যটি এই মুহূর্তে এভেলেবেল নেই পরবর্তীতে আবার চেষ্টা করে দেখুন";
    let isBeforeAvailable = false;
    let availabilityTitle = "পন্যটি বর্তমানে এবেলএবল নেই ।";

    // Sort schedules by priority: higher priority numbers take precedence
    const sortedSchedules = [...product.availability].sort((a, b) => {
      return (b.priority || 1) - (a.priority || 1);
    });

    let matchingSchedule: ProductAvailability | null = null;
    let scheduleTimeMatches = false;

    // Check all availability schedules to find the most specific one that applies
    for (const schedule of sortedSchedules) {
      let scheduleApplies = false;

      switch (schedule.type) {
        case AvailabilityType.DAILY_RECURRING:
          // Applies every day
          scheduleApplies = true;
          break;

        case AvailabilityType.WEEKLY_RECURRING:
          // Applies only on specific day of week
          scheduleApplies = schedule.dayOfWeek === currentDayOfWeek;
          break;

        case AvailabilityType.DATE_RANGE:
          // Applies within date range
          if (schedule.startDate && schedule.endDate) {
            const startDate = new Date(schedule.startDate);
            const endDate = new Date(schedule.endDate);
            // Set time to start/end of day for proper comparison
            startDate.setHours(0, 0, 0, 0);
            endDate.setHours(23, 59, 59, 999);
            scheduleApplies = now >= startDate && now <= endDate;
          }
          break;

        case AvailabilityType.ONE_TIME:
          // Applies only on specific date
          if (schedule.startDate) {
            const scheduleDate = new Date(schedule.startDate);
            const todayStart = new Date(now);
            todayStart.setHours(0, 0, 0, 0);
            const todayEnd = new Date(now);
            todayEnd.setHours(23, 59, 59, 999);
            scheduleApplies =
              scheduleDate >= todayStart && scheduleDate <= todayEnd;
          }
          break;
      }

      // If this schedule applies, check time constraints
      if (scheduleApplies) {
        let timeMatches = true;

        // Check time constraints if specified
        if (schedule.startTime && schedule.endTime) {
          const startTimeInMinutes = timeStringToMinutes(schedule.startTime);
          const endTimeInMinutes = timeStringToMinutes(schedule.endTime);
          timeMatches =
            currentTimeInMinutes >= startTimeInMinutes &&
            currentTimeInMinutes <= endTimeInMinutes;
        }

        // Store the most specific matching schedule
        if (!matchingSchedule) {
          matchingSchedule = schedule;
          scheduleTimeMatches = timeMatches;
        }
      }
    }

    // Apply the matching schedule if found
    if (matchingSchedule) {
      if (scheduleTimeMatches) {
        // This schedule overrides the default availability
        productIsAvailable = matchingSchedule.isAvailable;

        // Set custom message when product is unavailable
        if (!matchingSchedule.isAvailable) {
          // Use beforeMessage or afterMessage if available, otherwise use default
          if (matchingSchedule.beforeMessage) {
            availabilityMessage = matchingSchedule.beforeMessage;
          } else if (matchingSchedule.afterMessage) {
            availabilityMessage = matchingSchedule.afterMessage;
          } else {
            availabilityMessage = "This product is currently unavailable";
          }
        }
      } else {
        // Schedule applies but we're outside the time range
        if (matchingSchedule.startTime && matchingSchedule.endTime) {
          const startTimeInMinutes = timeStringToMinutes(matchingSchedule.startTime);
          const endTimeInMinutes = timeStringToMinutes(matchingSchedule.endTime);

          if (matchingSchedule.isAvailable) {
            // This is an availability schedule (makes product available during specific time)
            if (
              currentTimeInMinutes < startTimeInMinutes &&
              matchingSchedule.beforeMessage
            ) {
              // We're before the available period starts (product is unavailable, show beforeMessage)
              availabilityMessage = matchingSchedule.beforeMessage;
              isBeforeAvailable = true;
              availabilityTitle = "পন্যটি একটু পর থেকে পাওয়া যাবে ।";
            } else if (
              currentTimeInMinutes > endTimeInMinutes &&
              matchingSchedule.afterMessage
            ) {
              // We're after the available period ends (product is unavailable again, show afterMessage)
              availabilityMessage = matchingSchedule.afterMessage;
              availabilityTitle = "আজকের মত পন্যটি শেষ ।";
            }
          } else {
            // This is an unavailability schedule (makes product unavailable during specific time)
            if (
              currentTimeInMinutes < startTimeInMinutes &&
              matchingSchedule.beforeMessage
            ) {
              // We're before the unavailable period starts
              availabilityMessage = matchingSchedule.beforeMessage;
            } else if (
              currentTimeInMinutes > endTimeInMinutes &&
              matchingSchedule.afterMessage
            ) {
              // We're after the unavailable period ends
              availabilityMessage = matchingSchedule.afterMessage;
            }
          }
        }
      }
    }

    // Default message if product is unavailable and no specific message is set
    if (!productIsAvailable && !availabilityMessage) {
      availabilityMessage = "";
    }

    return {
      isAvailable: productIsAvailable,
      message: availabilityMessage,
      isBefore: isBeforeAvailable,
      title: availabilityTitle,
    };
  }, [product.alwaysAvailable, product.availability]);

  return { isAvailable, message, isBefore, title };
}

export default useProductAvailability;
