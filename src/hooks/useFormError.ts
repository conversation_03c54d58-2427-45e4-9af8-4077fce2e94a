import { ActionErrorType, isActionError } from "@udoy/utils/app-error";
import { useFormState } from "react-dom";
import useForm, { FormOptions } from "./useForm";
import useStatus from "./useToastUtil";

export interface ErrorMessages {
  success: string;
  loading: string;
  failed: string;
}

function useFormError<T>(
  action: (state: T, data: FormData) => Promise<T | ActionErrorType>,
  options?: FormOptions<T> & {
    messages?: ErrorMessages;
  }
) {
  const status = useStatus();
  return useForm(action, {
    default: options?.default,
    onComplete(data) {
      status.success(options?.messages?.success || "Success");
      return options?.onComplete?.(data) ?? data;
    },
    onError(error) {
      options?.onError?.(error);
      status.error(error.message || options?.messages?.failed!);
    },
    onStart() {
      options?.onStart?.();
      status.loading(options?.messages?.loading || "Submitting...");
    },
    modifier: options?.modifier,
  });
}

export default useFormError;
