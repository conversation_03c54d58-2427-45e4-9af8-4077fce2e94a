import { useId } from "react";
import { ExternalToast, toast } from "sonner";

function useStatus(config: ExternalToast = {}) {
  const toastId = useId();

  const loading = (msg: string) => {
    toast.loading(msg, {
      ...config,
      id: toastId,
    });
  };

  const success = (msg: string) => {
    toast.success(msg, {
      ...config,
      id: toastId,
    });
  };

  const error = (msg: string) => {
    toast.error(msg, {
      ...config,
      id: toastId,
    });
  };

  return { loading, success, error, id: toastId };
}

export default useStatus;
