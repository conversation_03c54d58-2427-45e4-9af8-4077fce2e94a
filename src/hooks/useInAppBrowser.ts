"use client";

import { useState, useEffect } from "react";

export const useInAppBrowser = () => {
  const [isFacebookBrowser, setIsFacebookBrowser] = useState(false);

  useEffect(() => {
    const userAgent =
      navigator.userAgent || navigator.vendor || (window as any).opera;
    // Detect Facebook in-app browser
    const isFb =
      userAgent.indexOf("FBAN") > -1 || userAgent.indexOf("FBAV") > -1;
    setIsFacebookBrowser(isFb);
  }, []);

  return { isFacebookBrowser };
};
