import { useRouter } from "next/navigation";
import React from "react";
import { toast } from "sonner";

function useLogOut() {
  const router = useRouter();
  function handleLogout() {
    const names = ["token", "user","cartId"];

    for (const name of names) {
      document.cookie =
        name + "=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    }

    router.refresh();
    toast.success("Logged out successfully");
  }

  return handleLogout;
}

export default useLogOut;
