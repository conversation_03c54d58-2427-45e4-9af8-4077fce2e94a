import { useEffect, useState } from "react";

function getCurrentHour() {
  const now = new Date();
  return now.getHours();
}

function useInOperation() {
  const [currentHour, setCurrentHour] = useState(getCurrentHour());

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentHour(getCurrentHour());
    }, 60000);

    return () => clearInterval(interval);
  }, []);

  // Case 1: Open Hours (8:00 AM - 5:59 PM)
  if (currentHour >= 8 && currentHour < 19) {
    return {
      isInOperation: true,
      message: null,
    };
  }

  // Case 2: Closed for the Day (7:00 PM - 11:59 PM)
  if (currentHour >= 18 && currentHour <= 23) {
    return {
      isInOperation: false,
      message: {
        title: "দুঃখিত, আজকের মত আমাদের কার্যক্রম শেষ ।",
        description:
          "আগামীকাল সকাল ৮টা থেকে আমাদের কার্যক্রম পুনরায় শুরু হবে। আপনার অর্ডারটি প্লেস করতে পারেন, আগামিকাল আমাদের কার্যক্রম শুরু হওয়ার পর আপনার অর্ডারটি ডেলিভারি করা হবে, উদয়মার্ট এর সাথে-ই থাকুন ধন্যবাদ!",
      },
    };
  }

  // Case 3: Not Yet Open (12:00 AM - 7:59 AM)
  if (currentHour >= 0 && currentHour < 8) {
    return {
      isInOperation: false,
      message: {
        title: "আমাদের কার্যক্রম এখনও শুরু হয়নি ।",
        description:
          "আমাদের কার্যক্রম সকাল ৮টা থেকে শুরু হবে । আপনার অর্ডারটি প্লেস করতে পারেন আমাদের কার্যক্রম শুরু হওয়ার পর আপনার অর্ডারটি ডেলিভারি করা হবে, উদয়মার্ট এর সাথে-ই থাকুন ধন্যবাদ",
      },
    };
  }

  // Fallback (should not be reached with the logic above)
  return {
    isInOperation: false,
    message: {
      title: "দোকান বন্ধ আছে",
      description: "আমাদের কার্যক্রম সকাল ৮টা থেকে সন্ধ্যা ৬টা পর্যন্ত।",
    },
  };
}

export default useInOperation;
