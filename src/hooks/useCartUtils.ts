import { Product } from "@prisma/client";
import { manageCart } from "@udoy/actions/cart";
import { store } from "@udoy/state";
import { withError } from "@udoy/utils/app-error";
import { toast } from "sonner";

function useCartUtils(cachedProduct: Product) {
  async function handleAddToCart() {
    const snap = store.getSnapshot().context;
    const { count, product } = snap.cartItems[cachedProduct.id] || {};
    const supply = product?.supply || cachedProduct.supply;
    const itemCount = count || 0;

    if (itemCount >= supply) {
      return toast.error("পন্যটি আর অবশিষ্ট নেই");
    }

    store.send({ type: "addToCart", product: cachedProduct });

    try {
      await withError(manageCart(cachedProduct.id, "add"));
    } catch (error: any) {
      store.send({ type: "removeFromCart", productId: cachedProduct.id });
    }
  }
  async function handleRemoveFromCart() {
    store.send({ type: "removeFromCart", productId: cachedProduct.id });

    try {
      await withError(manageCart(cachedProduct.id, "remove"));
    } catch (error: any) {
      store.send({ type: "addToCart", product: cachedProduct });
    }
  }

  return {
    handleAddToCart,
    handleRemoveFromCart,
  };
}

export default useCartUtils;
