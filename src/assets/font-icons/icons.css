@font-face {
    font-family: "icons";
    src: url("./icons.eot?2b73895fa3a16ca2be96012df0f44998#iefix") format("embedded-opentype"),
url("./icons.woff2?2b73895fa3a16ca2be96012df0f44998") format("woff2"),
url("./icons.woff?2b73895fa3a16ca2be96012df0f44998") format("woff");
}

i[class^="icon-"]:before, i[class*=" icon-"]:before {
    font-family: icons !important;
    font-style: normal;
    font-weight: normal !important;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-cart:before {
    content: "\f101";
}
.icon-check2-circle:before {
    content: "\f102";
}
.icon-chevron-down:before {
    content: "\f103";
}
.icon-chevron-left:before {
    content: "\f104";
}
.icon-chevron-right:before {
    content: "\f105";
}
.icon-close:before {
    content: "\f106";
}
.icon-edit:before {
    content: "\f107";
}
.icon-image:before {
    content: "\f108";
}
.icon-logo:before {
    content: "\f109";
}
.icon-plus-circle:before {
    content: "\f10a";
}
