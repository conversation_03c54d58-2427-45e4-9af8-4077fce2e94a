export type IconsId =
  | "cart"
  | "check2-circle"
  | "chevron-down"
  | "chevron-left"
  | "chevron-right"
  | "close"
  | "edit"
  | "image"
  | "logo"
  | "plus-circle";

export type IconsKey =
  | "Cart"
  | "Check2Circle"
  | "ChevronDown"
  | "ChevronLeft"
  | "ChevronRight"
  | "Close"
  | "Edit"
  | "Image"
  | "Logo"
  | "PlusCircle";

export enum Icons {
  Cart = "cart",
  Check2Circle = "check2-circle",
  ChevronDown = "chevron-down",
  ChevronLeft = "chevron-left",
  ChevronRight = "chevron-right",
  Close = "close",
  Edit = "edit",
  Image = "image",
  Logo = "logo",
  PlusCircle = "plus-circle",
}

export const ICONS_CODEPOINTS: { [key in Icons]: string } = {
  [Icons.Cart]: "61697",
  [Icons.Check2Circle]: "61698",
  [Icons.ChevronDown]: "61699",
  [Icons.ChevronLeft]: "61700",
  [Icons.ChevronRight]: "61701",
  [Icons.Close]: "61702",
  [Icons.Edit]: "61703",
  [Icons.Image]: "61704",
  [Icons.Logo]: "61705",
  [Icons.PlusCircle]: "61706",
};
