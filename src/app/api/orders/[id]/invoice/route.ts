import { NextRequest, NextResponse } from "next/server";
import { getPrisma } from "@udoy/utils/db-utils";
import PDFDocument from "pdfkit";
import fs from "fs";
import path from "path";
import { OrderItemWithProduct } from "@udoy/utils/types";
import { Prisma } from "@prisma/client";

// Define font names for easy switching
const FONT_REGULAR = "Roboto";
const NOTO_SERIF = "NOTO_SERIF"; // Assuming you might add a bold variant later
const NOTO_SANS = "BanglaFont"; // Choose a name for your Bangla font

type Order = Prisma.OrderGetPayload<{
  include: {
    orderItems: {
      include: {
        product: {
          include: {
            images: true;
          };
        };
      };
    };
    address: true;
    buyer: true;
  };
}>;

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    if (Number.isNaN(parseInt(id))) {
      return NextResponse.json({ error: "Invalid Order ID" }, { status: 400 });
    }

    // ... (rest of your initial checks and data fetching) ...
    const order = await getPrisma().order.findUnique({
      where: {
        id: parseInt(id),
      },
      include: {
        orderItems: {
          include: {
            product: {
              include: {
                images: true,
              },
            },
          },
        },
        address: true,
        buyer: true,
      },
    });
    if (!order) {
      return NextResponse.json({ error: "Order Not Found" }, { status: 404 });
    }

    // --- Font Loading ---
    const loadFont = (fontFileName: string): Buffer | null => {
      const fontPath = path.join(
        process.cwd(),
        "src",
        "assets",
        "fonts",
        fontFileName
      );
      try {
        return fs.readFileSync(fontPath);
      } catch (fontError) {
        console.error(`Error reading font file ${fontFileName}:`, fontError);
        return null; // Return null on error
      }
    };

    // const robotoRegularBuffer = loadFont("Roboto-Regular.ttf");
    // Example: Load bold if needed later
    // const robotoBoldBuffer = loadFont("Roboto-Bold.ttf");
    const banglaFontBuffer = loadFont("NotoSansBengali-Regular.ttf"); // <-- Load your Bangla font file
    const notoSerifBuffer = loadFont("NotoSerifBengali-Regular.ttf");

    if (!banglaFontBuffer || !notoSerifBuffer) {
      // Check if essential fonts loaded
      return NextResponse.json(
        { error: "Failed to load required fonts for invoice generation" },
        { status: 500 }
      );
    }
    // --- End Font Loading ---

    // Create a PDF document
    const doc = new PDFDocument({ margin: 50 });

    // --- Register Fonts ---
    // doc.registerFont(FONT_REGULAR, robotoRegularBuffer);
    // if (robotoBoldBuffer) doc.registerFont(FONT_BOLD, robotoBoldBuffer);
    doc.registerFont(NOTO_SANS, banglaFontBuffer); // <-- Register Bangla font
    doc.registerFont(NOTO_SERIF, notoSerifBuffer);
    // --- End Register Fonts ---

    // --- Set Default Font ---
    doc.font(NOTO_SERIF); // Set Roboto as the default
    // ---

    // ... (rest of your PDF generation setup: headers, chunks, etc.) ...
    const headers = new Headers();
    headers.set("Content-Type", "application/pdf");
    headers.set(
      "Content-Disposition",
      `attachment; filename="invoice-${order.id}.pdf"`
    );
    const chunks: Buffer[] = [];
    doc.on("data", (chunk) => chunks.push(chunk));

    // Generate PDF content (passing the font names if needed, or just use them inside)
    generateInvoice(doc, order);

    // Finalize the PDF
    doc.end();

    // Return PDF as response
    return new Promise<NextResponse>((resolve) => {
      // ... (promise logic with resolve and error handling) ...
      doc.on("end", () => {
        const pdfBuffer = Buffer.concat(chunks as any);
        resolve(
          new NextResponse(pdfBuffer, {
            status: 200,
            headers,
          })
        );
      });
      doc.on("error", (err) => {
        /* ... stream error handling ... */
      });
    });
  } catch (error) {
    // ... (your main catch block) ...
    console.error("Error generating invoice:", error);
    return NextResponse.json(
      { error: "Failed to generate invoice" },
      { status: 500 }
    );
  }
}

function bnNum(num: number) {
  return num.toLocaleString("bn-BD");
}

function generateInvoice(doc: PDFKit.PDFDocument, order: Order) {
  // Initial setup - track current y position
  let currentY = 45;
  const pageHeight = doc.page.height;
  const margin = 50;
  const contentHeight = pageHeight - margin * 2;

  // Function to check if we need a new page
  const checkForPageBreak = (requiredHeight: number) => {
    if (currentY + requiredHeight > pageHeight - margin) {
      doc.addPage();
      currentY = margin;
      return true;
    }
    return false;
  };

  // Function to draw table headers
  const drawTableHeaders = () => {
    // Calculate table dimensions
    const pageWidth = doc.page.width;
    const tableWidth = pageWidth - margin * 2;

    // Calculate column positions
    const noX = margin;
    const itemX = margin + tableWidth * 0.05;
    const priceX = margin + tableWidth * 0.55;
    const discountedX = margin + tableWidth * 0.6;
    const quantityX = margin + tableWidth * 0.75;
    const amountX = margin + tableWidth * 0.9;

    // Draw table header
    doc.strokeColor("#cccccc");
    doc.rect(noX, currentY, tableWidth, 25).stroke();

    // Table headers
    doc.font(NOTO_SANS).fontSize(10);
    doc.fillColor("#000000");
    doc.text("নং", noX + 5, currentY + 5);
    doc.text("পণ্য", itemX, currentY + 5);
    doc.text("মূল্য", priceX, currentY + 5);
    // doc.text("ছাড়", discountedX, currentY + 5);
    doc.text("পরিমাণ", quantityX, currentY + 5);
    doc.text("মোট", amountX, currentY + 5);

    // Draw vertical lines for columns
    doc
      .moveTo(itemX - 5, currentY)
      .lineTo(itemX - 5, currentY + 25)
      .stroke();
    doc
      .moveTo(priceX - 5, currentY)
      .lineTo(priceX - 5, currentY + 25)
      .stroke();
    // doc
    //   .moveTo(discountedX - 5, currentY)
    //   .lineTo(discountedX - 5, currentY + 25)
    //   .stroke();
    doc
      .moveTo(quantityX - 5, currentY)
      .lineTo(quantityX - 5, currentY + 25)
      .stroke();
    doc
      .moveTo(amountX - 5, currentY)
      .lineTo(amountX - 5, currentY + 25)
      .stroke();

    currentY += 25;

    return { noX, itemX, priceX, discountedX, quantityX, amountX, tableWidth };
  };

  // --- Logo and Header Section ---
  // Logo on left side
  const logoPath = path.join(
    process.cwd(),
    "src",
    "assets",
    "images",
    "logo-dark.png"
  );
  try {
    doc.image(logoPath, 50, currentY, { width: 100 });
    doc.fontSize(8).text("উদয় মার্ট", 118, currentY - 2.5);
  } catch (error) {
    console.error("Logo not found:", error);
  }

  // Order info on right side
  doc
    .font(NOTO_SANS)
    .fontSize(16)
    .text(`চালান #${bnNum(order.id)}`, 350, currentY, { align: "right" })
    .fontSize(10)
    .text(
      `তারিখ: ${new Date(order.createdAt).toLocaleDateString("bn-BD", {
        year: "numeric",
        month: "long",
        day: "numeric",
      })}`,
      350,
      null as any,
      { align: "right" }
    );

  currentY += 80; // Move down after header

  // --- Customer Information ---
  doc
    .fontSize(14)
    .text("কাষ্টমারের তথ্য", 50, currentY, { underline: true })
    .fontSize(10);
  currentY += 25;
  doc.font(NOTO_SANS);
  doc.text(`নাম: ${order.address.name}`, 50, currentY);
  currentY += 15;
  doc.text(`ই-মেইল: ${order.buyer.email}`, 50, currentY);
  currentY += 15;
  doc.text(`মোবাইল: ${order.address.phone}`, 50, currentY);
  currentY += 15;
  doc.text(`ঠিকানা: ${order.address.home}`, 50, currentY);
  currentY += 30;

  // Check if we need a page break before starting the table
  checkForPageBreak(50);

  // Draw table headers
  const { noX, itemX, priceX, discountedX, quantityX, amountX, tableWidth } =
    drawTableHeaders();

  // --- Table Rows ---
  order.orderItems.forEach((item, index: number) => {
    // Calculate row height based on product name length
    const nameWidth = priceX - itemX - 10;
    doc.font(NOTO_SANS);

    // Get product name safely
    const productName = item.product.nam || item.product.name || "Product";

    // Calculate height needed for this row
    let nameHeight = 0;
    try {
      nameHeight = doc.heightOfString(productName, { width: nameWidth });
    } catch (error) {
      doc.font(NOTO_SERIF);
      nameHeight = doc.heightOfString(productName, { width: nameWidth });
      doc.font(NOTO_SANS);
    }
    const rowHeight = Math.max(20, nameHeight + 10);

    // Check if we need a page break
    if (checkForPageBreak(rowHeight + 10)) {
      // If we added a new page, redraw the headers
      drawTableHeaders();
    }

    // Draw row border with dynamic height
    doc.rect(noX, currentY, tableWidth, rowHeight).stroke();

    // Draw vertical lines for columns with dynamic height
    doc
      .moveTo(itemX - 5, currentY)
      .lineTo(itemX - 5, currentY + rowHeight)
      .stroke();
    doc
      .moveTo(priceX - 5, currentY)
      .lineTo(priceX - 5, currentY + rowHeight)
      .stroke();
    // doc
    //   .moveTo(discountedX - 5, currentY)
    //   .lineTo(discountedX - 5, currentY + rowHeight)
    //   .stroke();
    doc
      .moveTo(quantityX - 5, currentY)
      .lineTo(quantityX - 5, currentY + rowHeight)
      .stroke();
    doc
      .moveTo(amountX - 5, currentY)
      .lineTo(amountX - 5, currentY + rowHeight)
      .stroke();

    // Item number
    doc.fillColor("#000000");
    doc.text(bnNum(index + 1), noX + 5, currentY + 5);

    // Product name (potentially in Bangla) - with wrapping
    try {
      doc.font(NOTO_SANS);
      doc.text(productName, itemX, currentY + 5, {
        width: nameWidth,
        align: "left",
      });
    } catch (error) {
      doc.font(NOTO_SERIF);
      doc.text(productName, itemX, currentY + 5);
      doc.font(NOTO_SANS);
    }

    // Price and calculations - align vertically in the middle of the row
    const verticalCenter = currentY + rowHeight / 2 - 5;
    doc.text(bnNum(item.price), priceX, verticalCenter);

    // Discounted price (if available)
    // const discountedPrice = item.product.discount || 0;
    // doc.text(bnNum(discountedPrice), discountedX + 6, verticalCenter);
    doc.text(bnNum(item.quantity), quantityX, verticalCenter);
    doc.text(bnNum(item.price * item.quantity), amountX, verticalCenter);

    currentY += rowHeight;
  });

  // --- Totals ---
  currentY += 10;

  // Check if we need a page break before totals
  if (checkForPageBreak(100)) {
    // If we added a new page for totals, add some space at the top
    currentY += 20;
  }

  // Subtotal
  doc.text("ডিসকাউন্ট:", discountedX, currentY);
  doc.text(`৳ ${bnNum(order.discount || 0)}`, amountX, currentY);
  
  
  // Shipping
  currentY += 20;
  doc.text("সাব-টোটাল:", discountedX, currentY);
  doc.text("৳ " + bnNum(order.subTotal), amountX, currentY);
  // Discount (if available)
  currentY += 15;
  doc.text("ডেলিভারি ফি:", discountedX, currentY);
  doc.text(`৳ ${bnNum(order.shipping)}`, amountX, currentY);

  // Line before total
  currentY += 20;
  doc.strokeColor("#999999");
  doc
    .moveTo(discountedX - 20, currentY)
    .lineTo(doc.page.width - margin, currentY)
    .stroke();

  // Total
  currentY += 15;
  doc.fontSize(12);
  doc.text("মোট:", discountedX, currentY);
  doc.text(`৳ ${bnNum(order.subTotal + order.shipping)}`, amountX, currentY);

  // Reset stroke color
  doc.strokeColor("#000000");

  // --- Footer ---
  // Always put footer at the bottom of the last page
  const pageBottom = doc.page.height - margin;
  doc.font(NOTO_SERIF).fontSize(10);
  doc.text(
    "উদয়মার্ট থেকে কেনা-কাটার জন্য ধন্যবাদ! সাশ্রয়ী দামে পন্য কিনতে আমাদের সাথেই থাকুন!",
    50,
    pageBottom - 20,
    {
      align: "center",
    }
  );
}
