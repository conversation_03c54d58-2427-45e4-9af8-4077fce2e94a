import { NextRequest, NextResponse } from "next/server";
import { getPrisma } from "@udoy/utils/db-utils";

export async function POST(request: NextRequest) {
  try {
    const { notificationId } = await request.json();
    
    if (!notificationId) {
      return NextResponse.json(
        { success: false, error: "Notification ID is required" },
        { status: 400 }
      );
    }
    
    const prisma = getPrisma();
    
    // Update the notification as clicked
    await prisma.notification.update({
      where: { id: notificationId },
      data: {
        isClicked: true,
        clickedAt: new Date(),
      },
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error tracking notification click:", error);
    return NextResponse.json(
      { success: false, error: "Failed to track notification click" },
      { status: 500 }
    );
  }
}