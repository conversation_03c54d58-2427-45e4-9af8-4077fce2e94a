import { NextRequest, NextResponse } from "next/server";
import { getPrisma } from "@udoy/utils/db-utils";
import { CookieUtil } from "@udoy/utils/cookie-util";
import { Role, SummaryType } from "@prisma/client";
import { 
  startOfDay, 
  endOfDay, 
  startOfWeek, 
  endOfWeek, 
  startOfMonth, 
  endOfMonth, 
  startOfYear, 
  endOfYear,
  subDays,
  subWeeks,
  subMonths,
  subYears,
  format
} from "date-fns";

export async function GET(request: NextRequest) {
  try {
    const userId = await CookieUtil.userId();
    
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const prisma = getPrisma();
    
    // Check if user has permission
    const user = await prisma.user.findUnique({
      where: {
        id: userId,
        role: { in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER] },
      },
    });

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get("timeRange") || "monthly";

    let summaryType: SummaryType;
    let periods: { start: Date; end: Date; name: string }[] = [];
    const now = new Date();

    switch (timeRange) {
      case "daily":
        summaryType = SummaryType.DAILY;
        // Get last 30 days
        for (let i = 29; i >= 0; i--) {
          const date = subDays(now, i);
          periods.push({
            start: startOfDay(date),
            end: endOfDay(date),
            name: format(date, "MMM d"),
          });
        }
        break;

      case "weekly":
        summaryType = SummaryType.WEEKLY;
        // Get last 12 weeks
        for (let i = 11; i >= 0; i--) {
          const date = subWeeks(now, i);
          periods.push({
            start: startOfWeek(date),
            end: endOfWeek(date),
            name: format(date, "MMM d"),
          });
        }
        break;

      case "yearly":
        summaryType = SummaryType.YEARLY;
        // Get last 5 years
        for (let i = 4; i >= 0; i--) {
          const date = subYears(now, i);
          periods.push({
            start: startOfYear(date),
            end: endOfYear(date),
            name: format(date, "yyyy"),
          });
        }
        break;

      case "monthly":
      default:
        summaryType = SummaryType.MONTHLY;
        // Get last 12 months
        for (let i = 11; i >= 0; i--) {
          const date = subMonths(now, i);
          periods.push({
            start: startOfMonth(date),
            end: endOfMonth(date),
            name: format(date, "MMM yyyy"),
          });
        }
        break;
    }

    // Fetch summaries for each period
    const data = await Promise.all(
      periods.map(async (period) => {
        const summaries = await prisma.summary.findMany({
          where: {
            type: summaryType,
            startDate: {
              gte: period.start,
              lte: period.end,
            },
          },
        });

        // Aggregate data for the period
        const totalRevenue = summaries.reduce((sum, s) => sum + s.totalRevenue, 0);
        const totalProfit = summaries.reduce((sum, s) => sum + s.totalProfit, 0);
        const totalOrders = summaries.reduce((sum, s) => sum + s.totalOrders, 0);

        return {
          name: period.name,
          revenue: totalRevenue,
          profit: totalProfit,
          orders: totalOrders,
          period: period.name,
        };
      })
    );

    return NextResponse.json({ data });
  } catch (error) {
    console.error("Error fetching summary data:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
