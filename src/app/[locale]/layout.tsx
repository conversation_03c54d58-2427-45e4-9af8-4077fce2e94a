import { classUtil } from "@udoy/utils/class-util";
import "./globals.scss";
import type { Metadata } from "next";
import { Inter, Noto_Sans_Bengali } from "next/font/google";
import ToastContainer from "@udoy/components/ToastContainer";
import { Env } from "@udoy/libs/env";
import { routing } from "@udoy/i18n/routing";
import { hasLocale, NextIntlClientProvider } from "next-intl";
import { notFound } from "next/navigation";
import { setRequestLocale, getLocale } from "next-intl/server";
import { ThemeProvider } from "@udoy/components/ThemeProvider";
import SyncCart from "../../components/SyncCart";
import { Toaster } from "@udoy/components/ui/sonner";
import { GoogleOAuthProvider } from "@react-oauth/google";
import { Suspense } from "react";
import PushNotificationPrompt from "@udoy/components/PushNotificationPrompt";
import GoogleAnalytics from "@udoy/components/GoogleAnalytics";

const inter = Inter({ subsets: ["latin"] });
const noto = Noto_Sans_Bengali({ subsets: ["bengali"] });

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const locale = (await params).locale;
  const isBangla = locale === "bn";

  return {
    metadataBase: new URL(Env.NEXT_PUBLIC_FRONTEND_URL),
    title: {
      default: isBangla ? "উদয় মার্ট" : "UdoyMart",
      template: isBangla ? "%s | উদয় মার্ট" : "%s | UdoyMart",
    },
    description: isBangla
      ? "মোল্লাহাটে আপনার প্রতিদিনের তাজা শাকসবজি, ফলমূল, মাছ-মাংস, চাল-ডাল ও সব ধরনের মুদি পণ্য কিনুন উদয় মার্ট থেকে। ঘরে বসে সেরা মানের পণ্য স্বল্পমূল্যে দ্রুত পান।"
      : "UdoyMart - Your neighborhood haven for fresh, quality groceries. Explore a curated selection of farm-fresh produce and pantry essentials. Celebrating one year of serving your community with convenience and care. Shop UdoyMart for a delightful grocery experience today!",
    creator: "Imran Shaikh",
    authors: [{ name: "Imran Shaikh" }],
    verification: {
      google: "0CWcorgF6wh1WQcTukLKLVV1Bh0nXwcPPhaEQM1cw6I",
    },
    openGraph: {
      type: "website",
      locale: isBangla ? "bn_BD" : "en_US",
      countryName: "Bangladesh",
      description: isBangla
        ? "উদয় মার্ট-এ স্বাগতম! মোল্লারহাটে আপনার জন্য সেরা মানের মুদি পণ্য ও নিত্যপ্রয়োজনীয় সবকিছু এখন অনলাইনেই। সহজে কিনুন, দ্রুত ডেলিভারি পান। 🛒🍎"
        : "Welcome to UdoyMart, your premier destination for fresh and quality groceries. Experience the essence of UdoyMart, where every visit is a delightful journey through top-notch products. 🛒🍎 #UdoyMart #FreshGroceries #CommunityService",
      title: isBangla
        ? "উদয় মার্ট: মোল্লাহাটের বিশ্বস্ত অনলাইন মুদি দোকান"
        : "UdoyMart | Your Grocery Destination",
      alternateLocale: isBangla ? "en_US" : "bn_BD",
      siteName: isBangla ? "উদয় মার্ট" : "UdoyMart",
      images: ["/og-image.png"],
    },
    twitter: {
      card: "summary_large_image",
      creator: "@udoymart",
      site: "@udoymart",
      title: isBangla
        ? "উদয় মার্ট: মোল্লাহাটে আপনার অনলাইন বাজার"
        : "UdoyMart | Your Grocery Destination",
      description: isBangla
        ? "মোল্লাহাটে তাজা শাকসবজি, ফলমূল ও নিত্যপ্রয়োজনীয় মুদি সামগ্রী অনলাইনে কিনুন উদয় মার্ট থেকে। সেরা মান, সুলভ মূল্য এবং দ্রুত হোম ডেলিভারি। আজই অর্ডার করুন!"
        : "Explore a wide selection of fresh groceries and everyday essentials at UdoyMart. Best quality, affordable prices, and fast home delivery. Order today!",
    },
  };
}

export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }));
}

export default async function RootLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;

  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }
  setRequestLocale(locale);

  return (
    <html lang={locale} suppressHydrationWarning>
      <body className={classUtil(noto.className, `flex flex-col min-h-screen`)}>
        <GoogleAnalytics />
        <Suspense fallback={null}>
          <SyncCart />
        </Suspense>
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          <GoogleOAuthProvider clientId={Env.GOOGLE_CLIENT_ID}>
            <NextIntlClientProvider>
              <PushNotificationPrompt />
              <ToastContainer />
              {children}
              <div id="portal" />
            </NextIntlClientProvider>
          </GoogleOAuthProvider>
        </ThemeProvider>
        <Toaster />
      </body>
    </html>
  );
}
