"use client";

import { useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { optional, z } from "zod";
import { But<PERSON> } from "@udoy/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@udoy/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@udoy/components/ui/form";
import { Input } from "@udoy/components/ui/input";
import { isValidPlusCode } from "@udoy/utils";
import { CheckCircle } from "lucide-react";
import { cn } from "@udoy/utils/shadcn";
import Link from "next/link";

// Define the zod schema for shop validation
const shopFormSchema = z.object({
  name: z
    .string()
    .min(3, { message: "Shop name must be at least 3 characters" }),
  location: z
    .string()
    .min(5, { message: "Location must be at least 5 characters" }),
  slug: z
    .string()
    .min(3, { message: "Slug must be at least 3 characters" })
    .regex(/^[a-z0-9-]+$/, {
      message: "Slug can only contain lowercase letters, numbers, and hyphens",
    }),
  ownerId: z
    .string()
    .optional()
    .nullable()
    .transform((val) => (val ? Number(val) : null)) as any,
});

// Type for our form values
type ShopFormValues = z.infer<typeof shopFormSchema>;

interface ShopFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  shop?: any;
  onSubmit: (values: ShopFormValues) => void;
}

export function ShopFormDialog({
  isOpen,
  onClose,
  shop,
  onSubmit,
}: ShopFormDialogProps) {
  // Initialize the form with default values or existing shop data
  const form = useForm<ShopFormValues>({
    resolver: zodResolver(shopFormSchema),
    defaultValues: {
      name: "",
      location: "",
      slug: "",
      ownerId: "",
    },
  });

  // Reset form when shop changes
  useEffect(() => {
    if (shop) {
      form.reset({
        name: shop.name,
        location: shop.location,
        slug: shop.slug,
        ownerId: shop.ownerId,
      });
    } else {
      form.reset({
        name: "",
        location: "",
        slug: "",
        ownerId: "",
      });
    }
  }, [shop, form]);

  // Generate slug from name
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^\w\s-]/g, "")
      .replace(/\s+/g, "-");
  };

  // Handle name change to auto-generate slug
  const handleNameChange = (value: string) => {
    form.setValue("name", value);

    // Only auto-generate slug if it's a new shop or slug hasn't been manually edited
    if (
      !shop ||
      form.getValues("slug") === "" ||
      form.getValues("slug") === generateSlug(shop.name)
    ) {
      form.setValue("slug", generateSlug(value));
    }
  };

  // Handle form submission
  const handleSubmit = (values: ShopFormValues) => {
    onSubmit({
      ...values,
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{shop ? "Edit Shop" : "Add New Shop"}</DialogTitle>
          <DialogDescription>
            {shop
              ? "Make changes to the shop details here."
              : "Fill in the details to create a new shop."}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-4 py-4"
          >
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Shop Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter shop name"
                      {...field}
                      onChange={(e) => handleNameChange(e.target.value)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="slug"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Slug</FormLabel>
                  <FormControl>
                    <Input placeholder="shop-url-slug" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="ownerId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Owner ID{" "}
                    <span className="text-muted-foreground ">
                      {"(Optional)"}
                    </span>
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="Enter owner id" {...(field as any)} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Location</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        placeholder="WRJ3+JH2"
                        {...field}
                        onChange={(e) => {
                          const value = e.target.value;
                          const code = value
                            .split(" ")
                            .find((part) => isValidPlusCode(part));

                          field.onChange(code || value);
                        }}
                      />
                      <CheckCircle
                        size={18}
                        className={cn(
                          "absolute right-2 top-1/2 -translate-y-1/2 text-green-700 opacity-0 duration-150",
                          isValidPlusCode(field.value) && "opacity-100"
                        )}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                  <FormDescription>
                    Helps delivery person to find the shop easily.
                    <span className="ml-1 underline text-blue-700">
                      <Link target="__blank" href="https://plus.codes/map">
                        Find Shop
                      </Link>
                    </span>
                  </FormDescription>
                </FormItem>
              )}
            />

            <DialogFooter className="pt-4">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit">
                {shop ? "Update Shop" : "Create Shop"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
