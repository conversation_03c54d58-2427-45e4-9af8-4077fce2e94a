"use client";

import { useState } from "react";
import { Edit, Trash2, Store, ChevronDown, ChevronUp, Eye } from "lucide-react";
import { Button } from "@udoy/components/ui/button";
import { Card, CardContent } from "@udoy/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@udoy/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@udoy/components/ui/alert-dialog";
import { Shop, User } from "@prisma/client";

interface ShopListProps {
  shops: (Shop & { _count: { products: number }; owner?: User | null })[];
  onEdit: (shop: any) => void;
  onDelete: (shopId: string) => void;
  onViewDetails: (shop: any) => void;
}

export function ShopList({
  shops,
  onEdit,
  onDelete,
  onViewDetails,
}: ShopListProps) {
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [shopToDelete, setShopToDelete] = useState<string | null>(null);

  // Format date
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Confirm delete
  const confirmDelete = (shopId: string) => {
    setShopToDelete(shopId);
    setDeleteConfirmOpen(true);
  };

  // Handle delete confirmation
  const handleConfirmDelete = () => {
    if (shopToDelete) {
      onDelete(shopToDelete);
      setShopToDelete(null);
      setDeleteConfirmOpen(false);
    }
  };

  return (
    <Card>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[250px] cursor-pointer">
                  <div className="flex items-center">Shop Name</div>
                </TableHead>
                <TableHead className="cursor-pointer">
                  <div className="flex items-center">Owner</div>
                </TableHead>
                <TableHead className="cursor-pointer">
                  <div className="flex items-center">Location</div>
                </TableHead>
                <TableHead className="cursor-pointer">
                  <div className="flex items-center">Created</div>
                </TableHead>
                <TableHead className="cursor-pointer">
                  <div className="flex items-center">Products</div>
                </TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {shops.length === 0 ? (
                <TableRow>
                  <TableCell
                    colSpan={5}
                    className="text-center py-8 text-muted-foreground"
                  >
                    No shops found. Add a new shop to get started.
                  </TableCell>
                </TableRow>
              ) : (
                shops.map((shop) => (
                  <TableRow key={shop.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center">
                        <Store className="mr-2 h-4 w-4 text-muted-foreground" />
                        {shop.name}
                      </div>
                    </TableCell>
                    <TableCell>{shop.owner?.name || "N/A"}</TableCell>
                    <TableCell>{shop.location}</TableCell>
                    <TableCell>{formatDate(shop.createdAt)}</TableCell>
                    <TableCell>{shop?._count?.products}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => onViewDetails(shop)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => onEdit(shop)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => confirmDelete(shop.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>

      <AlertDialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              shop and remove its data from our servers.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmDelete}>
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
}
