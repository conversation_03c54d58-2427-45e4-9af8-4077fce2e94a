"use client"

import { useState } from "react"
import { Store, Package, Calendar, MapPin, Search } from "lucide-react"
import { <PERSON><PERSON> } from "@udoy/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@udoy/components/ui/dialog"
import { Input } from "@udoy/components/ui/input"
import { Badge } from "@udoy/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@udoy/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@udoy/components/ui/tabs"
import { Product } from "@prisma/client"



interface ShopDetailsDialogProps {
  isOpen: boolean
  onClose: () => void
  shop: any
}

export function ShopDetailsDialog({ isOpen, onClose, shop }: ShopDetailsDialogProps) {
  const [searchQuery, setSearchQuery] = useState("")

  if (!shop) return null

  // Filter products based on search query

  // Format date
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  // Format price
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
    }).format(price / 100)
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Store className="mr-2 h-5 w-5" />
            {shop.name}
          </DialogTitle>
          <DialogDescription>Shop details and associated products</DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center">
              <MapPin className="mr-2 h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{shop.location}</span>
            </div>
            <div className="flex items-center">
              <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Created: {formatDate(shop.createdAt)}</span>
            </div>
          </div>

          <Tabs defaultValue="products">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="products">Products ({shop._count.products})</TabsTrigger>
              <TabsTrigger value="info">Shop Info</TabsTrigger>
            </TabsList>

            <TabsContent value="products" className="space-y-4">
              <div className="flex items-center">
                <Search className="mr-2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="max-w-sm"
                />
              </div>

              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product</TableHead>
                      <TableHead>Price</TableHead>
                      <TableHead>Source Price</TableHead>
                      <TableHead>Supply</TableHead>
                      <TableHead>Discount</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {shop?.products?.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-6 text-muted-foreground">
                          No products found.
                        </TableCell>
                      </TableRow>
                    ) : (
                      shop.products?.map((product: Product) => (
                        <TableRow key={product.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium flex items-center">
                                <Package className="mr-2 h-4 w-4 text-muted-foreground" />
                                {product.name}
                              </div>
                              {product.nam && <div className="text-xs text-muted-foreground mt-1">{product.nam}</div>}
                            </div>
                          </TableCell>
                          <TableCell>{formatPrice(product.price)}</TableCell>
                          <TableCell>{formatPrice(product.sourcePrice)}</TableCell>
                          <TableCell>{product.supply}</TableCell>
                          <TableCell>
                            {product.discount > 0 ? (
                              <Badge variant="secondary">{product.discount}% off</Badge>
                            ) : (
                              <span className="text-muted-foreground">-</span>
                            )}
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>

            <TabsContent value="info">
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Shop Name</h3>
                    <p>{shop.name}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Slug</h3>
                    <p>{shop.slug}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Location</h3>
                    <p>{shop.location}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Created</h3>
                    <p>{formatDate(shop.createdAt)}</p>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">Shop Statistics</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-muted rounded-lg p-4">
                      <div className="text-2xl font-bold">{shop._count.products}</div>
                      <div className="text-sm text-muted-foreground">Total Products</div>
                    </div>
                    <div className="bg-muted rounded-lg p-4">
                      <div className="text-2xl font-bold">{formatPrice(4999)}</div>
                      <div className="text-sm text-muted-foreground">Average Product Price</div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <DialogFooter>
          <Button onClick={onClose}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
