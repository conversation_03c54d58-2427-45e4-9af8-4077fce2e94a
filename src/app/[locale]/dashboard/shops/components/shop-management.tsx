"use client";

import { useState } from "react";
import { Plus } from "lucide-react";
import { Button } from "@udoy/components/ui/button";
import { Input } from "@udoy/components/ui/input";
import { ShopList } from "./shop-list";
import { ShopFormDialog } from "./shop-form-dialog";
import { ShopDetailsDialog } from "./shop-details-dialog";
import { Shop, User } from "@prisma/client";
import { withError } from "@udoy/utils/app-error";
import { createShop, deleteShop, updateShop } from "../action";
import { toast } from "sonner";

// Mock data for shops - replace with actual data fetching

export function ShopManagement(props: {
  shops: (Shop & { _count: { products: number }; owner?: User | null })[];
}) {
  const [shops, setShops] = useState(props.shops);
  const [searchQuery, setSearchQuery] = useState("");
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedShop, setSelectedShop] = useState<any>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [currentShop, setCurrentShop] = useState<any>(null);

  // Filter shops based on search query

  // Handle shop creation
  const handleCreateShop = async (shopData: any) => {
    try {
      const shop = await withError(createShop(shopData));
      setShops([...shops, shop]);
      toast.success("Shop created");
    } catch (error: any) {
      toast.error(error?.message || "Failed to create shop");
    }
    setIsFormOpen(false);
  };

  // Handle shop update
  const handleUpdateShop = async (shopData: any, shopId: string) => {
    try {
      const shop = await withError(updateShop(shopId, shopData));
      setShops(shops.map((s) => (s.id === shop.id ? { ...s, ...shop } : s)));
      toast.success("Shop updated");
    } catch (error: any) {
      toast.error(error?.message || "Failed to update shop");
    }
    setIsFormOpen(false);
    setSelectedShop(null);
  };

  // Handle shop deletion
  const handleDeleteShop = async (shopId: string) => {
    try {
      await withError(deleteShop(shopId));
      toast.success("Shop deleted");
      setShops(shops.filter((shop) => shop.id !== shopId));
    } catch (error: any) {
      toast.error(error?.message || "Failed to delete shop");
    }
  };

  // Open form for editing
  const handleEditShop = (shop: any) => {
    setSelectedShop(shop);
    setIsFormOpen(true);
  };

  // Open details dialog
  const handleViewDetails = (shop: any) => {
    setCurrentShop(shop);
    setIsDetailsOpen(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
        <div className="w-full sm:w-auto">
          <Input
            placeholder="Search shops by name or location..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-md"
          />
        </div>
        <Button
          onClick={() => {
            setSelectedShop(null);
            setIsFormOpen(true);
          }}
        >
          <Plus className="mr-2 h-4 w-4" /> Add New Shop
        </Button>
      </div>

      <ShopList
        shops={props.shops}
        onEdit={handleEditShop}
        onDelete={handleDeleteShop}
        onViewDetails={handleViewDetails}
      />

      <ShopFormDialog
        isOpen={isFormOpen}
        onClose={() => {
          setIsFormOpen(false);
          setSelectedShop(null);
        }}
        shop={selectedShop}
        onSubmit={
          selectedShop
            ? (data) => handleUpdateShop(data, selectedShop.id) as any
            : handleCreateShop
        }
      />

      <ShopDetailsDialog
        isOpen={isDetailsOpen}
        onClose={() => {
          setIsDetailsOpen(false);
          setCurrentShop(null);
        }}
        shop={currentShop}
      />
    </div>
  );
}
