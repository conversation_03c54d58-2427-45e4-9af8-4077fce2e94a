"use server";

import { Role } from "@prisma/client";
import { ActionError } from "@udoy/utils/app-error";
import { CookieUtil } from "@udoy/utils/cookie-util";
import { getPrisma } from "@udoy/utils/db-utils";
import { revalidatePath } from "next/cache";

export async function createShop(data: any) {
  try {
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }
    const prisma = getPrisma();

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const shop = await prisma.shop.create({
      data: {
        name: data.name,
        slug: data.slug,
        location: data.location,
        ownerId: data.ownerId,
      },
      include: {
        owner: true,
        _count: {
          select: {
            products: true,
          },
        },
      },
    });

    revalidatePath("/dashboard/shops");
    return shop;
  } catch (error) {
    console.log(error);
    return ActionError("Failed To Create Shop");
  }
}

export async function updateShop(shopId: string, data: any) {
  try {
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();
    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const shop = await prisma.shop.update({
      where: { id: shopId },
      data: {
        name: data.name,
        slug: data.slug,
        location: data.location,
        ownerId: data.ownerId,
      },

      include: {
        owner: true,
        _count: {
          select: {
            products: true,
          },
        },
      }
    });
    revalidatePath("/dashboard/shops");
    return shop;
  } catch (error) {
    console.log(error);
    return ActionError("Failed To Update Shop");
  }
}

export async function deleteShop(shopId: string) {
  try {
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();
    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const shop = await prisma.shop.delete({
      where: { id: shopId },
    });
    revalidatePath("/dashboard/shops");
    return shop;
  } catch (error) {
    console.log(error);
    return ActionError("Failed To Delete Shop");
  }
}
