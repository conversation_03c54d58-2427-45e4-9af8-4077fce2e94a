import { getPrisma } from "@udoy/utils/db-utils";
import Layout from "../components/Layout";
import { ShopManagement } from "./components/shop-management";

export default async function ShopsPage() {
  const shops = await getPrisma().shop.findMany({
    include: {
      owner: true,
      products: {
        orderBy: {
          createdAt: "desc",
        },
        take: 30,
      },
      _count: {
        select: {
          products: true,
        },
      },
    },
  });
  return (
    <Layout>
      <div className="">
        <h1 className="text-3xl font-bold mb-6">Store Management</h1>
        <ShopManagement shops={shops} />
      </div>
    </Layout>
  );
}
