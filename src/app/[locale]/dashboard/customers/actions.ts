"use server";

import { Role } from "@prisma/client";
import { ActionError } from "@udoy/utils/app-error";
import { CookieUtil } from "@udoy/utils/cookie-util";
import { getPrisma } from "@udoy/utils/db-utils";
import { revalidatePath } from "next/cache";

export async function toggleUserBlock({
  userId,
  blocked,
}: {
  userId: number;
  blocked: boolean;
}) {
  try {
    const currentUserId = await CookieUtil.userId();

    if (!currentUserId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();

    // Check if current user has permission to block users
    const admin = await prisma.user.findUnique({
      where: {
        id: currentUserId,
        role: {
          in: [Role.SUPER_ADMIN, Role.ADMIN],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    // Prevent blocking super admins
    const targetUser = await prisma.user.findUnique({
      where: { id: userId },
      select: { role: true, name: true },
    });

    if (!targetUser) {
      return ActionError("User not found");
    }

    if (targetUser.role === Role.SUPER_ADMIN) {
      return ActionError("Cannot block super admin users");
    }

    // Prevent users from blocking themselves
    if (currentUserId === userId) {
      return ActionError("Cannot block yourself");
    }

    // Update user blocked status
    await prisma.user.update({
      where: { id: userId },
      data: { blocked },
    });

    // Revalidate the customer page to reflect changes
    revalidatePath(`/dashboard/customers/${userId}`);
    revalidatePath("/dashboard/customers");

    return {
      success: true,
      message: `User ${blocked ? "blocked" : "unblocked"} successfully`,
    };
  } catch (error) {
    console.error("Failed to toggle user block status:", error);
    return ActionError("Failed to update user block status");
  }
}
