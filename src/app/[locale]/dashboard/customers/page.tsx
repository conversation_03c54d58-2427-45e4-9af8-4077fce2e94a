import { Suspense } from "react";
import { getPrisma } from "@udoy/utils/db-utils";
import { Role as PrismaRole } from "@prisma/client";
import CustomersTable from "./components/customers-table";
import CustomersHeader from "./components/customers-header";
import CustomersFilters from "./components/customers-filters";
import Loading from "./loading";
import { buildQuery, FilterParams } from "./utils/queryBuilder";

export default async function PageWrapper({
  searchParams,
}: {
  searchParams: Promise<FilterParams>;
}) {
  const params = await searchParams;
  
  return (
    <Suspense key={JSON.stringify(params)} fallback={<Loading />}>
      <CustomersPage searchParams={params} />
    </Suspense>
  );
}

async function CustomersPage({ searchParams }: { searchParams: FilterParams }) {
  const prisma = getPrisma();
  const { whereClause, orderByClause, skip, limit, ...filterProps } = buildQuery(searchParams);

  // Get total count for pagination
  const totalCount = await prisma.user.count({
    where: whereClause,
  });

  // Get paginated users with their orders
  const users = await prisma.user.findMany({
    where: whereClause,
    orderBy: orderByClause,
    skip,
    take: limit,
    include: {
      orders: {
        include: {
          orderItems: true,
        },
        orderBy: {
          createdAt: "desc",
        },
      },
      _count: {
        select: {
          orders: true,
        },
      },
    },
  });

  // Calculate pagination info
  const totalPages = Math.ceil(totalCount / limit);
  const currentPage = filterProps.page;
  const hasNextPage = currentPage < totalPages;
  const hasPrevPage = currentPage > 1;

  // Transform the data to match our Customer interface
  const customers = users.map((user) => {
    // Calculate total spent across all orders
    const totalSpent = user.orders.reduce((sum, order) => {
      return sum + order.subTotal + order.shipping;
    }, 0);

    // Get the date of the most recent order if any
    const lastOrderDate =
      user.orders.length > 0
        ? user.orders[0].createdAt.toISOString()
        : undefined;

    return {
      ...user,
      ordersCount: user._count.orders,
      totalSpent,
      lastOrderDate,
    };
  });

  const paginationInfo = {
    totalCount,
    totalPages,
    currentPage,
    hasNextPage,
    hasPrevPage,
    limit,
  };

  return (
    <div className="flex flex-col gap-6">
      <CustomersHeader customers={customers} totalCount={totalCount} />
      <CustomersFilters {...filterProps} />
      <CustomersTable
        initialCustomers={customers}
        paginationInfo={paginationInfo}
      />
    </div>
  );
}
