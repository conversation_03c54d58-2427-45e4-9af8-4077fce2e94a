import { Prisma, Role } from "@prisma/client";

export interface FilterParams {
  search?: string;
  role?: string;
  sort?: string;
  page?: string;
  limit?: string;
}

export function buildQuery(params: FilterParams) {
  let orderByClause: Prisma.UserOrderByWithRelationInput = {};
  let whereClause: Prisma.UserWhereInput = {};

  const search = params.search || "";
  const role = params.role || null;
  const sortBy = params.sort || "newest";
  const page = parseInt(params.page || "1", 10);
  const limit = parseInt(params.limit || "20", 10);

  // Calculate pagination
  const skip = (page - 1) * limit;

  // Search filter
  if (search) {
    whereClause.OR = [
      { name: { contains: search, mode: "insensitive" } },
      { email: { contains: search, mode: "insensitive" } },
      { phone: { contains: search, mode: "insensitive" } },
    ];
  }

  // Role filter
  if (role && Object.values(Role).includes(role as Role)) {
    whereClause.role = role as Role;
  }

  // Sort options
  switch (sortBy) {
    case "name-asc":
      orderByClause = { name: "asc" };
      break;
    case "name-desc":
      orderByClause = { name: "desc" };
      break;
    case "orders-desc":
      orderByClause = { orders: { _count: "desc" } };
      break;
    case "newest":
    default:
      orderByClause = { createdAt: "desc" };
      break;
  }

  return {
    whereClause,
    orderByClause,
    search,
    role,
    sortBy,
    page,
    limit,
    skip,
  };
}