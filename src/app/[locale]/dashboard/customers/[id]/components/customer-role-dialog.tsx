"use client";

import { useState } from "react";
import { But<PERSON> } from "@udoy/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  <PERSON>alog<PERSON><PERSON>le,
  DialogTrigger,
} from "@udoy/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import { Role, User } from "@prisma/client";
import { toast } from "sonner";

// This would be an actual API call in a real implementation
async function updateUserRole(userId: number, role: Role) {
  // Implement the actual API call here
  // For now, we'll just simulate a successful update
  return new Promise((resolve) => {
    setTimeout(() => resolve({ success: true }), 500);
  });
}

export default function CustomerRoleDialog({ customer }: { customer: User }) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleRoleChange = async () => {
    if (!selectedRole) return;

    setIsLoading(true);
    try {
      await updateUserRole(customer.id, selectedRole);
      toast.success(`Role updated to ${selectedRole.replace("_", " ")}`);
      setIsOpen(false);
      // In a real implementation, you would refresh the data or update the UI
    } catch (error) {
      toast.error("Failed to update role");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm">
          Change Role
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Change User Role</DialogTitle>
          <DialogDescription>
            Select a new role for {customer.name}. This will change their
            permissions on the platform.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <Select
            onValueChange={(value) => setSelectedRole(value as Role)}
            defaultValue={customer.role}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select a role" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Available Roles</SelectLabel>
                <SelectItem value={Role.SUPER_ADMIN}>Super Admin</SelectItem>
                <SelectItem value={Role.ADMIN}>Admin</SelectItem>
                <SelectItem value={Role.MAINTAINER}>Maintainer</SelectItem>
                <SelectItem value={Role.USER}>User</SelectItem>
                <SelectItem value={Role.DELIVERY_MAN}>Delivery Man</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setIsOpen(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleRoleChange}
            disabled={!selectedRole || isLoading}
          >
            {isLoading ? "Saving..." : "Save Changes"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
