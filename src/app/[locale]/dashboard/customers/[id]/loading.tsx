import { Skeleton } from "@udoy/components/ui/skeleton"
import { <PERSON>, CardContent, CardHeader } from "@udoy/components/ui/card"
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@udoy/components/ui/tabs"

export default function Loading() {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center gap-4">
        <Skeleton className="h-10 w-10" />
        <Skeleton className="h-10 w-[200px]" />
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Customer Profile Card */}
        <Card className="md:col-span-1">
          <CardHeader className="flex flex-row items-center justify-between">
            <div className="space-y-1.5">
              <Skeleton className="h-5 w-[100px]" />
              <Skeleton className="h-4 w-[150px]" />
            </div>
            <Skeleton className="h-10 w-10" />
          </CardHeader>
          <CardContent className="flex flex-col items-center text-center">
            <Skeleton className="h-24 w-24 rounded-full mb-4" />
            <Skeleton className="h-6 w-[150px] mb-2" />
            <Skeleton className="h-5 w-[100px] mb-6" />
            <div className="w-full space-y-4">
              <Skeleton className="h-5 w-full" />
              <Skeleton className="h-5 w-full" />
              <Skeleton className="h-5 w-full" />
            </div>
          </CardContent>
        </Card>

        {/* Customer Details Tabs */}
        <div className="md:col-span-2">
          <Tabs defaultValue="orders">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="orders">Orders</TabsTrigger>
              <TabsTrigger value="addresses">Addresses</TabsTrigger>
              <TabsTrigger value="activity">Activity</TabsTrigger>
            </TabsList>
            <TabsContent value="orders" className="mt-4">
              <Card>
                <CardHeader className="space-y-1.5">
                  <Skeleton className="h-5 w-[150px]" />
                  <Skeleton className="h-4 w-[250px]" />
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {Array.from({ length: 3 }).map((_, index) => (
                      <Skeleton key={index} className="h-16 w-full" />
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Customer Stats */}
        <Card>
          <CardHeader>
            <Skeleton className="h-5 w-[80px]" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-8 w-[60px] mb-1" />
            <Skeleton className="h-4 w-[120px]" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <Skeleton className="h-5 w-[80px]" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-8 w-[80px] mb-1" />
            <Skeleton className="h-4 w-[120px]" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <Skeleton className="h-5 w-[80px]" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-8 w-[100px] mb-1" />
            <Skeleton className="h-4 w-[120px]" />
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
