"use client";

import React, { useState } from "react";
import { Button } from "@udoy/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@udoy/components/ui/alert-dialog";
import { Shield, ShieldOff } from "lucide-react";
import { toast } from "sonner";
import { withError } from "@udoy/utils/app-error";
import { toggleUserBlock } from "../actions";

interface BlockUserButtonProps {
  userId: number;
  userName: string;
  isBlocked: boolean;
}

export function BlockUserButton({
  userId,
  userName,
  isBlocked,
}: BlockUserButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [open, setOpen] = useState(false);

  const handleToggleBlock = async () => {
    setIsLoading(true);
    try {
      const result = await withError(
        toggleUserBlock({
          userId,
          blocked: !isBlocked,
        })
      );

      if (result.success) {
        toast.success(result.message);
        setOpen(false);
      }
    } catch (error: any) {
      toast.error(error?.message || "Failed to update user status");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={setOpen}>
      <AlertDialogTrigger asChild>
        <Button
          variant={isBlocked ? "default" : "destructive"}
          size="sm"
          className="gap-2"
        >
          {isBlocked ? (
            <>
              <Shield className="h-4 w-4" />
              Unblock User
            </>
          ) : (
            <>
              <ShieldOff className="h-4 w-4" />
              Block User
            </>
          )}
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {isBlocked ? "Unblock User" : "Block User"}
          </AlertDialogTitle>
          <AlertDialogDescription>
            {isBlocked
              ? `Are you sure you want to unblock ${userName}? They will be able to place orders again.`
              : `Are you sure you want to block ${userName}? They will not be able to place any orders while blocked.`}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleToggleBlock}
            disabled={isLoading}
            className={
              isBlocked
                ? "bg-green-600 hover:bg-green-700"
                : "bg-red-600 hover:bg-red-700"
            }
          >
            {isLoading
              ? "Processing..."
              : isBlocked
              ? "Unblock User"
              : "Block User"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
