"use client";

import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { Button } from "@udoy/components/ui/button";
import { Input } from "@udoy/components/ui/input";
import { Search, Filter, X } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import { Role } from "@prisma/client";
import { Badge } from "@udoy/components/ui/badge";
import Link from "next/link";

interface CustomersFiltersProps {
  search: string;
  role: string | null;
  sortBy: string;
}

export default function CustomersFilters({ 
  search, 
  role, 
  sortBy 
}: CustomersFiltersProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const createFilterUrl = (newParams: {
    [key: string]: string | null | undefined;
  }) => {
    const params = new URLSearchParams(searchParams.toString());
    
    // Update or remove params
    Object.entries(newParams).forEach(([key, value]) => {
      if (value === null || value === undefined || value === "") {
        params.delete(key);
      } else {
        params.set(key, value);
      }
    });

    return `${pathname}?${params.toString()}`;
  };

  const resetFiltersUrl = pathname;

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col gap-4 md:flex-row md:items-center">
        <form action={pathname} className="relative flex-1 md:max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            name="search"
            placeholder="Search customers..."
            className="pl-8"
            defaultValue={search}
          />
          {role && <input type="hidden" name="role" value={role} />}
          {sortBy !== "newest" && <input type="hidden" name="sort" value={sortBy} />}
          <button type="submit" className="sr-only">Search</button>
        </form>
        
        <div className="flex items-center gap-2">
          <Select
            value={role || "ALL"}
            onValueChange={(value) => 
              router.push(createFilterUrl({ role: value === "ALL" ? null : value }))
            }
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by role" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Roles</SelectLabel>
                <SelectItem value="ALL">All Roles</SelectItem>
                <SelectItem value={Role.SUPER_ADMIN}>Super Admin</SelectItem>
                <SelectItem value={Role.ADMIN}>Admin</SelectItem>
                <SelectItem value={Role.MAINTAINER}>Maintainer</SelectItem>
                <SelectItem value={Role.USER}>User</SelectItem>
                <SelectItem value={Role.DELIVERY_MAN}>Delivery Man</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
          
          <Select 
            value={sortBy} 
            onValueChange={(value) => 
              router.push(createFilterUrl({ sort: value === "newest" ? null : value }))
            }
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Sort by</SelectLabel>
                <SelectItem value="newest">Newest</SelectItem>
                <SelectItem value="name-asc">Name: A to Z</SelectItem>
                <SelectItem value="name-desc">Name: Z to A</SelectItem>
                <SelectItem value="orders-desc">Most Orders</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {/* Active filters */}
      {(search || role) && (
        <div className="flex flex-wrap items-center gap-2">
          <span className="text-sm text-muted-foreground">Active filters:</span>
          
          {search && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Search: {search}
              <Link href={createFilterUrl({ search: null })}>
                <X className="h-3 w-3 cursor-pointer" />
              </Link>
            </Badge>
          )}
          
          {role && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Role: {role}
              <Link href={createFilterUrl({ role: null })}>
                <X className="h-3 w-3 cursor-pointer" />
              </Link>
            </Badge>
          )}
          
          <Button variant="ghost" size="sm" asChild>
            <Link href={resetFiltersUrl}>Clear all</Link>
          </Button>
        </div>
      )}
    </div>
  );
}
