"use client";

import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@udoy/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@udoy/components/ui/table";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@udoy/components/ui/avatar";
import { Badge } from "@udoy/components/ui/badge";
import { Checkbox } from "@udoy/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@udoy/components/ui/dropdown-menu";
import { CopyIcon, MoreHorizontal } from "lucide-react";
import { Role, User } from "@prisma/client";
import { cn } from "@udoy/utils/shadcn";
import { withError } from "@udoy/utils/app-error";
import { updateUserRoles } from "@udoy/actions/user";
import { changeRole } from "@udoy/actions/administration";
import { toast } from "sonner";
import { CustomersPagination } from "./CustomersPagination";

// Helper function to get role badge variant
function getRoleBadgeVariant(role: Role) {
  switch (role) {
    case Role.SUPER_ADMIN:
      return "destructive";
    case Role.ADMIN:
      return "default";
    case Role.MAINTAINER:
      return "secondary";
    case Role.USER:
      return "outline";
    case Role.DELIVERY_MAN:
      return "warning";
    default:
      return "outline";
  }
}

interface PaginationInfo {
  totalCount: number;
  totalPages: number;
  currentPage: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  limit: number;
}

export default function CustomersTable({
  initialCustomers,
  paginationInfo,
}: {
  initialCustomers: (User & {
    ordersCount: number;
    totalSpent: number;
    lastOrderDate?: string;
  })[];
  paginationInfo: PaginationInfo;
}) {
  const [customers, setCustomers] = useState<
    (User & {
      ordersCount: number;
      totalSpent: number;
      lastOrderDate?: string;
    })[]
  >(initialCustomers);
  const [selectedCustomers, setSelectedCustomers] = useState<number[]>([]);

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedCustomers(customers.map((c) => c.id));
    } else {
      setSelectedCustomers([]);
    }
  };

  const handleSelectCustomer = (customerId: number, checked: boolean) => {
    if (checked) {
      setSelectedCustomers((prev) => [...prev, customerId]);
    } else {
      setSelectedCustomers((prev) => prev.filter((id) => id !== customerId));
    }
  };

  // Format date to a more readable format
  const formatDate = (dateString?: string) => {
    if (!dateString) return "No orders";
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(date);
  };

  async function handleRoleChange(customerId: number, role: Role) {
    try {
      const changed = await withError(changeRole(customerId, role));
      if (changed) {
        toast.success(`Role updated to ${role.replace("_", " ")}`);
      }
    } catch (error: any) {
      toast.error(error?.message || "Failed to update role");
    }
  }

  async function handleCopyId(customerId: number) {
    try {
      await navigator.clipboard.writeText(customerId.toString());
      toast.success("Copied to clipboard");
    } catch (error: any) {
      toast.error(error?.message || "Failed to copy");
    }
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">
              <Checkbox
                checked={selectedCustomers.length === customers.length}
                onCheckedChange={handleSelectAll}
              />
            </TableHead>
            <TableHead>Customer</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Role</TableHead>
            <TableHead className="text-right">Orders</TableHead>
            <TableHead className="text-right">Total Spent</TableHead>
            <TableHead>Last Order</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {customers.map((customer) => (
            <TableRow key={customer.id}>
              <TableCell>
                <Checkbox
                  checked={selectedCustomers.includes(customer.id)}
                  onCheckedChange={(checked) =>
                    handleSelectCustomer(customer.id, checked as boolean)
                  }
                />
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-3">
                  <Avatar className="h-8 w-8">
                    <AvatarImage
                      src={customer.avatar || "/placeholder.svg"}
                      alt={customer.name}
                    />
                    <AvatarFallback>
                      {customer.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">{customer.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {customer.phone || "No phone"}
                    </p>
                  </div>
                </div>
              </TableCell>
              <TableCell>{customer.email}</TableCell>
              <TableCell>
                <Badge variant={getRoleBadgeVariant(customer.role) as any}>
                  {customer.role.replace("_", " ")}
                </Badge>
              </TableCell>
              <TableCell className="text-right">
                {customer.ordersCount}
              </TableCell>
              <TableCell className="text-right">
                ৳ {customer.totalSpent}
              </TableCell>
              <TableCell>{formatDate(customer.lastOrderDate)}</TableCell>
              <TableCell className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                      <span className="sr-only">Open menu</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuItem asChild>
                      <Link href={`/dashboard/customers/${customer.id}`}>
                        View details
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem>Edit customer</DropdownMenuItem>
                    <DropdownMenuSeparator />

                    <DropdownMenuSub>
                      <DropdownMenuSubTrigger>
                        Change Role
                      </DropdownMenuSubTrigger>
                      <DropdownMenuPortal>
                        <DropdownMenuSubContent>
                          {[
                            Role.ADMIN,
                            Role.MAINTAINER,
                            Role.USER,
                            Role.DELIVERY_MAN,
                          ].map((role) => (
                            <DropdownMenuItem
                              key={role}
                              onClick={() =>
                                handleRoleChange(customer.id, role)
                              }
                              className={cn(
                                customer.role === role && "bg-brand/10"
                              )}
                            >
                              {role.replace("_", " ")}
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenuSubContent>
                      </DropdownMenuPortal>
                    </DropdownMenuSub>

                    <DropdownMenuItem
                      className="flex justify-between"
                      onClick={() => handleCopyId(customer.id)}
                    >
                      <span>Copy Id</span>
                      <CopyIcon />
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Pagination */}
      <div className="mt-4">
        <CustomersPagination paginationInfo={paginationInfo} />
      </div>
    </div>
  );
}
