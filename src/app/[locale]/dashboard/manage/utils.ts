import { z } from "zod";

export const productFormSchema = z.object({
  name: z.string().min(3, { message: "Name must be at least 3 characters" }),
  nam: z.string().optional(),
  amount: z.coerce
    .number()
    .int()
    .positive({ message: "Amount must be a positive number" }),
  quantity: z.coerce
    .number()
    .int()
    .nonnegative({ message: "Quantity must be a non-negative number" }),
  supply: z.coerce
    .number()
    .int()
    .nonnegative({ message: "Supply must be a non-negative number" }),
  maxOrder: z.coerce
    .number()
    .min(1, { message: "Maximum order must be at least 1" })
    .int()
    .nonnegative({ message: "Maximum order must be a non-negative number" }),
  price: z.coerce
    .number()
    .int()
    .positive({ message: "Price must be a positive number" }),
  sourcePrice: z.coerce
    .number()
    .int()
    .nonnegative({ message: "Source price must be a non-negative number" }),
  discount: z.coerce.number().int().min(0),
  extraCharge: z.coerce.number().int().min(0),
  unitId: z.string({ required_error: "Please select a unit" }),
  shopId: z
    .string()
    .nullable()
    .transform((val) => (val === "" ? null : val)),
  companyId: z
    .string()
    .nullable()
    .transform((val) => (val === "" ? null : val)),
  featured: z.boolean(),
  position: z.number().int().nonnegative(),
});

type Category = {
  id: string;
  name: string;
  isBase: boolean;
  subCategories: Category[];
};
export function getSubcategories(category: Category) {
  const subIds = [];
  const baseIds = [];

  let categories = [category];
  while (categories.length) {
    const current = categories.shift();
    if (!current) {
      continue;
    }

    if (current.isBase) {
      baseIds.push(current.id);
      subIds.push(current.id);
    } else {
      subIds.push(current.id);
    }
    categories = categories.concat(current.subCategories);
  }

  return { subIds, baseIds };
}
