import { getPrisma } from "@udoy/utils/db-utils";
import React from "react";
import CategoryItem from "./components/CategoryItem";
import ManagePageClient from "./components/ManagePageClient";

async function getData() {
  const prisma = getPrisma();
  const categories = await prisma.category.findMany({
    where: { parentId: null },
    orderBy: [{ hide: "asc" }, { featured: "desc" }, { position: "asc" }],
  });

  // Get all categories for parent selection in dialogs
  const allCategories = await prisma.category.findMany({
    orderBy: [{ name: "asc" }],
  });

  return { categories, allCategories };
}

async function Page() {
  const { categories, allCategories } = await getData();
  return (
    <ManagePageClient
      categories={categories}
      allCategories={allCategories}
    />
  );
}

export default Page;
