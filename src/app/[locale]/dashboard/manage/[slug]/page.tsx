import { getPrisma } from "@udoy/utils/db-utils";
import { PageProps } from "@udoy/utils/types";
import Icon from "@udoy/components/Icon";
import Hide from "@udoy/components/Hide";
import { Category } from "@prisma/client";
import Link from "next/link";
import { getLocale } from "next-intl/server";
import SyncActivePath from "../components/SyncActivePath";
import { HomeIcon } from "lucide-react";
import RenderPageItems from "../components/RenderPageItems";

function flattenPathToParent(
  category: (Category & { parentCategory?: Category | null }) | null | undefined
): { en: string; bn: string | null }[] {
  if (!category) {
    return [];
  }

  if (!category?.parentCategory) {
    return [{ en: category.slug, bn: category.nam }];
  }
  return [
    { en: category.slug, bn: category.nam },
    ...flattenPathToParent(category.parentCategory),
  ];
}

async function getData(parentSlug?: string) {
  const start = Date.now();
  if (!parentSlug) {
    return {
      categories: [],
      path: [],
      category: null,
      allCategories: [],
    };
  }

  const category = await getPrisma().category.findUnique({
    where: { slug: parentSlug },
    include: {
      products: {
        include: { images: true, unit: true },
        orderBy: [{ hide: "asc" }, { featured: "desc" }, { position: "asc" }],
      },
    },
  });

  const pathToParent = await getPrisma().category.findUnique({
    where: { slug: parentSlug },
    select: {
      slug: true,
      nam: true,
      parentCategory: {
        select: {
          slug: true,
          nam: true,
          parentCategory: {
            select: { slug: true, nam: true, parentCategory: true },
          },
        },
      },
    },
  });
  const path = flattenPathToParent(pathToParent as any).reverse();

  // Get all categories for parent selection in dialogs
  const allCategories = await getPrisma().category.findMany({
    orderBy: [{ name: "asc" }],
  });

  if (category?.isBase) {
    console.log("Time taken", Date.now() - start);
    return {
      categories: [],
      path,
      category,
      allCategories,
    };
  }

  const categories = await getPrisma().category.findMany({
    where: { parentCategory: { slug: parentSlug } },
    orderBy: [{ hide: "asc" }, { featured: "desc" }, { position: "asc" }],
  });

  console.log("Time taken", Date.now() - start);

  return {
    categories,
    path,
    category,
    allCategories,
  };
}

async function CategoriesPage(props: PageProps<{ slug: string }>) {
  const params = await props.params;
  const locale = await getLocale();
  const isBngla = locale === "bn";

  const { categories, path, category, allCategories } = await getData(params.slug);
  return (
    <div
      className="flex-1 overflow-y-scroll relative pb-6"
      style={{ height: "calc(100vh - 64px)" }}
    >
      <div className="flex gap-1 items-center bg-background z-40 sticky top-0 duration-200 px-4 md:px-8 py-4">
        <Link href={{ pathname: "/dashboard/manage" }} className="">
          <HomeIcon className="h-4 w-4 text-muted-foreground" />
        </Link>

        <Icon icon="chevron-right" className="text-sm text-muted-foreground" />
        {path.map((category, i) => (
          <div key={category.en} className="flex items-center">
            <Link
              className="font-semibold text-sm text-muted-foreground truncate"
              href={{ pathname: `/dashboard/manage/${category.en}` }}
              style={{ maxWidth: Math.floor(90 / path.length) + "vw" }}
            >
              {isBngla ? category.bn : category.en}
            </Link>
            <Hide open={i < path.length - 1}>
              <Icon
                icon="chevron-right"
                className="text-sm ml-1 text-muted-foreground"
              />
            </Hide>
          </div>
        ))}
      </div>
      <SyncActivePath path={path.map((item) => item.en)} />

      <RenderPageItems
        categories={categories}
        products={category?.products || []}
        isBase={category?.isBase || false}
        currentCategory={category}
        allCategories={allCategories}
      />
    </div>
  );
}

export default CategoriesPage;
