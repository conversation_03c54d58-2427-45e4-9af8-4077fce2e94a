"use client";

import { Category } from "@prisma/client";
import { useState, useCallback } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@udoy/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@udoy/components/ui/form";
import { Input } from "@udoy/components/ui/input";
import { Button } from "@udoy/components/ui/button";
import { toast } from "sonner";
import { withError } from "@udoy/utils/app-error";
import { createCategory } from "../action";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import { Upload, X, ChevronDown } from "lucide-react";
import Image from "next/image";
import {
  Command,
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@udoy/components/ui/command";

interface CategoryCreateDialogProps {
  isOpen: boolean;
  onClose: () => void;
  availableCategories?: Category[];
  defaultParentId?: string | null;
}

// Category form schema
const categoryCreateSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters"),
  nam: z.string().nullable(),
  slug: z.string().min(3, "Slug must be at least 3 characters"),
  isBase: z.boolean(),
  featured: z.boolean(),
  position: z.number().int().nonnegative(),
  parentId: z.string().nullable(),
  image: z.instanceof(File).nullable(),
});

// Form type
type CategoryCreateFormValues = z.infer<typeof categoryCreateSchema>;

export function CategoryCreateDialog({
  isOpen,
  onClose,
  availableCategories = [],
  defaultParentId = null,
}: CategoryCreateDialogProps) {
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [commandOpen, setCommandOpen] = useState(false);

  // Function to generate slug from name
  const generateSlug = useCallback((name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  }, []);

  // Initialize form
  const form = useForm<CategoryCreateFormValues>({
    resolver: zodResolver(categoryCreateSchema),
    defaultValues: {
      name: "",
      nam: "",
      slug: "",
      isBase: false,
      featured: false,
      position: 0,
      parentId: defaultParentId,
      image: null,
    },
  });

  // Watch name field to auto-generate slug
  const watchedName = form.watch("name");

  // Get selected parent category for display
  const selectedParentCategory = availableCategories.find(
    cat => cat.id === form.watch("parentId")
  );

  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedImage(file);
      form.setValue("image", file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle image removal
  const handleImageRemove = () => {
    setSelectedImage(null);
    setImagePreview(null);
    form.setValue("image", null);
  };

  // Handle form reset
  const handleClose = () => {
    form.reset();
    setImagePreview(null);
    setSelectedImage(null);
    setCommandOpen(false);
    onClose();
  };

  // Handle category creation submission
  async function handleCreateCategory(values: CategoryCreateFormValues) {
    try {
      const formData = new FormData();
      
      // Add basic category data
      formData.append("name", values.name);
      formData.append("nam", values.nam || "");
      formData.append("slug", values.slug);
      formData.append("isBase", values.isBase.toString());
      formData.append("featured", values.featured.toString());
      formData.append("position", values.position.toString());
      formData.append("parentId", values.parentId || "");
      
      // Add image if selected
      if (selectedImage) {
        formData.append("image", selectedImage);
      }

      const result = await withError(createCategory(formData));
      if (result) {
        toast.success("Category created successfully");
        handleClose();
      }
    } catch (error: any) {
      toast.error(error?.message || "Failed to create category");
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Category</DialogTitle>
          <DialogDescription>
            Create a new category with all the necessary information
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleCreateCategory)}
            className="space-y-4"
          >
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input 
                      {...field} 
                      onChange={(e) => {
                        field.onChange(e);
                        // Auto-generate slug if slug is empty
                        if (!form.getValues("slug")) {
                          form.setValue("slug", generateSlug(e.target.value));
                        }
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="nam"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Bangla Name</FormLabel>
                  <FormControl>
                    <Input {...field} value={field.value || ""} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="slug"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Slug</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="parentId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Parent Category</FormLabel>
                  <FormControl>
                    <Button
                      type="button"
                      onClick={() => setCommandOpen(true)}
                      className="w-full justify-between"
                      variant="outline"
                    >
                      <span>
                        {selectedParentCategory?.name || "No Parent (Root Category)"}
                      </span>
                      <ChevronDown className="h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                  <CommandDialog
                    open={commandOpen}
                    onOpenChange={setCommandOpen}
                  >
                    <CommandInput placeholder="Search parent category..." />
                    <CommandList className="w-full">
                      <CommandEmpty>No results found.</CommandEmpty>

                      <CommandGroup heading="Available Parent Categories">
                        <CommandItem
                          onSelect={() => {
                            field.onChange(null);
                            setCommandOpen(false);
                          }}
                        >
                          No Parent (Root Category)
                        </CommandItem>
                        {availableCategories
                          .filter((cat) => !cat.isBase)
                          .map((cat) => (
                            <CommandItem
                              key={cat.id}
                              onSelect={() => {
                                field.onChange(cat.id);
                                setCommandOpen(false);
                              }}
                            >
                              {cat.name}
                            </CommandItem>
                          ))}
                      </CommandGroup>
                    </CommandList>
                  </CommandDialog>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Image Upload Section */}
            <FormField
              control={form.control}
              name="image"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Category Image</FormLabel>
                  <FormControl>
                    <div className="space-y-4">
                      {imagePreview ? (
                        <div className="relative w-full h-32 border rounded-md overflow-hidden">
                          <Image
                            src={imagePreview}
                            alt="Category preview"
                            fill
                            className="object-cover"
                          />
                          <Button
                            type="button"
                            variant="destructive"
                            size="icon"
                            className="absolute top-2 right-2 h-6 w-6"
                            onClick={handleImageRemove}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ) : (
                        <div className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-md p-6">
                          <Upload className="h-8 w-8 text-gray-400 mb-2" />
                          <p className="text-sm text-gray-500">No image selected</p>
                        </div>
                      )}
                      <div>
                        <Input
                          type="file"
                          accept="image/*"
                          className="hidden"
                          id="category-create-image-upload"
                          onChange={handleImageUpload}
                        />
                        <Button type="button" variant="outline" asChild>
                          <label htmlFor="category-create-image-upload" className="cursor-pointer">
                            <Upload className="mr-2 h-4 w-4" />
                            {imagePreview ? "Change Image" : "Upload Image"}
                          </label>
                        </Button>
                      </div>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="isBase"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Is Base Category</FormLabel>
                    <FormControl>
                      <Select
                        value={field.value ? "true" : "false"}
                        onValueChange={(value) =>
                          field.onChange(value === "true")
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select option" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="true">Yes</SelectItem>
                          <SelectItem value="false">No</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="featured"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Featured</FormLabel>
                    <FormControl>
                      <Select
                        value={field.value ? "true" : "false"}
                        onValueChange={(value) =>
                          field.onChange(value === "true")
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select option" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="true">Yes</SelectItem>
                          <SelectItem value="false">No</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="position"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Position</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="0"
                      {...field}
                      onChange={(e) =>
                        field.onChange(parseInt(e.target.value) || 0)
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={form.formState.isSubmitting}>
                {form.formState.isSubmitting ? "Creating..." : "Create Category"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
