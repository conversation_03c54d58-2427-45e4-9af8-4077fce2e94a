"use client";

import { Category, Product } from "@prisma/client";
import Hide from "@udoy/components/Hide";
import React, { useState } from "react";
import { useLocale } from "next-intl";
import CategoryItem from "./CategoryItem";
import ProductItem from "./ProductItem";
import { Button } from "@udoy/components/ui/button";
import { Plus } from "lucide-react";
import { CategoryCreateDialog } from "./CategoryCreateDialog";

function RenderPageItems(props: {
  categories: Category[];
  products: Product[];
  isBase: boolean;
  currentCategory?: Category | null;
  allCategories?: Category[];
}) {
  const [products] = React.useState(props.products);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const locale = useLocale();

  return (
    <div className="px-4 md:px-8">
      <Hide
        open={props?.isBase}
        fallback={
          <div className="space-y-6">
            {/* Create Category Button for non-base categories */}
            {props.currentCategory && !props.currentCategory.isBase && (
              <div className="flex justify-end">
                <Button onClick={() => setCreateDialogOpen(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Subcategory
                </Button>
              </div>
            )}
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              {props.categories.map((category) => (
                <CategoryItem
                  key={category.id}
                  category={category}
                  availableCategories={props.allCategories || []}
                />
              ))}
            </div>
          </div>
        }
      >
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {products?.map((product) => (
            <ProductItem
              key={product.id}
              product={product as any}
              locale={locale}
            />
          ))}
        </div>
      </Hide>

      {/* Category Create Dialog */}
      <CategoryCreateDialog
        isOpen={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        availableCategories={props.allCategories || []}
        defaultParentId={props.currentCategory?.id || null}
      />
    </div>
  );
}

export default RenderPageItems;
