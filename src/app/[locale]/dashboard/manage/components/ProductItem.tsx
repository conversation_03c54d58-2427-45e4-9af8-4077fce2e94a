"use client";

import { Product, ProductImage, QuantityUnit } from "@prisma/client";
import { UnitUtil } from "@udoy/utils/product-unit";
import Image from "next/image";
import {
  Card,
  CardContent,
  <PERSON>Footer,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import { getLocale } from "next-intl/server";
import { localeNumber } from "@udoy/utils";
import { useLocale } from "next-intl";
import { cn } from "@udoy/utils/shadcn";
import AdminProductAction from "./AdminProductAction";
import ProductForm from "./ProductQuickForm";
import React, { createContext, useState } from "react";

function Pricing({ price, discount }: { price: number; discount: number }) {
  const locale = useLocale();

  if (discount === 0) {
    return (
      <span className="font-semibold">
        ৳ {localeNumber(price - discount, locale)}
      </span>
    );
  }

  return (
    <div className="relative">
      <span className="font-semibold line-through text-muted-foreground text-xs absolute block min-w-max left-full -top-1">
        ৳ {localeNumber(price, locale)}
      </span>
      <span className="font-semibold leading-none">
        ৳ {localeNumber(price - discount, locale)}
      </span>
    </div>
  );
}

type ProductWithImagesAndUnit = Product & {
  images: ProductImage[];
  unit: QuantityUnit;
};

const ProductContext = createContext<{
  product: ProductWithImagesAndUnit;
  setProduct: React.Dispatch<React.SetStateAction<ProductWithImagesAndUnit>>;
}>({ product: {} as ProductWithImagesAndUnit, setProduct: () => {} });

export function useProduct() {
  return React.useContext(ProductContext);
}

function ProductItem({
  product: pro,
  locale,
}: {
  product: ProductWithImagesAndUnit;
  locale: string;
}) {
  const isBangla = locale === "bn";
  const [product, setProduct] = useState(pro);

  return (
    <ProductContext value={{ product, setProduct }}>
      <Card className="flex flex-col overflow-clip relative">
        <AdminProductAction product={product} />
        <CardHeader className={cn("p-0", product.hide && "opacity-50")}>
          <Image
            src={product.images[0]?.url}
            width={400}
            height={400}
            alt={product.name}
            className="object-cover w-full rounded rounded-b-none"
          />
        </CardHeader>
        <CardContent
          className={cn(
            "p-2 flex-1 flex flex-col",
            product.hide && "opacity-50"
          )}
        >
          <CardTitle className="text-center text-sm line-clamp-2 mt-3">
            {isBangla ? product.nam || product.name : product.name}
          </CardTitle>

          <div className="flex-1"></div>
          <div className="flex justify-between items-center mt-3 text-lg ">
            <Pricing discount={product.discount} price={product.price} />
            <span className="text-sm">
              {UnitUtil.getAmountUnit(product.amount, product.unit, locale)}
            </span>
          </div>
        </CardContent>
        <CardFooter className={cn("p-2 pt-0", product.hide && "opacity-50")}>
          <ProductForm  />
        </CardFooter>
      </Card>
    </ProductContext>
  );
}

export default ProductItem;
