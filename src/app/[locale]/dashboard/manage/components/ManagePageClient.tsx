"use client";

import { Category } from "@prisma/client";
import { useState } from "react";
import { Button } from "@udoy/components/ui/button";
import { Plus } from "lucide-react";
import CategoryItem from "./CategoryItem";
import { CategoryCreateDialog } from "./CategoryCreateDialog";

interface ManagePageClientProps {
  categories: Category[];
  allCategories: Category[];
}

export default function ManagePageClient({
  categories,
  allCategories,
}: ManagePageClientProps) {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);

  return (
    <div
      className="flex-1 p-6 md:p-8 overflow-y-auto"
      style={{ maxHeight: "calc(100vh - 64px)" }}
    >
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Shop</h1>
          <p className="text-muted-foreground">
            Manage your shop&apos;s categories and products.
          </p>
        </div>
        <Button onClick={() => setCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Create New Category
        </Button>
      </div>

      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-12">
        {categories.map((category) => (
          <CategoryItem 
            key={category.id} 
            category={category} 
            availableCategories={allCategories}
          />
        ))}
      </div>

      <CategoryCreateDialog
        isOpen={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        availableCategories={allCategories}
      />
    </div>
  );
}
