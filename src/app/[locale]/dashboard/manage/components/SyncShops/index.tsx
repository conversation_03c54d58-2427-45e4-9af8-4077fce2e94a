import React from "react";
import SyncShopClient from "./Sync";
import { getPrisma } from "@udoy/utils/db-utils";

async function SyncShop({ children }: { children: React.ReactNode }) {
  const shops = await getPrisma().shop.findMany();
  const companies = await getPrisma().company.findMany();
  return (
    <SyncShopClient shops={shops} companies={companies}>
      {children}
    </SyncShopClient>
  );
}

export default SyncShop;
