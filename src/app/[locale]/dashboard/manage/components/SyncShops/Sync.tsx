"use client";

import { useHydrateAtoms } from "jotai/utils";
import { companiesAtom, shopsAtom } from "../../state";
import { Company, Shop } from "@prisma/client";

function SyncShopClient({
  shops,
  children,
  companies,
}: {
  shops: Shop[];
  companies: Company[];
  children?: React.ReactNode;
}) {
  useHydrateAtoms([
    [shopsAtom, shops],
    [companiesAtom, companies],
  ]);
  return children;
}

export default SyncShopClient;
