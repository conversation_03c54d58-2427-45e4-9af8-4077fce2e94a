import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@udoy/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import React from "react";
import { unitsAtom } from "../state";
import { useAtomValue } from "jotai";

function UnitSelector({ control }: { control: any }) {
  const units = useAtomValue(unitsAtom);
  return (
    <FormField
      control={control}
      name="unitId"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Unit</FormLabel>
          <FormField
            control={control}
            name="unitId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Unit</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a unit" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {units.map((unit) => (
                      <SelectItem key={unit.id} value={unit.id}>
                        {unit.full}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export default UnitSelector;
