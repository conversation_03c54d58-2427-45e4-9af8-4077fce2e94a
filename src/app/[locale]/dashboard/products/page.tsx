import Link from "next/link";
import { But<PERSON> } from "@udoy/components/ui/button";
import { Plus } from "lucide-react";
import { ProductsTable } from "./components/ProductsTable";
import { ProductFilters } from "./components/ProductFilters";
import { getPrisma } from "@udoy/utils/db-utils";
import { buildQuery, FilterParams } from "./utils/queryBuilder";
import { wait } from "@udoy/utils/wait";
import { Suspense } from "react";
import Loading from "./loading";

export default async function PageWraper({
  searchParams,
}: {
  searchParams: Promise<FilterParams>;
}) {
  const params = await searchParams;
  return (
    <Suspense key={JSON.stringify(params)} fallback={<Loading />}>
      <ProductsPage searchparams={params} />
    </Suspense>
  );
}

async function ProductsPage({ searchparams }: { searchparams: FilterParams }) {
  // Parse search params
  const prisma = getPrisma();
  const { whereClause, orderByClause, ...props } = buildQuery(searchparams);

  // Fetch products with filters and sorting
  const products = await prisma.product.findMany({
    where: whereClause,
    orderBy: orderByClause,
    include: {
      images: true,
      unit: true,
      category: true,
    },
    take: 50, // Pagination can be added later
  });

  // Fetch categories for filter UI
  const categories = await prisma.category.findMany({
    where: {
      OR: [
        // Categories that have base subcategories
        {
          subCategories: {
            some: {
              isBase: true,
            },
          },
        },
        // Categories that are themselves base categories with no parent
        {
          isBase: true,
          parentId: null,
        },
      ],
    },

    select: {
      id: true,
      name: true,
      isBase: true,
      subCategories: {
        where: {
          isBase: true,
        },
        select: {
          id: true,
          name: true,
        },
      },
    },
  });

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Products</h1>
        <div className="flex flex-wrap items-center gap-2">
          <Button asChild>
            <Link href="/dashboard/products/new">
              <Plus className="mr-2 h-4 w-4" />
              Add Product
            </Link>
          </Button>
        </div>
      </div>

      <div className="flex flex-col gap-4 md:flex-row md:items-start">
        {/* Main search and filters */}
        <div className="flex w-full flex-col gap-4">
          <ProductFilters {...props} categories={categories} />

          {/* Products table */}
          <ProductsTable products={products} />
        </div>
      </div>
    </div>
  );
}
