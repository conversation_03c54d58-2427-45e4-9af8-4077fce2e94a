import Link from "next/link";
import { But<PERSON> } from "@udoy/components/ui/button";
import { Input } from "@udoy/components/ui/input";
import { Badge } from "@udoy/components/ui/badge";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@udoy/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import { Checkbox } from "@udoy/components/ui/checkbox";
import { Search, Filter, X } from "lucide-react";
import {
  Command,
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
  CommandShortcut,
} from "@udoy/components/ui/command";
import { Separator } from "@udoy/components/ui/separator";
import Hide from "@udoy/components/Hide";
import StockFilterSelect from "./StockFilterSelect";
import SortSelect from "./SortSelect";
import { Env } from "@udoy/libs/env";

// Helper function to format currency
function formatCurrency(amount: number) {
  return `৳ ${amount.toLocaleString()}`;
}

interface Category {
  id: string;
  name: string;
  subCategories: { id: string; name: string }[];
}

interface ProductFiltersProps {
  search: string;
  categoryIds: string[];
  minPrice: number;
  maxPrice: number;
  stockFilter: string | null;
  sortBy: string;
  categories: Category[];
}

export function ProductFilters({
  search,
  categoryIds,
  minPrice,
  maxPrice,
  stockFilter,
  sortBy,
  categories,
}: // createFilterUrl,
// toggleCategoryUrl,
// resetFiltersUrl,
ProductFiltersProps) {
  const createFilterUrl = (newParams: {
    [key: string]: string | string[] | null | undefined;
  }) => {
    // Create URL object with current path
    const url = new URL("/dashboard/products", Env.NEXT_PUBLIC_FRONTEND_URL);

    // Add current params
    if (search) url.searchParams.set("search", search);
    categoryIds.forEach((id) => url.searchParams.append("categories", id));
    if (minPrice > 0) url.searchParams.set("minPrice", minPrice.toString());
    if (maxPrice < 2000) url.searchParams.set("maxPrice", maxPrice.toString());
    if (stockFilter) url.searchParams.set("stock", stockFilter);
    if (sortBy !== "newest") url.searchParams.set("sort", sortBy);

    // Override with new params
    Object.entries(newParams).forEach(([key, value]) => {
      if (value === null || value === undefined) {
        url.searchParams.delete(key);
      } else if (Array.isArray(value)) {
        url.searchParams.delete(key);
        value.forEach((v) => url.searchParams.append(key, v));
      } else {
        url.searchParams.set(key, value);
      }
    });

    return url.pathname + url.search;
  };

  // Toggle category in URL
  const toggleCategoryUrl = (categoryId: string) => {
    const newCategories = categoryIds.includes(categoryId)
      ? categoryIds.filter((id) => id !== categoryId)
      : [...categoryIds, categoryId];

    return createFilterUrl({
      categories: newCategories.length > 0 ? newCategories : null,
    });
  };

  // Reset filters URL
  const resetFiltersUrl = "/dashboard/products";
  return (
    <>
      <div className="flex flex-wrap items-center gap-2">
        <form action="/dashboard/products" className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            name="search"
            placeholder="Search products..."
            className="pl-8"
            defaultValue={search}
          />
          {/* Preserve other filters when searching */}
          {categoryIds.map((id) => (
            <input key={id} type="hidden" name="categories" value={id} />
          ))}
          {minPrice > 0 && (
            <input type="hidden" name="minPrice" value={minPrice.toString()} />
          )}
          {maxPrice < 2000 && (
            <input type="hidden" name="maxPrice" value={maxPrice.toString()} />
          )}
          {stockFilter && (
            <input type="hidden" name="stock" value={stockFilter} />
          )}
          {sortBy !== "newest" && (
            <input type="hidden" name="sort" value={sortBy} />
          )}
          <button type="submit" className="sr-only">
            Search
          </button>
        </form>

        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" className="shrink-0">
              <Filter className="mr-2 h-4 w-4" />
              Filter
              {(categoryIds.length > 0 ||
                stockFilter !== null ||
                minPrice > 0 ||
                maxPrice < 2000) && (
                <Badge
                  variant="secondary"
                  className="ml-2 rounded-sm px-1 font-normal"
                >
                  !
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[340px]" align="end">
            <div className="grid gap-4">
              <div className="space-y-2">
                <h4 className="font-medium leading-none">Categories</h4>
                <div className="">
                  <Command className="w-full col-span-2">
                    <CommandInput placeholder="Type a command or search..." />
                    <CommandList className="w-full max-h-[200px]">
                      <CommandEmpty>No results found.</CommandEmpty>

                      {categories.map((category) => (
                        <Hide
                          open={category.subCategories.length > 0}
                          key={category.id}
                        >
                          <CommandGroup heading={category.name}>
                            {category.subCategories.map((item) => (
                              <Link
                                key={item.id}
                                href={toggleCategoryUrl(item.id)}
                              >
                                <CommandItem key={item.id}>
                                  {item.name}
                                </CommandItem>
                              </Link>
                            ))}
                          </CommandGroup>
                        </Hide>
                      ))}
                    </CommandList>
                  </Command>
                </div>
              </div>

              <Separator className="mb-2" />
              <div className="space-y-2">
                <h4 className="font-medium leading-none">Price Range</h4>
                <form action="/dashboard/products" className="pt-2">
                  {/* Preserve other filters */}
                  {search && (
                    <input type="hidden" name="search" value={search} />
                  )}
                  {categoryIds.map((id) => (
                    <input
                      key={id}
                      type="hidden"
                      name="categories"
                      value={id}
                    />
                  ))}
                  {stockFilter && (
                    <input type="hidden" name="stock" value={stockFilter} />
                  )}
                  {sortBy !== "newest" && (
                    <input type="hidden" name="sort" value={sortBy} />
                  )}

                  <div className="flex items-center gap-2">
                    <Input
                      type="number"
                      name="minPrice"
                      defaultValue={minPrice}
                      min={0}
                      max={maxPrice}
                      className="flex-1"
                    />
                    <span>to</span>
                    <Input
                      type="number"
                      name="maxPrice"
                      defaultValue={maxPrice}
                      min={minPrice}
                      max={2000}
                      className="flex-1"
                    />
                    <Button className="flex-1" type="submit" size="sm">
                      Apply
                    </Button>
                  </div>
                </form>
              </div>

              <Separator className="mb-2" />
              <div className="space-y-2">
                <h4 className="font-medium leading-none">Stock Status</h4>
                <div className="pt-2">
                  <StockFilterSelect
                    stockFilter={stockFilter}
                    baseUrl="/dashboard/products"
                  />
                </div>
              </div>
              <div className="flex items-center justify-between pt-2">
                <Button variant="outline" className="w-full" asChild>
                  <Link href={resetFiltersUrl}>Reset filters</Link>
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>

        <SortSelect sortBy={sortBy} baseUrl="/dashboard/products" />
      </div>

      {/* Active filters */}
      {(categoryIds.length > 0 ||
        stockFilter !== null ||
        minPrice > 0 ||
        maxPrice < 2000) && (
        <div className="flex flex-wrap items-center gap-2">
          <span className="text-sm text-muted-foreground">Active filters:</span>
          {categoryIds.map((categoryId) => {
            const category = categories.find((c) =>
              c.subCategories.find((sc) => sc.id === categoryId)
            );
            return (
              category && (
                <Badge
                  key={categoryId}
                  variant="secondary"
                  className="flex items-center gap-1"
                >
                  {category.name}
                  <Link href={toggleCategoryUrl(categoryId)}>
                    <X className="h-3 w-3 cursor-pointer" />
                  </Link>
                </Badge>
              )
            );
          })}
          {stockFilter && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {stockFilter === "in-stock"
                ? "In Stock"
                : stockFilter === "low-stock"
                ? "Low Stock"
                : "Out of Stock"}
              <Link href={createFilterUrl({ stock: null })}>
                <X className="h-3 w-3 cursor-pointer" />
              </Link>
            </Badge>
          )}
          {(minPrice > 0 || maxPrice < 2000) && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {formatCurrency(minPrice)} - {formatCurrency(maxPrice)}
              <Link href={createFilterUrl({ minPrice: null, maxPrice: null })}>
                <X className="h-3 w-3 cursor-pointer" />
              </Link>
            </Badge>
          )}
          <Button variant="ghost" size="sm" asChild>
            <Link href={resetFiltersUrl}>Clear all</Link>
          </Button>
        </div>
      )}
    </>
  );
}
