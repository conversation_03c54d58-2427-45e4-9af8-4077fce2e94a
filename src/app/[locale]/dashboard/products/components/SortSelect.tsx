"use client";

import { useRouter, useSearchParams } from "next/navigation";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";

type SortSelectProps = {
  sortBy: string;
  baseUrl: string;
};

export default function SortSelect({ sortBy, baseUrl }: SortSelectProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const handleValueChange = (value: string) => {
    const newParams = new URLSearchParams(searchParams.toString());
    
    if (value === "newest") {
      newParams.delete("sort");
    } else {
      newParams.set("sort", value);
    }
    
    router.push(`${baseUrl}?${newParams.toString()}`);
  };

  return (
    <div className="relative">
      <Select defaultValue={sortBy} onValueChange={handleValueChange}>
        <SelectTrigger className="w-[180px] shrink-0">
          <SelectValue placeholder="Sort by" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectLabel>Sort by</SelectLabel>
            <SelectItem value="newest">Newest</SelectItem>
            <SelectItem value="price-asc">Price: Low to High</SelectItem>
            <SelectItem value="price-desc">Price: High to Low</SelectItem>
            <SelectItem value="name-asc">Name: A to Z</SelectItem>
            <SelectItem value="name-desc">Name: Z to A</SelectItem>
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
  );
}