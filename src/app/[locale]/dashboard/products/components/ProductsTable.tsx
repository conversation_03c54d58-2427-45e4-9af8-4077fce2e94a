import Link from "next/link";
import { Button } from "@udoy/components/ui/button";
import { Badge } from "@udoy/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@udoy/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@udoy/components/ui/dropdown-menu";
import { Eye, Edit, Trash2, MoreHorizontal } from "lucide-react";
import Image from "next/image";
import { Category, Product, ProductImage, QuantityUnit } from "@prisma/client";

// Helper function to format currency
function formatCurrency(amount: number) {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
  }).format(amount / 100);
}

// Helper function to get stock status
function getStockStatus(supply: number) {
  if (supply === 0) {
    return { status: "out-of-stock", label: "Out of Stock" };
  } else if (supply <= 10) {
    return { status: "low-stock", label: "Low Stock" };
  } else {
    return { status: "in-stock", label: "In Stock" };
  }
}

interface ProductsTableProps {
  products: any[];
}

export function ProductsTable({ products }: ProductsTableProps) {
  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Product</TableHead>
            <TableHead>Category</TableHead>
            <TableHead className="text-right">Price</TableHead>
            <TableHead className="text-right">Stock</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {products.length > 0 ? (
            products.map(
              (
                product: Product & {
                  images: ProductImage[];
                  unit: QuantityUnit;
                  category: Category;
                }
              ) => {
                const stockStatus = getStockStatus(product.supply);
                return (
                  <TableRow key={product.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        {product.images.length > 0 && (
                          <Image
                            width={40}
                            height={40}
                            src={product.images[0].url || "/placeholder.svg"}
                            alt={product.name}
                            className="h-10 w-10 rounded-md object-cover"
                          />
                        )}
                        <div>
                          <p className="font-medium">{product.name}</p>
                          <p className="text-xs text-muted-foreground">
                            {product.amount} {product.unit.slug}
                          </p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {product.category?.name || "Uncategorized"}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex flex-col items-end">
                        <span className="font-medium">
                          ৳{" "}
                          {(product.price - product.discount).toLocaleString()}
                        </span>
                        {product.discount > 0 && (
                          <span className="text-xs text-muted-foreground line-through">
                            ৳ {product.price.toLocaleString()}
                          </span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      {product.supply}
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          stockStatus.status === "in-stock"
                            ? "default"
                            : stockStatus.status === "low-stock"
                            ? "secondary"
                            : "destructive"
                        }
                      >
                        {stockStatus.label}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">Open menu</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem asChild>
                            <Link href={`/dashboard/products/${product.id}`}>
                              <Eye className="mr-2 h-4 w-4" />
                              View details
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link
                              href={`/dashboard/products/${product.id}/edit`}
                            >
                              <Edit className="mr-2 h-4 w-4" />
                              Edit product
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-destructive"
                            asChild
                          >
                            <Link
                              href={`/dashboard/products/${product.id}/delete`}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete product
                            </Link>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                );
              }
            )
          ) : (
            <TableRow>
              <TableCell colSpan={6} className="h-24 text-center">
                No products found.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}
