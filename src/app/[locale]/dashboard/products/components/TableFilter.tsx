import React from "react";

function TableFilter() {
  return (
    <div className="">
      <div className="flex w-full flex-col gap-4">
        <div className="flex flex-wrap items-center gap-2">
          {/* <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search products..."
              className="pl-8"
            />
          </div> */}
        </div>

        {/* {(selectedCategories.length > 0 ||
          stockFilter !== null ||
          priceRange[0] > 0 ||
          priceRange[1] < 2000) && (
          <div className="flex flex-wrap items-center gap-2">
            <span className="text-sm text-muted-foreground">
              Active filters:
            </span>
            {selectedCategories.map((categoryId) => {
              const category = categories.find((c) => c.id === categoryId);
              return (
                category && (
                  <Badge
                    key={categoryId}
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    {category.name}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() =>
                        setSelectedCategories((prev) =>
                          prev.filter((id) => id !== categoryId)
                        )
                      }
                    />
                  </Badge>
                )
              );
            })}
            {stockFilter && (
              <Badge variant="secondary" className="flex items-center gap-1">
                {stockFilter === "in-stock"
                  ? "In Stock"
                  : stockFilter === "low-stock"
                  ? "Low Stock"
                  : "Out of Stock"}
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => setStockFilter(null)}
                />
              </Badge>
            )}
            {(priceRange[0] > 0 || priceRange[1] < 2000) && (
              <Badge variant="secondary" className="flex items-center gap-1">
                {formatCurrency(priceRange[0])} -{" "}
                {formatCurrency(priceRange[1])}
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => setPriceRange([0, 2000])}
                />
              </Badge>
            )}
            <Button variant="ghost" size="sm">
              Clear all
            </Button>
          </div>
        )} */}

        {/* Products table */}
        {/* <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]">
                  <Checkbox
                    // checked={filteredProducts.length > 0 && selectedProducts.length === filteredProducts.length}
                    // onCheckedChange={toggleAllProducts}
                    aria-label="Select all products"
                  />
                </TableHead>
                <TableHead>Product</TableHead>
                <TableHead>Category</TableHead>
                <TableHead className="text-right">Price</TableHead>
                <TableHead className="text-right">Stock</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <Hide
                open={products.length > 0}
                fallback={
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      No products found.
                    </TableCell>
                  </TableRow>
                }
              >
                {products.map((product) => {
                  const stockStatus = getStockStatus(product.supply);
                  return (
                    <TableRow key={product.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedProducts.includes(product.id)}
                          // onCheckedChange={() => toggleProductSelection(product.id)}
                          aria-label={`Select ${product.name}`}
                        />
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          {product.images.length > 0 && (
                            <img
                              src={product.images[0].url || "/placeholder.svg"}
                              alt={product.name}
                              className="h-10 w-10 rounded-md object-cover"
                            />
                          )}
                          <div>
                            <p className="font-medium">{product.name}</p>
                            <p className="text-xs text-muted-foreground">
                              {product.amount} {product.unit.slug}
                            </p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {product.category?.name || "Uncategorized"}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex flex-col items-end">
                          <span className="font-medium">
                            {formatCurrency(product.price)}
                          </span>
                          {product.discount > 0 && (
                            <span className="text-xs text-muted-foreground line-through">
                              {formatCurrency(product.price + product.discount)}
                            </span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        {product.supply}
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            stockStatus.status === "in-stock"
                              ? "default"
                              : stockStatus.status === "low-stock"
                              ? "secondary"
                              : "destructive"
                          }
                        >
                          {stockStatus.label}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Open menu</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem
                              onClick={() =>
                                router.push(`/dashboard/products/${product.id}`)
                              }
                            >
                              <Eye className="mr-2 h-4 w-4" />
                              View details
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() =>
                                router.push(
                                  `/dashboard/products/${product.id}/edit`
                                )
                              }
                            >
                              <Edit className="mr-2 h-4 w-4" />
                              Edit product
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-destructive">
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete product
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </Hide>
            </TableBody>
          </Table>
        </div> */}
      </div>
      {/* Active filters
      {(selectedCategories.length > 0 ||
        stockFilter !== null ||
        priceRange[0] > 0 ||
        priceRange[1] < 2000) && (
        <div className="flex flex-wrap items-center gap-2">
          <span className="text-sm text-muted-foreground">Active filters:</span>
          {selectedCategories.map((categoryId) => {
            const category = categories.find((c) => c.id === categoryId);
            return (
              category && (
                <Badge
                  key={categoryId}
                  variant="secondary"
                  className="flex items-center gap-1"
                >
                  {category.name}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() =>
                      setSelectedCategories((prev) =>
                        prev.filter((id) => id !== categoryId)
                      )
                    }
                  />
                </Badge>
              )
            );
          })}
          {stockFilter && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {stockFilter === "in-stock"
                ? "In Stock"
                : stockFilter === "low-stock"
                ? "Low Stock"
                : "Out of Stock"}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => setStockFilter(null)}
              />
            </Badge>
          )}
          {(priceRange[0] > 0 || priceRange[1] < 2000) && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {formatCurrency(priceRange[0])} - {formatCurrency(priceRange[1])}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => setPriceRange([0, 2000])}
              />
            </Badge>
          )}
          <Button variant="ghost" size="sm">
            Clear all
          </Button>
        </div>
      )} */}

      {/* <Select defaultValue="newest">
        <SelectTrigger className="w-[180px] shrink-0">
          <SelectValue placeholder="Sort by" />
        </SelectTrigger>
        <SelectContent value="newest">
          <SelectGroup>
            <SelectLabel>Sort by</SelectLabel>
            <SelectItem value="newest">Newest</SelectItem>
            <SelectItem value="price-asc">Price: Low to High</SelectItem>
            <SelectItem value="price-desc">Price: High to Low</SelectItem>
            <SelectItem value="name-asc">Name: A to Z</SelectItem>
            <SelectItem value="name-desc">Name: Z to A</SelectItem>
          </SelectGroup>
        </SelectContent>
      </Select> */}
      {/* <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline" className="shrink-0">
            <Filter className="mr-2 h-4 w-4" />
            Filter
            {(selectedCategories.length > 0 ||
              stockFilter !== null ||
              priceRange[0] > 0 ||
              priceRange[1] < 2000) && (
              <Badge
                variant="secondary"
                className="ml-2 rounded-sm px-1 font-normal"
              >
                !
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[340px]" align="end">
          <div className="grid gap-4">
            <div className="space-y-2">
              <h4 className="font-medium leading-none">Categories</h4>
              <div className="grid grid-cols-2 gap-2">
                {categories.map((category) => (
                  <div
                    key={category.id}
                    className="flex items-center space-x-2"
                  >
                    <Checkbox
                      id={`category-${category.id}`}
                      checked={selectedCategories.includes(category.id)}
                      // onCheckedChange={() => toggleCategory(category.id)}
                    />
                    <label
                      htmlFor={`category-${category.id}`}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {category.name}
                    </label>
                  </div>
                ))}
              </div>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium leading-none">Price Range</h4>
              <div className="pt-4">
                <Slider
                  defaultValue={priceRange}
                  min={0}
                  max={2000}
                  step={10}
                  value={priceRange}
                  onValueChange={(value) =>
                    setPriceRange(value as [number, number])
                  }
                />
                <div className="mt-2 flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">
                    {formatCurrency(priceRange[0])}
                  </span>
                  <span className="text-sm text-muted-foreground">
                    {formatCurrency(priceRange[1])}
                  </span>
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium leading-none">Stock Status</h4>
              <Select
                value={stockFilter || "all"}
                onValueChange={(value) =>
                  setStockFilter(value === "all" ? null : value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="All stock statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All stock statuses</SelectItem>
                  <SelectItem value="in-stock">In Stock</SelectItem>
                  <SelectItem value="low-stock">Low Stock</SelectItem>
                  <SelectItem value="out-of-stock">Out of Stock</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center justify-between pt-2">
              <Button variant="ghost">Reset filters</Button>
              <Button onClick={() => setIsFilterOpen(false)}>
                Apply filters
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover> */}
    </div>
  );
}

export default TableFilter;
