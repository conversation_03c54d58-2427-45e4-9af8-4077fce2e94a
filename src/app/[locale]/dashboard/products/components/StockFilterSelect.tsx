"use client";

import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from "@udoy/components/ui/select";
import { useRouter, useSearchParams } from "next/navigation";

type StockFilterSelectProps = {
  stockFilter: string | null;
  baseUrl: string;
};

export default function StockFilterSelect({ stockFilter, baseUrl }: StockFilterSelectProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const handleValueChange = (value: string) => {
    const newParams = new URLSearchParams(searchParams.toString());
    
    if (value === "all") {
      newParams.delete("stock");
    } else {
      newParams.set("stock", value);
    }
    
    router.push(`${baseUrl}?${newParams.toString()}`);
  };

  return (
    <Select
      defaultValue={stockFilter || "all"}
      onValueChange={handleValueChange}
    >
      <SelectTrigger className="w-full">
        <SelectValue placeholder="All stock statuses" />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectLabel>Stock Status</SelectLabel>
          <SelectItem value="all">All stock statuses</SelectItem>
          <SelectItem value="in-stock">In Stock</SelectItem>
          <SelectItem value="low-stock">Low Stock</SelectItem>
          <SelectItem value="out-of-stock">Out of Stock</SelectItem>
        </SelectGroup>
      </SelectContent>
    </Select>
  );
}
