import { getPrisma } from "@udoy/utils/db-utils";
import { ProductForm } from "../components/product-form";

async function getData() {
  const prisma = getPrisma();
  const categories = await prisma.category.findMany({
    where: {
      OR: [
        // Categories that have base subcategories
        {
          subCategories: {
            some: {
              isBase: true,
            },
          },
        },
        // Categories that are themselves base categories with no parent
        {
          isBase: true,
          parentId: null,
        },
      ],
    },

    include: {
      subCategories: {
        where: {
          isBase: true,
        },
      },
    },
  });
  const quantityUnits = await prisma.quantityUnit.findMany();
  const shops = await prisma.shop.findMany();
  const companies = await prisma.company.findMany();

  return {
    categories,
    units: quantityUnits,
    shops,
    companies,
  };
}

export default async function NewProductPage() {
  const { categories, units, shops, companies } = await getData();
  return (
    <ProductForm
      categories={categories}
      quantityUnits={units}
      shops={shops}
      companies={companies}
    />
  );
}
