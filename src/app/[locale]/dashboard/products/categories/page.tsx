import { ProductCategories } from "../../components/product-categories"

// Sample data for categories
const categories = [
  { id: "cat_1", name: "Fruits", slug: "fruits", productCount: 12 },
  { id: "cat_2", name: "Vegetables", slug: "vegetables", productCount: 18 },
  { id: "cat_3", name: "Dairy", slug: "dairy", productCount: 8 },
  { id: "cat_4", name: "Bakery", slug: "bakery", productCount: 10 },
  { id: "cat_5", name: "Meat", slug: "meat", productCount: 6 },
  { id: "cat_6", name: "Seafood", slug: "seafood", productCount: 4 },
  { id: "cat_7", name: "Beverages", slug: "beverages", productCount: 15 },
  { id: "cat_8", name: "Snacks", slug: "snacks", productCount: 20 },
]

export default function ProductCategoriesPage() {
  return (
    <div className="flex flex-col gap-6">
      <h1 className="text-3xl font-bold tracking-tight">Product Categories</h1>
      <p className="text-muted-foreground">
        Manage product categories to organize your inventory and help customers find products more easily.
      </p>
      <ProductCategories categories={categories} />
    </div>
  )
}
