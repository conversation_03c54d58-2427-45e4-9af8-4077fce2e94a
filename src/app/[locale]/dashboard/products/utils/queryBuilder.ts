import { Prisma } from "@prisma/client";

export interface FilterParams {
  search?: string;
  categories?: string | string[];
  minPrice?: string;
  maxPrice?: string;
  stock?: string;
  sort?: string;
}

export function buildQuery(params: FilterParams) {
  let orderByClause: Prisma.ProductOrderByWithRelationInput = {};
  // Build where clause based on filters
  let whereClause: Prisma.ProductWhereInput = {};
  const search = params.search || "";
  const categoryIds = params.categories
    ? Array.isArray(params.categories)
      ? params.categories
      : [params.categories]
    : [];
  const minPrice = parseInt(params.minPrice || "0", 10);
  const maxPrice = parseInt(params.maxPrice || "2000", 10);
  const stockFilter = params.stock || null;
  const sortBy = params.sort || "newest";

  // Search filter
  if (search) {
    whereClause.OR = [
      { name: { contains: search, mode: "insensitive" } },
      { description: { contains: search, mode: "insensitive" } },
      { nam: { contains: search, mode: "insensitive" } },
      { biboron: { contains: search, mode: "insensitive" } },
    ];
  }

  // Category filter
  if (categoryIds.length > 0) {
    whereClause.categoryId = { in: categoryIds };
  }

  // Price filter
  whereClause.price = {
    gte: minPrice,
    lte: maxPrice,
  };

  // Stock filter
  if (stockFilter) {
    if (stockFilter === "in-stock") {
      whereClause.supply = { gt: 10 };
    } else if (stockFilter === "low-stock") {
      whereClause.supply = { gt: 0, lte: 10 };
    } else if (stockFilter === "out-of-stock") {
      whereClause.supply = { equals: 0 };
    }
  }

  switch (sortBy) {
    case "price-asc":
      orderByClause = { price: "asc" };
      break;
    case "price-desc":
      orderByClause = { price: "desc" };
      break;
    case "name-asc":
      orderByClause = { name: "asc" };
      break;
    case "name-desc":
      orderByClause = { name: "desc" };
      break;
    case "newest":
    default:
      orderByClause = { createdAt: "desc" };
      break;
  }

  return {
    whereClause,
    orderByClause,
    categoryIds,
    minPrice,
    maxPrice,
    stockFilter,
    sortBy,
    search
  };
}