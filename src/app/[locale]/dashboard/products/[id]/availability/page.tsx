import React from "react";
import ProductAvailabilityManager from "./components/ProductAvailabilityManager";
import { getPrisma } from "@udoy/utils/db-utils";
import { notFound } from "next/navigation";

async function getData(productId: string) {
  const prisma = getPrisma();
  const product = await prisma.product.findUnique({
    where: { id: productId },
    include: {
      availability: {
        orderBy: {
          priority: 'desc'
        }
      },
    },
  });

  return product;
}

async function Page({ params }: { params: Promise<{ id: string }> }) {
  const { id: productId } = await params;
  const product = await getData(productId);

  if (!product) {
    return notFound();
  }

  return (
    <div>
      <ProductAvailabilityManager product={product} />
    </div>
  );
}

export default Page;
