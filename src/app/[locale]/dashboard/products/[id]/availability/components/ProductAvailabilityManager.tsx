"use client";

import { useState } from "react";
import {
  Calendar,
  Clock,
  Edit,
  Plus,
  Trash2,
  Info,
  ArrowLeft,
} from "lucide-react";
import { But<PERSON> } from "@udoy/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import { Switch } from "@udoy/components/ui/switch";
import { Badge } from "@udoy/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@udoy/components/ui/dialog";
import { Label } from "@udoy/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@udoy/components/ui/radio-group";
import { Checkbox } from "@udoy/components/ui/checkbox";
import { Input } from "@udoy/components/ui/input";
import { Textarea } from "@udoy/components/ui/textarea";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@udoy/components/ui/alert-dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@udoy/components/ui/tooltip";
import { useRouter } from "next/navigation";
import {
  AvailabilityType,
  DayOfWeek,
  Prisma,
  ProductAvailability,
} from "@prisma/client";
import { toast } from "sonner";
import { withError } from "@udoy/utils/app-error";
import {
  changeAlwaysAvailable,
  createAvailabilitySchedule,
  deleteAvailabilitySchedule,
  updateAvailabilitySchedule,
} from "../actions";
import { AvailabilityFormInput } from "../schema";
import { Separator } from "@udoy/components/ui/separator";

interface Product {
  id: string;
  name: string;
  alwaysAvailable: boolean;
}

const DAYS_OF_WEEK: { value: DayOfWeek; label: string }[] = [
  { value: "SUNDAY", label: "Sunday" },
  { value: "MONDAY", label: "Monday" },
  { value: "TUESDAY", label: "Tuesday" },
  { value: "WEDNESDAY", label: "Wednesday" },
  { value: "THURSDAY", label: "Thursday" },
  { value: "FRIDAY", label: "Friday" },
  { value: "SATURDAY", label: "Saturday" },
];

const AVAILABILITY_TYPES: {
  value: AvailabilityType;
  label: string;
  description: string;
}[] = [
  {
    value: "DAILY_RECURRING",
    label: "Daily Recurring",
    description: "Same time every day",
  },
  {
    value: "WEEKLY_RECURRING",
    label: "Weekly Recurring",
    description: "Specific days of the week",
  },
  {
    value: "DATE_RANGE",
    label: "Date Range",
    description: "Between specific dates",
  },
  { value: "ONE_TIME", label: "One Time", description: "Single specific date" },
];

export default function ProductAvailabilityManager({
  product,
}: {
  product: Product & { availability: ProductAvailability[] };
}) {
  const availabilitySchedules = product.availability;

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [alwaysAvailable, setAlwaysAvailable] = useState(
    product.alwaysAvailable
  );
  const [editingSchedule, setEditingSchedule] =
    useState<ProductAvailability | null>(null);
  const [formData, setFormData] = useState<AvailabilityFormInput>({
    type: "DAILY_RECURRING",
    isAvailable: product.alwaysAvailable,
    dayOfWeek: undefined,
    startTime: "",
    endTime: "",
    startDate: "" as any,
    endDate: "" as any,
    beforeMessage: "",
    afterMessage: "",
    priority: 1,
    productId: product.id,
  });
  const router = useRouter();

  // Helper function to get default priority based on schedule type
  const getDefaultPriority = (type: AvailabilityType): number => {
    switch (type) {
      case AvailabilityType.ONE_TIME:
        return 4;
      case AvailabilityType.DATE_RANGE:
        return 3;
      case AvailabilityType.WEEKLY_RECURRING:
        return 2;
      case AvailabilityType.DAILY_RECURRING:
      default:
        return 1;
    }
  };

  const handleToggleAlwaysAvailable = async (checked: boolean) => {
    try {
      setAlwaysAvailable(checked);
      await withError(changeAlwaysAvailable(product.id, checked));
      toast.success("Availability updated");
    } catch (error: any) {
      setAlwaysAvailable(!checked);
      toast.error(error?.message || "Failed to update availability");
    }
  };

  const handleAddSchedule = () => {
    setEditingSchedule(null);
    const defaultType = "DAILY_RECURRING" as AvailabilityType;
    setFormData({
      type: defaultType,
      isAvailable: !product.alwaysAvailable,
      dayOfWeek: undefined,
      startTime: "09:00",
      endTime: "17:00",
      startDate: undefined,
      endDate: undefined,
      beforeMessage: "",
      afterMessage: "",
      priority: getDefaultPriority(defaultType),
      productId: product.id,
    });
    setIsModalOpen(true);
  };

  const handleEditSchedule = (schedule: ProductAvailability) => {
    setEditingSchedule(schedule);
    setFormData({
      type: schedule.type,
      isAvailable: schedule.isAvailable,
      dayOfWeek: schedule.dayOfWeek || (undefined as DayOfWeek | undefined),
      startTime: schedule.startTime || "",
      endTime: schedule.endTime || "",
      startDate: schedule.startDate || (undefined as any),
      endDate: schedule.endDate || (undefined as any),
      beforeMessage: schedule.beforeMessage || "",
      afterMessage: schedule.afterMessage || "",
      priority: schedule.priority || 1,
      productId: product.id,
    });
    setIsModalOpen(true);
  };

  const handleDeleteSchedule = (id: string) => {
    try {
      withError(deleteAvailabilitySchedule(id));
      toast.success("Schedule deleted");
    } catch (error: any) {
      toast.error(error?.message || "Failed to delete schedule");
    }
  };

  const handleSaveSchedule = () => {
    if (editingSchedule) {
      updateSchedule();
    } else {
      createSchedule();
    }
    setIsModalOpen(false);
  };

  async function createSchedule() {
    try {
      await withError(createAvailabilitySchedule(formData));
      toast.success("Schedule created");
    } catch (error: any) {
      toast.error(error?.message || "Failed to create schedule");
    }
  }

  async function updateSchedule() {
    try {
      await withError(
        updateAvailabilitySchedule(editingSchedule!.id, formData)
      );
      toast.success("Schedule updated");
    } catch (error: any) {
      toast.error(error?.message || "Failed to update schedule");
    }
  }

  const formatSchedulePeriod = (schedule: ProductAvailability) => {
    switch (schedule.type) {
      case "DAILY_RECURRING":
        return "Every Day";
      case "WEEKLY_RECURRING":
        return schedule.dayOfWeek
          ? DAYS_OF_WEEK.find((d) => d.value === schedule.dayOfWeek)?.label
          : "Unknown";
      case "DATE_RANGE":
        return `From ${schedule.startDate} to ${schedule.endDate}`;
      case "ONE_TIME":
        return `On ${schedule.startDate}`;
      default:
        return "Unknown";
    }
  };

  const formatTimeRange = (schedule: ProductAvailability) => {
    if (schedule.startTime && schedule.endTime) {
      return `${schedule.startTime} - ${schedule.endTime}`;
    }
    return "All Day";
  };

  return (
    <TooltipProvider>
      <div className="space-y-6">
        <div className="space-y-2">
          <h1 className="text-2xl font-bold">
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={() => router.back()}
              className="mr-2"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            Product Availability Management
          </h1>
          <p className="text-muted-foreground">
            Manage when {`"`}
            {product.name}
            {`"`} is available for purchase
          </p>
        </div>

        {/* Main Availability Control */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5 -mt-0.5" />
              Default Availability
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label
                  htmlFor="always-available"
                  className="text-base font-medium"
                >
                  Product is generally available
                </Label>
                <p className="text-sm text-muted-foreground">
                  {alwaysAvailable
                    ? "Product is available by default, unless specific unavailability periods are defined below."
                    : "Product is unavailable by default, you must define when it's available below."}
                </p>
              </div>
              <Switch
                id="always-available"
                checked={alwaysAvailable}
                onCheckedChange={handleToggleAlwaysAvailable}
              />
            </div>

            <div className="flex gap-2">
              <Button
                onClick={() => handleAddSchedule()}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Schedule
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* List of Schedules */}
        <Card>
          <CardHeader>
            <CardTitle>Availability Schedules</CardTitle>
            <CardDescription>
              Specific periods that override the default availability setting
            </CardDescription>
          </CardHeader>
          <CardContent>
            {availabilitySchedules.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No specific availability schedules defined.</p>
                <p className="text-sm">Use the button above to add one.</p>
              </div>
            ) : (
              <div className="space-y-3">
                {availabilitySchedules.map((schedule) => (
                  <div key={schedule.id} className="p-4 border rounded-lg">
                    <div
                      className="flex items-center justify-between "
                    >
                      <div className="flex-1 space-y-1">
                        <div className="flex items-center gap-3 mb-2">
                          <Badge variant="outline">
                            {
                              AVAILABILITY_TYPES.find(
                                (t) => t.value === schedule.type
                              )?.label
                            }
                          </Badge>
                          <Badge
                            variant={
                              schedule.isAvailable ? "default" : "destructive"
                            }
                          >
                            {schedule.isAvailable ? "Available" : "Unavailable"}
                          </Badge>
                        </div>
                        <div className="text-sm space-y-1">
                          <div>
                            <strong>Period:</strong>{" "}
                            {formatSchedulePeriod(schedule)}
                          </div>
                          <div>
                            <strong>Time:</strong> {formatTimeRange(schedule)}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditSchedule(schedule)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>
                                Delete Schedule
                              </AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to delete this
                                availability schedule? This action cannot be
                                undone.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() =>
                                  handleDeleteSchedule(schedule.id)
                                }
                              >
                                Delete
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </div>
                    <Separator className="my-2" />
                    <div className="flex justify-between">
                      <div className="max-w-md">
                        {schedule.beforeMessage && (
                          <div className="flex flex-col mt-2">
                            <strong>Before Message:</strong>
                            <span className="">{schedule.beforeMessage}</span>
                          </div>
                        )}
                      </div>
                      <div className="max-w-md">
                        {schedule.beforeMessage && (
                          <div className="flex flex-col mt-2">
                            <strong>After Message:</strong>
                            <span className="">{schedule.afterMessage}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Add/Edit Modal */}
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {editingSchedule
                  ? "Edit Availability Schedule"
                  : "Add Availability Schedule"}
              </DialogTitle>
              <DialogDescription>
                Define when the product should be available or unavailable for
                purchase.
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-6">
              {/* Step 1: Schedule Type */}
              <div className="space-y-3">
                <Label className="text-base font-medium">Schedule Type</Label>
                <RadioGroup
                  value={formData.type}
                  onValueChange={(value: AvailabilityType) =>
                    setFormData((prev) => ({
                      ...prev,
                      type: value,
                      priority: getDefaultPriority(value)
                    }))
                  }
                >
                  {AVAILABILITY_TYPES.map((type) => (
                    <div
                      key={type.value}
                      className="flex items-center space-x-2"
                    >
                      <RadioGroupItem value={type.value} id={type.value} />
                      <Label
                        htmlFor={type.value}
                        className="flex-1 cursor-pointer"
                      >
                        <div className="font-medium">{type.label}</div>
                        <div className="text-sm text-muted-foreground">
                          {type.description}
                        </div>
                      </Label>
                    </div>
                  ))}
                </RadioGroup>
              </div>

              {/* Step 2: Schedule Details */}
              <div className="space-y-4">
                <Label className="text-base font-medium">
                  Schedule Details
                </Label>

                {formData.type === "DAILY_RECURRING" && (
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="start-time">Start Time</Label>
                      <Input
                        id="start-time"
                        type="time"
                        value={formData.startTime || ""}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            startTime: e.target.value,
                          }))
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="end-time">End Time</Label>
                      <Input
                        id="end-time"
                        type="time"
                        value={formData.endTime || ""}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            endTime: e.target.value,
                          }))
                        }
                      />
                    </div>
                  </div>
                )}

                {formData.type === "WEEKLY_RECURRING" && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label>Select Day of Week</Label>
                      <RadioGroup
                        value={formData.dayOfWeek || ""}
                        onValueChange={(value: DayOfWeek) =>
                          setFormData((prev) => ({ ...prev, dayOfWeek: value }))
                        }
                      >
                        <div className="grid grid-cols-2 gap-2">
                          {DAYS_OF_WEEK.map((day) => (
                            <div
                              key={day.value}
                              className="flex items-center space-x-2"
                            >
                              <RadioGroupItem
                                value={day.value}
                                id={day.value}
                              />
                              <Label
                                htmlFor={day.value}
                                className="cursor-pointer"
                              >
                                {day.label}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </RadioGroup>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="weekly-start-time">Start Time</Label>
                        <Input
                          id="weekly-start-time"
                          type="time"
                          value={formData.startTime || ""}
                          onChange={(e) =>
                            setFormData((prev) => ({
                              ...prev,
                              startTime: e.target.value,
                            }))
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="weekly-end-time">End Time</Label>
                        <Input
                          id="weekly-end-time"
                          type="time"
                          value={formData.endTime || ""}
                          onChange={(e) =>
                            setFormData((prev) => ({
                              ...prev,
                              endTime: e.target.value,
                            }))
                          }
                        />
                      </div>
                    </div>
                  </div>
                )}

                {formData.type === "DATE_RANGE" && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="start-date">From Date</Label>
                        <Input
                          id="start-date"
                          type="date"
                          value={(formData.startDate as any) || ""}
                          onChange={(e) =>
                            setFormData((prev) => ({
                              ...prev,
                              startDate: e.target.value as any,
                            }))
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="end-date">To Date</Label>
                        <Input
                          id="end-date"
                          type="date"
                          value={(formData.endDate as any) || ""}
                          onChange={(e) =>
                            setFormData((prev) => ({
                              ...prev,
                              endDate: e.target.value as any,
                            }))
                          }
                        />
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="specific-times-range"
                        checked={!!(formData.startTime && formData.endTime)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setFormData((prev) => ({
                              ...prev,
                              startTime: "09:00",
                              endTime: "17:00",
                            }));
                          } else {
                            setFormData((prev) => ({
                              ...prev,
                              startTime: "",
                              endTime: "",
                            }));
                          }
                        }}
                      />
                      <Label htmlFor="specific-times-range">
                        Apply specific times for this range
                      </Label>
                    </div>
                    {formData.startTime && formData.endTime && (
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="range-start-time">Start Time</Label>
                          <Input
                            id="range-start-time"
                            type="time"
                            value={formData.startTime}
                            onChange={(e) =>
                              setFormData((prev) => ({
                                ...prev,
                                startTime: e.target.value,
                              }))
                            }
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="range-end-time">End Time</Label>
                          <Input
                            id="range-end-time"
                            type="time"
                            value={formData.endTime}
                            onChange={(e) =>
                              setFormData((prev) => ({
                                ...prev,
                                endTime: e.target.value,
                              }))
                            }
                          />
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {formData.type === "ONE_TIME" && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="one-time-date">On Date</Label>
                      <Input
                        id="one-time-date"
                        type="date"
                        value={(formData.startDate as any) || ""}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            startDate: e.target.value as any,
                          }))
                        }
                      />
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="specific-times-one"
                        checked={!!(formData.startTime && formData.endTime)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setFormData((prev) => ({
                              ...prev,
                              startTime: "09:00",
                              endTime: "17:00",
                            }));
                          } else {
                            setFormData((prev) => ({
                              ...prev,
                              startTime: "",
                              endTime: "",
                            }));
                          }
                        }}
                      />
                      <Label htmlFor="specific-times-one">
                        Apply specific times for this day
                      </Label>
                    </div>
                    {formData.startTime && formData.endTime && (
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="one-start-time">Start Time</Label>
                          <Input
                            id="one-start-time"
                            type="time"
                            value={formData.startTime}
                            onChange={(e) =>
                              setFormData((prev) => ({
                                ...prev,
                                startTime: e.target.value,
                              }))
                            }
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="one-end-time">End Time</Label>
                          <Input
                            id="one-end-time"
                            type="time"
                            value={formData.endTime}
                            onChange={(e) =>
                              setFormData((prev) => ({
                                ...prev,
                                endTime: e.target.value,
                              }))
                            }
                          />
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Step 3: Availability Status */}
              <div className="space-y-4">
                <Label className="text-base font-medium">
                  Availability Status
                </Label>
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <Label htmlFor="is-available" className="font-medium">
                      Product is available during this period
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Toggle off to mark this period as unavailable
                    </p>
                  </div>
                  <Switch
                    id="is-available"
                    checked={formData.isAvailable}
                    onCheckedChange={(checked) =>
                      setFormData((prev) => ({ ...prev, isAvailable: checked }))
                    }
                  />
                </div>

                {/* Priority Field */}
                <div className="space-y-2">
                  <Label htmlFor="priority" className="text-base font-medium">
                    Priority
                  </Label>
                  <div className="space-y-2">
                    <Input
                      id="priority"
                      type="number"
                      min="1"
                      max="100"
                      value={formData.priority || 1}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          priority: parseInt(e.target.value) || 1,
                        }))
                      }
                    />
                    <p className="text-sm text-muted-foreground">
                      Higher numbers have higher priority. Use this to control which schedule takes precedence when multiple schedules apply.
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="unavailability-message">
                    Unavailable Before Message
                  </Label>
                  <Textarea
                    id="unavailability-before-message"
                    placeholder="একটু পরে পাওয়া যাবে"
                    value={formData.beforeMessage || ""}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        beforeMessage: e.target.value,
                      }))
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="unavailability-message">
                    Unavailable After Message
                  </Label>
                  <Textarea
                    id="unavailability-after-message"
                    placeholder="আজকে আর পাওয়া যাবে না"
                    value={formData.afterMessage || ""}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        afterMessage: e.target.value,
                      }))
                    }
                  />
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsModalOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveSchedule}>
                {editingSchedule ? "Update Schedule" : "Save Schedule"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </TooltipProvider>
  );
}
