"use server";

import { getPrisma } from "@udoy/utils/db-utils";
import { ActionError } from "@udoy/utils/app-error";
import { revalidatePath } from "next/cache";
import { Role } from "@prisma/client";
import { <PERSON>ieUtil } from "@udoy/utils/cookie-util";
import { AvailabilityFormInput, availabilityFormSchema } from "./schema";

export async function changeAlwaysAvailable(
  productId: string,
  alwaysAvailable: boolean
) {
  try {
    const prisma = getPrisma();

    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const product = await prisma.product.update({
      where: { id: productId },
      data: {
        alwaysAvailable,
      },
      include: {
        category: true,
      },
    });

    revalidatePath(`/dashboard/products/${productId}/availability`);
    revalidatePath(`/${product.category?.slug}`);
    product.slug &&
      revalidatePath(`/${product.category?.slug}/${product.slug}`);
    return true;
  } catch (error) {
    console.log(error);
    return ActionError("Failed To Update Product Availability");
  }
}

export async function createAvailabilitySchedule(data: AvailabilityFormInput) {
  try {
    const prisma = getPrisma();
    const scheduleData = availabilityFormSchema.parse(data);

    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const schedule = await prisma.productAvailability.create({
      data: {
        productId: scheduleData.productId,
        type: scheduleData.type,
        dayOfWeek: scheduleData.dayOfWeek,
        startTime: scheduleData.startTime,
        endTime: scheduleData.endTime,
        startDate: scheduleData.startDate,
        endDate: scheduleData.endDate,
        isAvailable: scheduleData.isAvailable,
        beforeMessage: scheduleData.beforeMessage,
        afterMessage: scheduleData.afterMessage,
        priority: scheduleData.priority,
      },

      include: {
        product: {
          include: {
            category: true,
          },
        },
      },
    });

    revalidatePath(`/dashboard/products/${data.productId}/availability`);
    revalidatePath(`/${schedule.product.category?.slug}`);
    schedule.product.slug &&
      revalidatePath(
        `/${schedule.product.category?.slug}/${schedule.product.slug}`
      );
    return schedule;
  } catch (error) {
    console.log(error);
    return ActionError("Failed To Create Availability Schedule");
  }
}

export async function deleteAvailabilitySchedule(scheduleId: string) {
  try {
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();
    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const schedule = await prisma.productAvailability.delete({
      where: { id: scheduleId },
      include: {
        product: {
          include: {
            category: true,
          },
        },
      },
    });

    revalidatePath(`/dashboard/products/${schedule.product.id}/availability`);
    revalidatePath(`/${schedule.product.category?.slug}`);
    schedule.product.slug &&
      revalidatePath(
        `/${schedule.product.category?.slug}/${schedule.product.slug}`
      );
    return true;
  } catch (error) {
    console.log(error);
    return ActionError("Failed To Delete Availability Schedule");
  }
}

export async function updateAvailabilitySchedule(
  scheduleId: string,
  data: AvailabilityFormInput
) {
  try {
    const prisma = getPrisma();
    const scheduleData = availabilityFormSchema.parse(data);

    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const schedule = await prisma.productAvailability.update({
      where: { id: scheduleId },
      data: {
        type: scheduleData.type,
        dayOfWeek: scheduleData.dayOfWeek,
        startTime: scheduleData.startTime,
        endTime: scheduleData.endTime,
        startDate: scheduleData.startDate,
        endDate: scheduleData.endDate,
        isAvailable: scheduleData.isAvailable,
        beforeMessage: scheduleData.beforeMessage,
        afterMessage: scheduleData.afterMessage,
        priority: scheduleData.priority,
      },
      include: {
        product: {
          include: {
            category: true,
          },
        },
      },
    });

    revalidatePath(`/dashboard/products/${schedule.product.id}/availability`);
    revalidatePath(`/${schedule.product.category?.slug}`);
    schedule.product.slug &&
      revalidatePath(
        `/${schedule.product.category?.slug}/${schedule.product.slug}`
      );
    return schedule;
  } catch (error) {
    console.log(error);
    return ActionError("Failed To Update Availability Schedule");
  }
}
