import { AvailabilityType, DayOfWeek } from "@prisma/client";
import { z } from "zod";

export const availabilityFormSchema = z.object({
  productId: z.string().cuid(),
  type: z.enum([
    AvailabilityType.DAILY_RECURRING,
    AvailabilityType.WEEKLY_RECURRING,
    AvailabilityType.DATE_RANGE,
    AvailabilityType.ONE_TIME,
  ]),
  dayOfWeek: z.optional(
    z.enum([
      DayOfWeek.SUNDAY,
      DayOfWeek.MONDAY,
      DayOfWeek.TUESDAY,
      DayOfWeek.WEDNESDAY,
      DayOfWeek.THURSDAY,
      DayOfWeek.FRIDAY,
      DayOfWeek.SATURDAY,
    ])
  ),
  startTime: z.string().optional(),
  endTime: z.string().optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  isAvailable: z.boolean(),
  beforeMessage: z.string().optional(),
  afterMessage: z.string().optional(),
  priority: z.number().int().min(1).max(100).default(1),
});

export type AvailabilityFormInput = z.input<typeof availabilityFormSchema>;
