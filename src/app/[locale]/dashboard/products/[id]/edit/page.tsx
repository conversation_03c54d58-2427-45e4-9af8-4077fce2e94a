import { ProductForm } from "../../components/product-form";
import { getPrisma } from "@udoy/utils/db-utils";

export default async function EditProductPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;

  // Find the product by ID
  const prisma = getPrisma();
  const product = await prisma.product.findUnique({
    where: { id },
    include: { images: true, unit: true, category: true },
  });

  if (!product) {
    return <div>Product not found</div>;
  }

  const categories = await prisma.category.findMany({
    where: {
      OR: [
        // Categories that have base subcategories
        {
          subCategories: {
            some: {
              isBase: true,
            },
          },
        },
        // Categories that are themselves base categories with no parent
        {
          isBase: true,
          parentId: null,
        },
      ],
    },

    include: {
      subCategories: {
        where: {
          isBase: true,
        },
      },
    },
  });
  const quantityUnits = await prisma.quantityUnit.findMany();
  const shops = await prisma.shop.findMany();
  const companies = await prisma.company.findMany();

  return (
    <ProductForm
      categories={categories}
      quantityUnits={quantityUnits}
      product={product}
      shops={shops}
      companies={companies}
    />
  );
}
