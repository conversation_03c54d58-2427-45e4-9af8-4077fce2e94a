import { Skeleton } from "@udoy/components/ui/skeleton";
import { Card, CardContent, CardHeader } from "@udoy/components/ui/card";
import { Ta<PERSON>, TabsContent, <PERSON><PERSON>List, TabsTrigger } from "@udoy/components/ui/tabs";
import { Separator } from "@udoy/components/ui/separator";

export default function Loading() {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Skeleton className="h-10 w-10" />
          <Skeleton className="h-10 w-[200px]" />
        </div>
        <div className="flex items-center gap-2">
          <Skeleton className="h-10 w-[100px]" />
          <Skeleton className="h-10 w-[120px]" />
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Product images */}
        <Card className="md:col-span-1">
          <CardContent className="p-4">
            <Skeleton className="aspect-square w-full rounded-md" />
            <div className="mt-4 grid grid-cols-4 gap-2">
              {Array.from({ length: 4 }).map((_, index) => (
                <Skeleton
                  key={index}
                  className="aspect-square w-full rounded-md"
                />
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Product details */}
        <Card className="md:col-span-2">
          <CardHeader>
            <Skeleton className="h-6 w-[150px] mb-2" />
            <Skeleton className="h-4 w-[250px]" />
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-4 md:grid-cols-2">
              {Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="space-y-2">
                  <Skeleton className="h-4 w-[100px]" />
                  <Skeleton className="h-6 w-[150px]" />
                </div>
              ))}
            </div>

            <Separator />

            <Tabs defaultValue="english">
              <TabsList>
                <TabsTrigger value="english">English</TabsTrigger>
                <TabsTrigger value="bengali">Bengali</TabsTrigger>
              </TabsList>
              <TabsContent value="english" className="pt-4">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-[100px]" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Product stats */}
        {Array.from({ length: 3 }).map((_, index) => (
          <Card key={index}>
            <CardHeader className="pb-2">
              <Skeleton className="h-5 w-[120px]" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-[100px] mb-1" />
              <Skeleton className="h-4 w-[150px]" />
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
