import { ProductUnits } from "../../components/product-units";

// Sample data for quantity units
const units = [
  { id: "unit_1", slug: "kg", full: "Kilogram", productCount: 15 },
  { id: "unit_2", slug: "g", full: "Gram", productCount: 8 },
  { id: "unit_3", slug: "l", full: "Liter", productCount: 12 },
  { id: "unit_4", slug: "ml", full: "Milliliter", productCount: 5 },
  { id: "unit_5", slug: "pcs", full: "Pieces", productCount: 25 },
  { id: "unit_6", slug: "pack", full: "Pack", productCount: 10 },
  { id: "unit_7", slug: "box", full: "Box", productCount: 7 },
  { id: "unit_8", slug: "dozen", full: "Dozen", productCount: 3 },
];

export default function ProductUnitsPage() {
  return (
    <div className="flex flex-col gap-6">
      <h1 className="text-3xl font-bold tracking-tight">Quantity Units</h1>
      <p className="text-muted-foreground">
        Manage quantity units for products to specify how products are measured
        or sold.
      </p>
      <ProductUnits units={units} />
    </div>
  );
}
