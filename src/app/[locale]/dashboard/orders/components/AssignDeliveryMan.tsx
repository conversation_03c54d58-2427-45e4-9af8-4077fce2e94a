"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@udoy/components/ui/dropdown-menu";
import useStatus from "@udoy/hooks/useToastUtil";
import { dbStore } from "@udoy/state";
import { useSelector } from "@xstate/store/react";

import React from "react";
import { assignDeliveryMan } from "../actions";
import { withError } from "@udoy/utils/app-error";
import { cn } from "@udoy/utils/shadcn";
import { Order } from "@prisma/client";

function AssignDeliveryMan({ order }: { order: Order }) {
  const deliveryMen = useSelector(
    dbStore,
    (state) => state.context.deliveryMen
  );
  const status = useStatus();

  async function handleAssingDeliveryMan(userId: number) {
    try {
      status.loading("Assigning Delivery Man...");
      withError(assignDeliveryMan(order.id, userId));
      status.success("Delivery Man Assigned Successfully");
    } catch (error) {
      console.log(error);
      status.error("Failed To Assign Delivery Man");
    }
  }

  return (
    <div>
      <DropdownMenuSub>
        <DropdownMenuSubTrigger>Assign</DropdownMenuSubTrigger>
        <DropdownMenuPortal>
          <DropdownMenuSubContent>
            <DropdownMenuLabel>Delivery Men</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {deliveryMen.map((user) => (
              <DropdownMenuItem
                onClick={() => handleAssingDeliveryMan(user.id)}
                key={user.id}
                className={cn(order.deliveryManId === user.id && "bg-brand/10")}
              >
                <div className="flex flex-col w-full">
                  <div className="flex justify-between">
                    <span className="text-left">{user.name}</span>
                    <span>{(user as any)._count.deliveries}</span>
                  </div>
                  <span className="text-xs text-muted-foreground">
                    {user.email}
                  </span>
                </div>
              </DropdownMenuItem>
            ))}
          </DropdownMenuSubContent>
        </DropdownMenuPortal>
      </DropdownMenuSub>
    </div>
  );
}

export default AssignDeliveryMan;
