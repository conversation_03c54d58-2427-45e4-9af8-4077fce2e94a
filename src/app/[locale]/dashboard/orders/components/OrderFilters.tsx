"use client";

import { useRouter, usePathname } from "next/navigation";
import { But<PERSON> } from "@udoy/components/ui/button";
import { Input } from "@udoy/components/ui/input";
import { Search, CalendarIcon } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@udoy/components/ui/popover";
import { Calendar } from "@udoy/components/ui/calendar";
import { format } from "date-fns";
import { cn } from "@udoy/utils/shadcn";
import { useEffect, useState } from "react";
import { OrderStatus } from "@prisma/client";

interface OrderFiltersProps {
  search?: string;
  status?: string;
  date?: string;
  sort?: string;
}

export function OrderFilters({
  search = "",
  status = "",
  date = "",
  sort = "newest",
}: OrderFiltersProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [searchValue, setSearchValue] = useState(search);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    date ? new Date(date) : undefined
  );

  // Debounce search input

  const updateFilters = (newParams: {
    [key: string]: string | null | undefined;
  }) => {
    const url = new URL(pathname, window.location.origin);
    const params = new URLSearchParams(window.location.search);

    // Update or remove params
    Object.entries(newParams).forEach(([key, value]) => {
      if (value === null || value === undefined || value === "") {
        params.delete(key);
      } else {
        params.set(key, value);
      }
    });

    // Navigate to the new URL
    router.push(`${pathname}?${params.toString()}`);
  };

  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date);
    updateFilters({ date: date ? date.toISOString().split("T")[0] : null });
  };

  return (
    <div className="flex flex-col gap-4 md:flex-row md:items-center">
      <div className="relative flex-1 md:max-w-sm">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          type="search"
          placeholder="Search orders by ID, customer..."
          className="pl-8"
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              updateFilters({ search: searchValue });
            }
          }}
        />
      </div>
      <div className="flex flex-wrap items-center gap-2">
        <Select
          defaultValue={status || "ALL"}
          onValueChange={(value) => updateFilters({ status: value })}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Order Status</SelectLabel>
              <SelectItem value="ALL">All Statuses</SelectItem>
              <SelectItem value={OrderStatus.PENDING}>Pending</SelectItem>
              <SelectItem value={OrderStatus.CONFIRMED}>Confirmed</SelectItem>
              <SelectItem value={OrderStatus.SHIPPING}>In Delivery</SelectItem>
              <SelectItem value={OrderStatus.CANCELLED}>Cancelled</SelectItem>
              <SelectItem value={OrderStatus.DELIVERED}>Delivered</SelectItem>
              <SelectItem value={OrderStatus.RETURNED}>Returned</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>

        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant={"outline"}
              className={cn(
                "w-[180px] justify-start text-left font-normal",
                !selectedDate && "text-muted-foreground"
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {selectedDate ? (
                format(selectedDate, "PPP")
              ) : (
                <span>Filter by date</span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0">
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={handleDateSelect}
              initialFocus
            />
            {selectedDate && (
              <div className="flex items-center justify-center p-2 border-t">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDateSelect(undefined)}
                >
                  Clear
                </Button>
              </div>
            )}
          </PopoverContent>
        </Popover>

        <Select
          defaultValue={sort}
          onValueChange={(value) => updateFilters({ sort: value })}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Sort By</SelectLabel>
              <SelectItem value="newest">Newest First</SelectItem>
              <SelectItem value="oldest">Oldest First</SelectItem>
              <SelectItem value="total-high">Highest Total</SelectItem>
              <SelectItem value="total-low">Lowest Total</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
