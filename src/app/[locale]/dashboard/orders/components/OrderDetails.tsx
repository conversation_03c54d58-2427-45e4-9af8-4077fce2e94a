"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { format } from "date-fns";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>er, Minus, Trash, Plus } from "lucide-react";
import { useRouter } from "next/navigation";
import { Order, OrderStatus, OrderTimeline } from "@prisma/client";

import { Button } from "@udoy/components/ui/button";
import { Badge } from "@udoy/components/ui/badge";
import { Separator } from "@udoy/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@udoy/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@udoy/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import { cn } from "@udoy/utils/shadcn";
import { OrderFullInterface } from "@udoy/utils/types";
import { withError } from "@udoy/utils/app-error";
import {
  addOrderItemFromCart,
  deleteOrderItem,
  updateOrderItemQuantity,
} from "../actions";
import { toast } from "sonner";
import { OrderTimeline as OrderTimelineComponent } from "../../components/order-timeline";
import { StatusUpdateForm } from "../../components/status-update-form";
import { QuantityEditModal } from "./QuantityEditModal";

// Helper function to format currency
function formatCurrency(amount: number) {
  return new Intl.NumberFormat("bn-BD", {
    style: "currency",
    currency: "BDT",
    minimumFractionDigits: 0,
  }).format(amount);
}

// Helper function to get status badge variant
function getStatusBadgeVariant(status: string) {
  switch (status) {
    case "DELIVERED":
      return "default";
    case "PROCESSING":
      return "secondary";
    case "PENDING":
      return "outline";
    case "CANCELLED":
      return "destructive";
    case "DELIVERY":
      return "warning";
    case "RETURNED":
      return "destructive";
    default:
      return "outline";
  }
}

// Helper function to format date
function formatDate(date: Date) {
  return format(date, "MMM dd, yyyy h:mm a");
}

export function OrderDetails({
  order,
}: {
  order: OrderFullInterface & { address: { zone: { name: string } } } & {
    timeline: OrderTimeline[];
  };
}) {
  const router = useRouter();
  const [quantityEditModal, setQuantityEditModal] = useState<{
    isOpen: boolean;
    orderItem: any;
  }>({
    isOpen: false,
    orderItem: null,
  });

  // Handle quantity decrement
  const handleDecrement = async (itemId: number) => {
    try {
      const currentItem = order.orderItems.find((item) => item.id === itemId)!;
      const newQuantity = Math.max(0.01, currentItem.quantity - 1);

      await withError(
        updateOrderItemQuantity({
          itemId,
          orderId: order.id,
          quantity: newQuantity,
        })
      );
      toast.success("Item quantity updated");
    } catch (error: any) {
      toast.error(error?.message || "Failed to update item quantity");
    }
  };

  // Handle item deletion
  const handleDelete = async (itemId: number) => {
    try {
      await withError(deleteOrderItem({ itemId, orderId: order.id }));
      toast.success("Item deleted");
    } catch (error: any) {
      toast.error(error?.message || "Failed to delete item");
    }
  };

  // Handle quantity increment
  const handleIncrement = async (itemId: number) => {
    try {
      const currentItem = order.orderItems.find((item) => item.id === itemId)!;
      const newQuantity = currentItem.quantity + 1;

      await withError(
        updateOrderItemQuantity({
          itemId,
          orderId: order.id,
          quantity: newQuantity,
        })
      );
      toast.success("Item quantity updated");
    } catch (error: any) {
      toast.error(error?.message || "Failed to update item quantity");
    }
  };

  const handleAddFromCart = async () => {
    try {
      await withError(addOrderItemFromCart({ orderId: order.id }));
      toast.success("Items added from cart");
    } catch (error: any) {
      toast.error(error?.message || "Failed to add items from cart");
    }
  };

  const handleOpenQuantityModal = (orderItem: any) => {
    setQuantityEditModal({
      isOpen: true,
      orderItem,
    });
  };

  const handleCloseQuantityModal = () => {
    setQuantityEditModal({
      isOpen: false,
      orderItem: null,
    });
  };

  const handleQuantityUpdated = () => {
    // Refresh the page to show updated data
    router.refresh();
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Order #{order.id}
            </h1>
            <p className="text-muted-foreground">
              Placed on {formatDate(order.createdAt)}
            </p>
          </div>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <Button asChild>
            <Link href={`/api/orders/${order.id}/invoice`} target="_blank">
              <Printer className="mr-2 h-4 w-4" />
              Download Invoice
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2">
          {/* <div className="grid gap-6 md:grid-cols-3"> */}
          <Card className="md:col-span-2">
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Order Status</CardTitle>
                <CardDescription>Current status and history</CardDescription>
              </div>
              <Badge
                variant={getStatusBadgeVariant(order.status) as any}
                className={cn(
                  "text-sm py-1 px-3",
                  order.status === OrderStatus.SHIPPING &&
                    "bg-yellow-500 hover:bg-yellow-600"
                )}
              >
                {order.status.replace("_", " ")}
              </Badge>
            </CardHeader>
            <CardContent>
              <OrderTimelineComponent timeline={order.timeline} />
            </CardContent>
            <CardFooter className="flex justify-between border-t pt-6">
              <div className="text-sm text-muted-foreground">
                Update the order status
              </div>
              <StatusUpdateForm
                orderId={order.id}
                currentStatus={order.status}
              />
            </CardFooter>
          </Card>
        </div>

        {/* <Card>
          <CardHeader>
            <CardTitle>Order Summary</CardTitle>
            <CardDescription>Order details and totals</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Subtotal</span>
              <span>{formatCurrency(order.subTotal)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Shipping</span>
              <span>{formatCurrency(order.shipping)}</span>
            </div>
            <Separator />
            <div className="flex justify-between font-medium">
              <span>Total</span>
              <span>{formatCurrency(order.subTotal + order.shipping)}</span>
            </div>
            <Separator />
            <div className="space-y-1">
              <div className="text-sm font-medium">Payment Information</div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Payment Method</span>
                <span>{"Cash On Delivery"}</span>
              </div>
            </div>
          </CardContent>
        </Card> */}

        <Card>
          <CardHeader>
            <CardTitle>Order Summary</CardTitle>
            <CardDescription>Order details and totals</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Subtotal</span>
              <span>{formatCurrency(order.subTotal)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Shipping</span>
              <span>{formatCurrency(order.shipping)}</span>
            </div>
            <Separator />
            <div className="flex justify-between font-medium">
              <span>Total</span>
              <span>{formatCurrency(order.subTotal + order.shipping)}</span>
            </div>
            <Separator />
            <div className="space-y-1">
              <div className="text-sm font-medium">Payment Information</div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Method</span>
                <span>{"Cash On Delivery"}</span>
              </div>
              {/* {order.paymentId && (
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Transaction ID</span>
                  <span>{order.paymentId}</span>
                </div>
              )} */}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Order Details Tabs */}
      <Tabs defaultValue="items">
        <TabsList>
          <TabsTrigger value="items">Order Items</TabsTrigger>
          <TabsTrigger value="customer">Customer</TabsTrigger>
          <TabsTrigger value="shipping">Shipping</TabsTrigger>
        </TabsList>
        <TabsContent value="items" className="mt-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between">
                <CardTitle>Order Items</CardTitle>
                <Button size="sm" onClick={handleAddFromCart}>
                  Add From Cart
                </Button>
              </div>
              <CardDescription>Items included in this order</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Product</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead className="text-right">Unit Price</TableHead>
                    <TableHead className="text-right">Total</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {order.orderItems.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          {item.product.images &&
                            item.product.images.length > 0 && (
                              <div className="relative h-10 w-10 overflow-hidden rounded-md">
                                <Image
                                  src={
                                    item.product.images[0].url ||
                                    "/placeholder.svg"
                                  }
                                  alt={item.product.name}
                                  fill
                                  className="object-cover"
                                />
                              </div>
                            )}
                          <div>
                            <p className="font-medium">{item.product.name}</p>
                            <p className="text-xs text-muted-foreground">
                              ID: {item.productId}
                            </p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleOpenQuantityModal(item)}
                          className="h-auto p-1 font-normal hover:bg-muted"
                        >
                          {item.quantity % 1 === 0 ? item.quantity.toString() : item.quantity.toFixed(2).replace(/\.?0+$/, '')}
                        </Button>
                      </TableCell>
                      <TableCell className="text-right">
                        {formatCurrency(item.price)}
                      </TableCell>
                      <TableCell className="text-right">
                        {formatCurrency(item.price * item.quantity)}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDecrement(item.id)}
                            disabled={item.quantity <= 0.01}
                          >
                            <Minus className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleIncrement(item.id)}
                            disabled={item.quantity >= item.product.supply}
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => handleDelete(item.id)}
                          >
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="customer" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Customer Information</CardTitle>
              <CardDescription>
                Details about the customer who placed this order
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <h4 className="mb-2 text-sm font-medium">
                    Contact Information
                  </h4>
                  <div className="rounded-md border p-4">
                    <p className="font-medium">{order.buyer.name}</p>
                    <p>{order.buyer.email}</p>
                    <p>{order.address.phone}</p>
                  </div>
                </div>
                <div>
                  <h4 className="mb-2 text-sm font-medium">Billing Address</h4>
                  <div className="rounded-md border p-4">
                    <p className="font-medium">{order.address.name}</p>
                    <p>{order.address.home}</p>
                    <p>{order.address.zone.name}</p>
                    <p>{order.address.phone}</p>
                    {order.address.location && (
                      <p className="text-muted-foreground">
                        {order.address.location}
                      </p>
                    )}
                  </div>
                </div>
              </div>
              <div className="flex justify-end">
                <Button
                  variant="outline"
                  onClick={() =>
                    router.push(`/dashboard/customers/${order.buyer.id}`)
                  }
                >
                  View Customer Profile
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="shipping" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Shipping Information</CardTitle>
              <CardDescription>
                Delivery details and shipping address
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <h4 className="mb-2 text-sm font-medium">Shipping Address</h4>
                  <div className="rounded-md border p-4">
                    <p className="font-medium">{order.address.name}</p>
                    <p>{order.address.home}</p>
                    <p>{order.address.zone.name}</p>
                    <p>{order.address.phone}</p>
                    {order.address.location && (
                      <p className="text-muted-foreground">
                        {order.address.location}
                      </p>
                    )}
                  </div>
                </div>
                <div>
                  <h4 className="mb-2 text-sm font-medium">Shipping Method</h4>
                  <div className="rounded-md border p-4">
                    <p className="font-medium">Standard Shipping</p>
                    <p className="text-muted-foreground">
                      Estimated delivery: 2-3 business days
                    </p>
                    <p className="mt-2">{formatCurrency(order.shipping)}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Quantity Edit Modal */}
      {quantityEditModal.orderItem && (
        <QuantityEditModal
          isOpen={quantityEditModal.isOpen}
          onClose={handleCloseQuantityModal}
          orderItem={quantityEditModal.orderItem}
          orderId={order.id}
          onQuantityUpdated={handleQuantityUpdated}
        />
      )}
    </div>
  );
}
