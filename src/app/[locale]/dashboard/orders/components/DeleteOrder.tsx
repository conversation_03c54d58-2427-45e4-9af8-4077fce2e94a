"use client";

import React from "react";
import { toast } from "sonner";
import { deleteOrder } from "../actions";
import { withError } from "@udoy/utils/app-error";
import {
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@udoy/components/ui/alert-dialog";

function DeleteOrder({ id }: { id: number }) {
  async function handleDeleteOrder() {
    try {
      const result = await withError(deleteOrder(id));
      if (result) {
        toast.success("Order deleted");
      }
    } catch (error: any) {
      toast.error(error?.message || "Failed to delete order");
    }
  }
  return (
    <AlertDialogContent>
      <AlertDialogHeader>
        <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
        <AlertDialogDescription>
          This action cannot be undone. This will permanently delete the order
          from the database.
        </AlertDialogDescription>
      </AlertDialogHeader>
      <AlertDialogFooter>
        <AlertDialogCancel>Cancel</AlertDialogCancel>
        <AlertDialogAction onClick={handleDeleteOrder}>
          Continue
        </AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  );
}

export default DeleteOrder;
