"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@udoy/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@udoy/components/ui/form";
import { Input } from "@udoy/components/ui/input";
import { Button } from "@udoy/components/ui/button";
import { toast } from "sonner";
import { withError } from "@udoy/utils/app-error";
import { updateOrderItemQuantity } from "../actions";

const quantitySchema = z.object({
  quantity: z
    .number()
    .min(0.01, "Quantity must be greater than 0")
    .max(999.99, "Quantity cannot exceed 999.99"),
});

type QuantityFormValues = z.infer<typeof quantitySchema>;

interface QuantityEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  orderItem: {
    id: number;
    quantity: number;
    product: {
      name: string;
    };
  };
  orderId: number;
  onQuantityUpdated?: () => void;
}

export function QuantityEditModal({
  isOpen,
  onClose,
  orderItem,
  orderId,
  onQuantityUpdated,
}: QuantityEditModalProps) {
  const [isLoading, setIsLoading] = useState(false);

  // Initialize form with current quantity
  const form = useForm<QuantityFormValues>({
    resolver: zodResolver(quantitySchema),
    defaultValues: {
      quantity: orderItem.quantity,
    },
  });

  // Reset form when modal opens with new data
  React.useEffect(() => {
    if (isOpen) {
      form.reset({
        quantity: orderItem.quantity,
      });
    }
  }, [isOpen, orderItem.quantity, form]);

  // Handle quantity update submission
  async function handleUpdateQuantity(values: QuantityFormValues) {
    setIsLoading(true);
    try {
      const result = await withError(
        updateOrderItemQuantity({
          itemId: orderItem.id,
          orderId: orderId,
          quantity: values.quantity,
        })
      );
      if (result) {
        toast.success("Quantity updated successfully");
        onQuantityUpdated?.();
        onClose();
      }
    } catch (error: any) {
      toast.error(error?.message || "Failed to update quantity");
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Quantity</DialogTitle>
          <DialogDescription>
            Update the quantity for {orderItem.product.name}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleUpdateQuantity)}
            className="space-y-4 py-4"
          >
            <FormField
              control={form.control}
              name="quantity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Quantity</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      min="0.01"
                      max="999.99"
                      placeholder="Enter quantity (e.g., 1.5)"
                      {...field}
                      onChange={(e) => {
                        const value = parseFloat(e.target.value);
                        field.onChange(isNaN(value) ? 0 : value);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Updating..." : "Update Quantity"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
