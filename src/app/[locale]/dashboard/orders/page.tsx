import { Suspense } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@udoy/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@udoy/components/ui/table";
import { Badge } from "@udoy/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@udoy/components/ui/dropdown-menu";
import { MoreHorizontal } from "lucide-react";
import { cn } from "@udoy/utils/shadcn";
import { getPrisma } from "@udoy/utils/db-utils";
import { OrderFilters } from "./components/OrderFilters";
import { buildQuery, FilterParams } from "./utils/queryBuilder";
import { OrderFullInterface, OrderWithItems } from "@udoy/utils/types";
import { OrderStatus, Role } from "@prisma/client";
import Loading from "./loading";
import AssignDeliveryMan from "./components/AssignDeliveryMan";
import SyncDeliveryMen from "./components/SyncDeliveryMen";
import DeleteOrder from "./components/DeleteOrder";
import {
  AlertDialog,
  AlertDialogTrigger,
} from "@udoy/components/ui/alert-dialog";

// import Loading from "./loading";

// Helper function to get status badge variant
function getStatusBadgeVariant(status: string) {
  switch (status) {
    case "DELIVERED":
      return "default";
    case "PROCESSING":
      return "secondary";
    case "PENDING":
      return "outline";
    case "CANCELLED":
      return "destructive";
    case "DELIVERY":
      return "warning";
    case "RETURNED":
      return "destructive";
    default:
      return "outline";
  }
}

// Helper function to format date
function formatDate(date: Date) {
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  }).format(date);
}

export default async function PageWrapper({
  searchParams,
}: {
  searchParams: Promise<FilterParams>;
}) {
  const params = await searchParams;

  return (
    <Suspense key={JSON.stringify(params)} fallback={<Loading />}>
      <OrdersPage searchParams={params} />
    </Suspense>
  );
}

async function OrdersPage({ searchParams }: { searchParams: FilterParams }) {
  const prisma = getPrisma();
  const { whereClause, orderByClause, ...filterProps } =
    buildQuery(searchParams);

  // Fetch orders with filters and sorting
  const orders = await prisma.order.findMany({
    where: whereClause,
    orderBy: orderByClause,
    include: {
      buyer: true,
      address: true,
      deliveryMan: true,
      orderItems: {
        include: {
          product: {
            include: {
              images: true,
            },
          },
        },
      },
    },
    take: 50, // Pagination can be added later
  });

  const deliveryMen = await prisma.user.findMany({
    where: {
      role: {
        in: [Role.DELIVERY_MAN, Role.SUPER_ADMIN, Role.ADMIN],
      },
    },

    include: {
      _count: {
        select: {
          deliveries: true,
        },
      },
    },
  });

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Orders</h1>
      </div>

      <OrderFilters {...filterProps} />

      <SyncDeliveryMen users={deliveryMen}>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[80px]">Order ID</TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Delivery Man</TableHead>
                <TableHead className="text-right">Items</TableHead>
                <TableHead className="text-right">Total</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {orders.length > 0 ? (
                orders.map((order) => <OrderRow key={order.id} order={order} />)
              ) : (
                <TableRow>
                  <TableCell colSpan={7} className="h-24 text-center">
                    No orders found.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </SyncDeliveryMen>
    </div>
  );
}

// Separate component for order row to keep the main component cleaner
function OrderRow({ order }: { order: OrderFullInterface }) {
  return (
    <TableRow>
      <TableCell className="font-medium">#{order.id}</TableCell>
      <TableCell>
        <div>
          <p className="font-medium">{order.buyer.name}</p>
          <p className="text-xs text-muted-foreground">{order.buyer.email}</p>
        </div>
      </TableCell>
      <TableCell>{formatDate(order.createdAt)}</TableCell>
      <TableCell>
        <div>
          <p className="font-medium">
            {order.deliveryMan?.name ?? "Not Assigned"}
          </p>
          <p className="text-xs text-muted-foreground">
            {order.deliveryMan?.email ?? ""}
          </p>
        </div>
      </TableCell>
      <TableCell className="text-right">{order.orderItems.length}</TableCell>
      <TableCell className="text-right font-medium">
        ৳ {(order.subTotal + order.shipping).toLocaleString()}
      </TableCell>
      <TableCell>
        <Badge
          variant={getStatusBadgeVariant(order.status) as any}
          className={cn(
            order.status === OrderStatus.DELIVERED &&
              "bg-green-700 hover:bg-green-600"
          )}
        >
          {order.status.replace("_", " ")}
        </Badge>
      </TableCell>
      <TableCell className="text-right">
        <AlertDialog>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">Open menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>

              <DropdownMenuItem asChild>
                <Link href={`/dashboard/orders/${order.id}`}>View details</Link>
              </DropdownMenuItem>
              <AssignDeliveryMan order={order} />
              <Link href={`/api/orders/${order.id}/invoice`} target="_blank">
                <DropdownMenuItem>Get Invoice</DropdownMenuItem>
              </Link>
              <DropdownMenuSeparator />
              <AlertDialogTrigger asChild>
                <DropdownMenuItem className="bg-destructive text-destructive-foreground mt-2">
                  Delete Order
                </DropdownMenuItem>
              </AlertDialogTrigger>
            </DropdownMenuContent>
          </DropdownMenu>
          <DeleteOrder id={order.id} />
        </AlertDialog>
      </TableCell>
    </TableRow>
  );
}
