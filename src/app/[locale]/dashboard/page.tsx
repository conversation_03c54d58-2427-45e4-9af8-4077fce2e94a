import { DashboardStats } from "./components/dashboard-stats";
import Layout from "./components/Layout";
import { RecentOrders } from "./components/recent-orders";
import SalesOverview from "./components/sales-overview";
import { SummaryOverview } from "./components/summary-overview";

export default async function DashboardPage() {
  return (
    <Layout>
      <div className="flex flex-col gap-6">
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <DashboardStats />
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-7">
          <SummaryOverview />
          <RecentOrders />
        </div>

        {/* Keep the original overview for reference */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-7">
          <SalesOverview />
        </div>
      </div>
    </Layout>
  );
}
