import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardTitle,
} from "@udoy/components/ui/card";
import { PathRevalidationForm } from "./components/path-revalidation-form";
import { TagRevalidationForm } from "./components/tag-revalidation-form";
import { QuickActions } from "./components/quick-actions";
import { RevalidationHistory } from "./components/revalidation-history";

export default function RevalidationDashboard() {
  return (
    <div className="px-4 md:px-8 p-6 space-y-8">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">
          Revalidation Dashboard
        </h1>
        <p className="text-muted-foreground">
          Manage and trigger revalidation for your Next.js application pages and
          cache tags.
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Path Revalidation</CardTitle>
            <CardDescription>
              Revalidate specific pages by their path. This will purge the cache
              for the specified route.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <PathRevalidationForm />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Tag Revalidation</CardTitle>
            <CardDescription>
              Revalidate all cached data associated with a specific cache tag.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <TagRevalidationForm />
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Quickly revalidate common paths and tags.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <QuickActions />
        </CardContent>
      </Card>

      {/* <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>
            History of recent revalidation actions.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <RevalidationHistory />
        </CardContent>
      </Card> */}
    </div>
  );
}
