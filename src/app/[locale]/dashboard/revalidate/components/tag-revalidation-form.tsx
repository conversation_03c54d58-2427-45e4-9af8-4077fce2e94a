"use client";

import type React from "react";
import { useState } from "react";
import { But<PERSON> } from "@udoy/components/ui/button";
import { Input } from "@udoy/components/ui/input";
import { Label } from "@udoy/components/ui/label";
import { Alert, AlertDescription } from "@udoy/components/ui/alert";
import { Loader2, CheckCircle, XCircle } from "lucide-react";
import { revalidateTagAction } from "../actions";
import { withError } from "@udoy/utils/app-error";

export function TagRevalidationForm() {
  const [tag, setTag] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!tag.trim()) return;

    setIsLoading(true);
    setResult(null);

    try {
      // Use withError to handle ActionError pattern
      const response = await withError(revalidateTagAction(tag.trim()));
      setResult(response);
      if (response.success) {
        setTag("");
      }
    } catch (error: any) {
      setResult({
        success: false,
        message: error?.message || "An unexpected error occurred",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="tag">Cache Tag</Label>
          <Input
            id="tag"
            placeholder="posts, users, products"
            value={tag}
            onChange={(e) => setTag(e.target.value)}
            disabled={isLoading}
          />
          <p className="text-sm text-muted-foreground">
            Enter the cache tag used in your fetch requests or data functions
          </p>
        </div>
        <Button type="submit" disabled={isLoading || !tag.trim()}>
          {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Revalidate Tag
        </Button>
      </form>

      {result && (
        <Alert variant={result.success ? "default" : "destructive"}>
          {result.success ? (
            <CheckCircle className="h-4 w-4" />
          ) : (
            <XCircle className="h-4 w-4" />
          )}
          <AlertDescription>{result.message}</AlertDescription>
        </Alert>
      )}
    </div>
  );
}
