"use client";

import { useState } from "react";
import { toast } from "sonner";
import { withError } from "@udoy/utils/app-error";
import { revalidatePathAction, revalidateTagAction } from "../actions";
import { Button } from "@udoy/components/ui/button";
import { Input } from "@udoy/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@udoy/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@udoy/components/ui/card";

export function RevalidateForm() {
  const [path, setPath] = useState("");
  const [tag, setTag] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  async function handleRevalidatePath(e: React.FormEvent) {
    e.preventDefault();
    setIsLoading(true);
    
    try {
      const result = await withError(revalidatePathAction(path));
      toast.success("Success", {
        description: result.message,
      });
      setPath("");
    } catch (error: any) {
      toast.error("Error", {
        description: error?.message || "Failed to revalidate path",
      });
    } finally {
      setIsLoading(false);
    }
  }

  async function handleRevalidateTag(e: React.FormEvent) {
    e.preventDefault();
    setIsLoading(true);
    
    try {
      const result = await withError(revalidateTagAction(tag));
      toast.success("Success", {
        description: result.message,
      });
      setTag("");
    } catch (error: any) {
      toast.error("Error", {
        description: error?.message || "Failed to revalidate tag",
      });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Cache Revalidation</CardTitle>
        <CardDescription>
          Revalidate paths or tags to refresh cached data
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="path">
          <TabsList className="mb-4">
            <TabsTrigger value="path">Revalidate Path</TabsTrigger>
            <TabsTrigger value="tag">Revalidate Tag</TabsTrigger>
          </TabsList>
          
          <TabsContent value="path">
            <form onSubmit={handleRevalidatePath} className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="path" className="text-sm font-medium">
                  Path
                </label>
                <Input
                  id="path"
                  placeholder="/dashboard/products"
                  value={path}
                  onChange={(e) => setPath(e.target.value)}
                  required
                />
                <p className="text-xs text-muted-foreground">
                  Path must start with a forward slash (/)
                </p>
              </div>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Revalidating..." : "Revalidate Path"}
              </Button>
            </form>
          </TabsContent>
          
          <TabsContent value="tag">
            <form onSubmit={handleRevalidateTag} className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="tag" className="text-sm font-medium">
                  Tag
                </label>
                <Input
                  id="tag"
                  placeholder="products"
                  value={tag}
                  onChange={(e) => setTag(e.target.value)}
                  required
                />
              </div>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Revalidating..." : "Revalidate Tag"}
              </Button>
            </form>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}