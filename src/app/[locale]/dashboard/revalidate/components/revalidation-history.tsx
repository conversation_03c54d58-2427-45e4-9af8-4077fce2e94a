"use client"

import { Badge } from "@udoy/components/ui/badge"
import { <PERSON>rollArea } from "@udoy/components/ui/scroll-area"
import { CheckCircle, XCircle, Clock, RefreshCw, Tag } from "lucide-react"

// Mock data - in a real app, this would come from a database or state management
const mockHistory = [
  {
    id: 1,
    type: "path" as const,
    target: "/blog",
    status: "success" as const,
    timestamp: new Date(Date.now() - 5 * 60 * 1000),
  },
  {
    id: 2,
    type: "tag" as const,
    target: "posts",
    status: "success" as const,
    timestamp: new Date(Date.now() - 15 * 60 * 1000),
  },
  {
    id: 3,
    type: "path" as const,
    target: "/products",
    status: "error" as const,
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
  },
  {
    id: 4,
    type: "tag" as const,
    target: "users",
    status: "success" as const,
    timestamp: new Date(Date.now() - 45 * 60 * 1000),
  },
  {
    id: 5,
    type: "path" as const,
    target: "/",
    status: "success" as const,
    timestamp: new Date(Date.now() - 60 * 60 * 1000),
  },
]

export function RevalidationHistory() {
  const formatTimeAgo = (date: Date) => {
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) return "Just now"
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`

    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `${diffInHours}h ago`

    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays}d ago`
  }

  return (
    <ScrollArea className="h-[300px]">
      <div className="space-y-3">
        {mockHistory.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Clock className="mx-auto h-8 w-8 mb-2" />
            <p>No revalidation history yet</p>
          </div>
        ) : (
          mockHistory.map((item) => (
            <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  {item.type === "path" ? (
                    <RefreshCw className="h-4 w-4 text-blue-500" />
                  ) : (
                    <Tag className="h-4 w-4 text-purple-500" />
                  )}
                  <Badge variant={item.type === "path" ? "default" : "secondary"}>{item.type}</Badge>
                </div>
                <span className="font-mono text-sm">{item.target}</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">{formatTimeAgo(item.timestamp)}</span>
                {item.status === "success" ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
              </div>
            </div>
          ))
        )}
      </div>
    </ScrollArea>
  )
}
