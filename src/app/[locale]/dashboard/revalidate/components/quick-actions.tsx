"use client";

import { useState } from "react";
import { But<PERSON> } from "@udoy/components/ui/button";
import { Loader2, Home } from "lucide-react";
import { revalidateAction, revalidatePathAction } from "../actions";
import { <PERSON><PERSON><PERSON><PERSON> } from "@udoy/utils/cache-key";
import { withError } from "@udoy/utils/app-error";

const commonPaths = [
  {
    id: "home-page-content",
    tags: [CacheKey.MostPopularCategories(), CacheKey.MostPopularProducts()],
    paths: [],
    label: "Home Page",
    icon: Home,
  },
];

export function QuickActions() {
  const [loadingPath, setLoadingPath] = useState<string | null>(null);

  const handlePathRevalidation = async (id: string, paths: string[], tags: string[]) => {
    setLoadingPath(id);
    try {
      await withError(revalidateAction(paths, tags));
    } finally {
      setLoadingPath(null);
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
        {commonPaths.map(({ id, label, icon: Icon, paths, tags }) => (
          <Button
            key={id}
            variant="outline"
            size="sm"
            onClick={() => handlePathRevalidation(id, paths, tags)}
            disabled={loadingPath === id}
            className="justify-start"
          >
            {loadingPath === id ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Icon className="mr-2 h-4 w-4" />
            )}
            {label}
          </Button>
        ))}
      </div>
    </div>
  );
}
