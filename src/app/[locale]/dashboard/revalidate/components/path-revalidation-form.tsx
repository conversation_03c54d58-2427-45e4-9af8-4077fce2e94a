"use client";

import type React from "react";
import { useState } from "react";
import { But<PERSON> } from "@udoy/components/ui/button";
import { Input } from "@udoy/components/ui/input";
import { Label } from "@udoy/components/ui/label";
import { Alert, AlertDescription } from "@udoy/components/ui/alert";
import { Loader2, CheckCircle, XCircle } from "lucide-react";
import { revalidatePathAction } from "../actions";
import { withError } from "@udoy/utils/app-error";

export function PathRevalidationForm() {
  const [path, setPath] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!path.trim()) return;

    setIsLoading(true);
    setResult(null);

    try {
      // Use withError to handle ActionError pattern
      const response = await withError(revalidatePathAction(path.trim()));
      setResult(response);
      if (response.success) {
        setPath("");
      }
    } catch (error: any) {
      setResult({
        success: false,
        message: error?.message || "An unexpected error occurred",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="path">Page Path</Label>
          <Input
            id="path"
            placeholder="/blog/my-post or /api/users"
            value={path}
            onChange={(e) => setPath(e.target.value)}
            disabled={isLoading}
          />
          <p className="text-sm text-muted-foreground">
            Enter the full path starting with {`"/" (e.g., /blog, /products/123)`}
          </p>
        </div>
        <Button type="submit" disabled={isLoading || !path.trim()}>
          {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Revalidate Path
        </Button>
      </form>

      {result && (
        <Alert variant={result.success ? "default" : "destructive"}>
          {result.success ? (
            <CheckCircle className="h-4 w-4" />
          ) : (
            <XCircle className="h-4 w-4" />
          )}
          <AlertDescription>{result.message}</AlertDescription>
        </Alert>
      )}
    </div>
  );
}
