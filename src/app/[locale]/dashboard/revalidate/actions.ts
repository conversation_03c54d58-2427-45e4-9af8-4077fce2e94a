"use server";

import { revalidatePath, revalidateTag } from "next/cache";
import { ActionError } from "@udoy/utils/app-error";
import { <PERSON>ieUtil } from "@udoy/utils/cookie-util";
import { getPrisma } from "@udoy/utils/db-utils";
import { Role } from "@prisma/client";

export async function revalidatePathAction(path: string) {
  try {
    // Validate the path format
    if (!path.startsWith("/")) {
      return ActionError("Path must start with '/'");
    }

    // Perform the revalidation
    revalidatePath(path);

    return { success: true, message: `Successfully revalidated path: ${path}` };
  } catch (error) {
    console.error("Error revalidating path:", error);
    return ActionError(`Failed to revalidate path: ${path}`);
  }
}

export async function revalidateAction(paths: string[], tags: string[]) {
  try {
    const userId = await <PERSON>ieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();
    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    // Validate the path format
    if (paths.some((path) => !path.startsWith("/"))) {
      return ActionError("Path must start with '/'");
    }

    // Perform the revalidation
    paths.forEach((path) => revalidatePath(path));
    tags.forEach((tag) => revalidateTag(tag));

    return true;
  } catch (error) {
    console.error("Error revalidating:", error);
    return ActionError(`Failed to revalidate`);
  }
}

export async function revalidateTagAction(tag: string) {
  try {
    // Validate the tag
    if (!tag || tag.trim().length === 0) {
      return ActionError("Tag cannot be empty");
    }

    // Perform the revalidation
    revalidateTag(tag);

    return { success: true, message: `Successfully revalidated tag: ${tag}` };
  } catch (error) {
    console.error("Error revalidating tag:", error);
    return ActionError(`Failed to revalidate tag: ${tag}`);
  }
}
