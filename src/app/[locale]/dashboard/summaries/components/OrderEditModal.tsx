"use client";

import React from "react";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@udoy/components/ui/dialog";
import { Button } from "@udoy/components/ui/button";
import { Input } from "@udoy/components/ui/input";
import { Label } from "@udoy/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@udoy/components/ui/table";
import { Badge } from "@udoy/components/ui/badge";
import { Separator } from "@udoy/components/ui/separator";
import { OrderStatus, User } from "@prisma/client";
import { OrderWithItems as OrderType } from "@udoy/utils/types";

type Order = OrderType & { buyer: User };

interface OrderEditModalProps {
  order: Order | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (updatedOrder: Order) => void;
}

export function OrderEditModal({
  order,
  open,
  onOpenChange,
  onSave,
}: OrderEditModalProps) {
  const [editedOrder, setEditedOrder] = useState<Order | null>(null);

  // Initialize edited order when modal opens
  React.useEffect(() => {
    if (order && open) {
      setEditedOrder({ ...order });
    }
  }, [order, open]);

  if (!order || !editedOrder) return null;

  const handleSave = () => {
    if (editedOrder) {
      onSave(editedOrder);
      onOpenChange(false);
    }
  };

  const updateOrderField = (field: keyof Order, value: any) => {
    setEditedOrder((prev) => (prev ? { ...prev, [field]: value } : null));
  };

  const updateOrderItem = (
    itemIndex: number,
    field: keyof (typeof editedOrder.orderItems)[0],
    value: any
  ) => {
    setEditedOrder((prev) => {
      if (!prev) return null;
      const newItems = [...prev.orderItems];
      newItems[itemIndex] = { ...newItems[itemIndex], [field]: value };

      // Recalculate subtotal when item prices or quantities change
      if (
        field === "price" ||
        field === "quantity" ||
        field === "sourcePrice"
      ) {
        const newSubTotal = newItems.reduce(
          (acc, item) => acc + item.price * item.quantity,
          0
        );
        const newProfit = newItems.reduce(
          (acc, item) => acc + (item.price - item.sourcePrice) * item.quantity,
          0
        );

        return {
          ...prev,
          orderItems: newItems,
          subTotal: newSubTotal,
          profit: newProfit,
        };
      }

      return { ...prev, orderItems: newItems };
    });
  };

  const calculatedTotal =
    editedOrder.subTotal + editedOrder.shipping - editedOrder.discount;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Order #{order.id}</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Order Basic Info */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="customer">Customer</Label>
              <Input
                id="customer"
                value={editedOrder.buyer.name}
                disabled
                className="bg-gray-50"
              />
            </div>
            <div>
              <Label htmlFor="status">Status</Label>
              <Select
                value={editedOrder.status}
                onValueChange={(value) =>
                  updateOrderField("status", value as OrderStatus)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.values(OrderStatus).map((status) => (
                    <SelectItem key={status} value={status}>
                      {status}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <Separator />

          {/* Order Items */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Order Items</h3>
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Product</TableHead>
                    <TableHead className="text-center">Quantity</TableHead>
                    <TableHead className="text-right">Selling Price</TableHead>
                    <TableHead className="text-right">Purchase Price</TableHead>
                    <TableHead className="text-right">Item Total</TableHead>
                    <TableHead className="text-right">Item Profit</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {editedOrder.orderItems.map((item, index) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">
                        {item.product.name}
                      </TableCell>
                      <TableCell className="text-center">
                        <Input
                          type="number"
                          min="1"
                          value={item.quantity}
                          onChange={(e) =>
                            updateOrderItem(
                              index,
                              "quantity",
                              Number.parseInt(e.target.value) || 1
                            )
                          }
                          className="w-20 text-center"
                        />
                      </TableCell>
                      <TableCell className="text-right">
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          value={item.price}
                          onChange={(e) =>
                            updateOrderItem(
                              index,
                              "price",
                              Number.parseFloat(e.target.value) || 0
                            )
                          }
                          className="w-24 text-right"
                        />
                      </TableCell>
                      <TableCell className="text-right">
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          value={item.sourcePrice}
                          onChange={(e) =>
                            updateOrderItem(
                              index,
                              "sourcePrice",
                              Number.parseFloat(e.target.value) || 0
                            )
                          }
                          className="w-24 text-right"
                        />
                      </TableCell>
                      <TableCell className="text-right font-medium">
                        ${(item.price * item.quantity).toFixed(2)}
                      </TableCell>
                      <TableCell className="text-right">
                        <Badge
                          variant={
                            item.price > item.sourcePrice
                              ? "default"
                              : "destructive"
                          }
                        >
                          $
                          {(
                            (item.price - item.sourcePrice) *
                            item.quantity
                          ).toFixed(2)}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>

          <Separator />

          {/* Order Totals */}
          <div className="grid grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Order Adjustments</h3>
              <div>
                <Label htmlFor="shipping">Shipping Charge</Label>
                <Input
                  id="shipping"
                  type="number"
                  min="0"
                  step="0.01"
                  value={editedOrder.shipping}
                  onChange={(e) =>
                    updateOrderField(
                      "shipping",
                      Number.parseFloat(e.target.value) || 0
                    )
                  }
                />
              </div>
              <div>
                <Label htmlFor="discount">Discount</Label>
                <Input
                  id="discount"
                  type="number"
                  min="0"
                  step="0.01"
                  value={editedOrder.discount}
                  onChange={(e) =>
                    updateOrderField(
                      "discount",
                      Number.parseFloat(e.target.value) || 0
                    )
                  }
                />
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Order Summary</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span className="font-medium">
                    ${editedOrder.subTotal.toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Shipping:</span>
                  <span className="font-medium">
                    ${editedOrder.shipping.toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Discount:</span>
                  <span className="font-medium text-red-600">
                    -${editedOrder.discount.toFixed(2)}
                  </span>
                </div>
                <Separator />
                <div className="flex justify-between text-lg font-bold">
                  <span>Total:</span>
                  <span>${calculatedTotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-lg">
                  <span>Total Profit:</span>
                  <Badge
                    variant={editedOrder.profit > 0 ? "default" : "destructive"}
                    className="text-base"
                  >
                    ${editedOrder.profit.toFixed(2)}
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button onClick={handleSave}>Save Changes</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
