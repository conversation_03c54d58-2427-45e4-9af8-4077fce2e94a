"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@udoy/components/ui/table";
import { Badge } from "@udoy/components/ui/badge";
import { Button } from "@udoy/components/ui/button";
import {
  ArrowLeft,
  DollarSign,
  Package,
  TrendingUp,
  Truck,
} from "lucide-react";
import { SummaryType } from "@prisma/client";
import { getSummaryById, type SummaryWithDetails } from "../actions";
import { format } from "date-fns";
import Link from "next/link";
import { toast } from "sonner";
import { withError } from "@udoy/utils/app-error";

export default function SummaryDetailPage() {
  const params = useParams();
  const summaryId = params.summaryId as string;
  const [summary, setSummary] = useState<SummaryWithDetails | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSummary();
  }, [summaryId]);

  const loadSummary = async () => {
    try {
      setLoading(true);
      const data = await withError(getSummaryById(summaryId));
      setSummary(data);
    } catch (error: any) {
      console.error("Failed to load summary:", error);
      toast.error(
        error?.message || "Failed to load summary details. Please try again."
      );
    } finally {
      setLoading(false);
    }
  };

  const formatDateRange = (summary: SummaryWithDetails) => {
    const start = new Date(summary.startDate);
    const end = new Date(summary.endDate);

    if (summary.type === SummaryType.DAILY) {
      return start.toLocaleDateString("en-US", {
        weekday: "long",
        month: "long",
        day: "numeric",
        year: "numeric",
      });
    }

    if (summary.type === SummaryType.WEEKLY) {
      return `${start.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      })} - ${end.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        year: "numeric",
      })}`;
    }

    if (summary.type === SummaryType.MONTHLY) {
      return start.toLocaleDateString("en-US", {
        month: "long",
        year: "numeric",
      });
    }

    return start.getFullYear().toString();
  };

  const getSummaryTypeColor = (type: SummaryType) => {
    switch (type) {
      case SummaryType.DAILY:
        return "bg-blue-100 text-blue-800";
      case SummaryType.WEEKLY:
        return "bg-green-100 text-green-800";
      case SummaryType.MONTHLY:
        return "bg-purple-100 text-purple-800";
      case SummaryType.YEARLY:
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!summary) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Summary Not Found
          </h2>
          <p className="text-gray-600 mb-4">
            The requested summary could not be found.
          </p>
          <Link href="/dashboard/summaries">
            <Button>Back to Summaries</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center gap-4">
        <Link href="/dashboard/summaries">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        </Link>
        <div>
          <div className="flex items-center gap-3 mb-1">
            <h1 className="text-3xl font-bold">
              {summary.type.charAt(0) + summary.type.slice(1).toLowerCase()}{" "}
              Summary
            </h1>
            <Badge className={getSummaryTypeColor(summary.type)}>
              {summary.type}
            </Badge>
          </div>
          <p className="text-gray-600">{formatDateRange(summary)}</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ৳{Number(summary.totalRevenue).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Generated from {summary.totalOrders} orders
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Profit</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ৳{Number(summary.totalProfit).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              {(
                (Number(summary.totalProfit) / Number(summary.totalRevenue)) *
                100
              ).toFixed(1)}
              % margin
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.totalOrders}</div>
            <p className="text-xs text-muted-foreground">
              Avg: ৳
              {(Number(summary.totalRevenue) / summary.totalOrders).toFixed(2)}{" "}
              per order
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Delivery Charges
            </CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ৳{Number(summary.totalDeliveryCharges).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Avg: ৳
              {(
                Number(summary.totalDeliveryCharges) / summary.totalOrders
              ).toFixed(2)}{" "}
              per order
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Notes */}
      {summary.notes && (
        <Card>
          <CardHeader>
            <CardTitle>Notes</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700">{summary.notes}</p>
          </CardContent>
        </Card>
      )}

      {/* Detailed Data */}
      <Card>
        <CardHeader>
          <CardTitle>
            {summary.type === SummaryType.DAILY
              ? "Individual Orders"
              : "Child Summaries"}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {summary.type === SummaryType.DAILY && summary.orders.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Order ID</TableHead>
                  <TableHead>Customer</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Subtotal</TableHead>
                  <TableHead className="text-right">Shipping</TableHead>
                  <TableHead className="text-right">Profit</TableHead>
                  <TableHead>Date</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {summary.orders.map((order) => (
                  <TableRow key={order.id}>
                    <TableCell className="font-medium">
                      <Link
                        href={`/dashboard/orders/${order.id}`}
                        target="_blank"
                      >
                        #{order.id}
                      </Link>
                    </TableCell>
                    <TableCell>{order.buyer.name}</TableCell>
                    <TableCell>
                      <Badge variant="outline">{order.status}</Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      ৳{order.subTotal}
                    </TableCell>
                    <TableCell className="text-right">
                      ৳{order.shipping}
                    </TableCell>
                    <TableCell className="text-right">
                      <Badge
                        variant={order.profit > 0 ? "default" : "destructive"}
                      >
                        ৳{order.profit.toFixed(2)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {format(new Date(order.createdAt), "MMM d, h:mm a")}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : summary.childSummaries.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead className="text-right">Revenue</TableHead>
                  <TableHead className="text-right">Profit</TableHead>
                  <TableHead className="text-right">Orders</TableHead>
                  <TableHead className="text-center">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {summary.childSummaries.map((childSummary) => (
                  <TableRow key={childSummary.id}>
                    <TableCell>
                      {format(new Date(childSummary.startDate), "MMM d, yyyy")}
                    </TableCell>
                    <TableCell>
                      <Badge className={getSummaryTypeColor(childSummary.type)}>
                        {childSummary.type}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      ৳{Number(childSummary.totalRevenue).toLocaleString()}
                    </TableCell>
                    <TableCell className="text-right">
                      ৳{Number(childSummary.totalProfit).toLocaleString()}
                    </TableCell>
                    <TableCell className="text-right">
                      {childSummary.totalOrders}
                    </TableCell>
                    <TableCell className="text-center">
                      <Link href={`/dashboard/summaries/${childSummary.id}`}>
                        <Button variant="outline" size="sm">
                          View Details
                        </Button>
                      </Link>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No detailed data available for this summary.
            </div>
          )}
        </CardContent>
      </Card>

      {/* Summary Info */}
      <Card>
        <CardHeader>
          <CardTitle>Summary Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="flex justify-between">
            <span className="text-gray-600">Generated By:</span>
            <span className="font-medium">{summary.generatedBy.name}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Created At:</span>
            <span className="font-medium">
              {format(new Date(summary.createdAt), "PPP p")}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Status:</span>
            <Badge variant={summary.isFinalized ? "default" : "secondary"}>
              {summary.isFinalized ? "Finalized" : "Draft"}
            </Badge>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
