"use server";

import { ActionError } from "@udoy/utils/app-error";
import { revalidatePath } from "next/cache";
import { AddressInputSchema } from "@udoy/libs/zod-schema";
import { z } from "zod";
import { <PERSON>ieUtil } from "@udoy/utils/cookie-util";
import { getPrisma } from "@udoy/utils/db-utils";
import { Role } from "@prisma/client";

// Update address action
export async function updateAddress(addressId: string, data: any) {
  try {
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("User Not Logged In");
    }

    const { zoneId, name, home, phone, label, location } =
      AddressInputSchema.parse(data);

    const prisma = getPrisma();

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    // Check if address belongs to user
    const existingAddress = await prisma.address.findUnique({
      where: {
        id: addressId,
      },
    });

    if (!existingAddress) {
      return ActionError("Address not found");
    }

    // Update the address
    const updatedAddress = await prisma.address.update({
      where: {
        id: addressId,
      },
      data: {
        home,
        name,
        label,
        phone,
        location,
        zoneId,
      },
      include: {
        zone: true,
      },
    });

    // Revalidate the dashboard path
    revalidatePath("/dashboard");

    return updatedAddress;
  } catch (error: any) {
    return ActionError(error?.message || "Failed To Update Address");
  }
}
