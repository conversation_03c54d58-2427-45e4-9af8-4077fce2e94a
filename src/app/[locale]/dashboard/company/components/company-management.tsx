"use client";

import { useState } from "react";
import { Plus } from "lucide-react";
import { Button } from "@udoy/components/ui/button";
import { Input } from "@udoy/components/ui/input";
import { CompanyList } from "./company-list";
import { CompanyFormDialog } from "./company-form-dialog";
import { CompanyDetailsDialog } from "./company-details-dialog";
import { Company } from "@prisma/client";
import { withError } from "@udoy/utils/app-error";
import { createCompany, deleteCompany, updateCompany } from "../action";
import { toast } from "sonner";

export function CompanyManagement(props: {
  companies: (Company & { _count: { products: number } })[];
}) {
  const [companies, setCompany] = useState(props.companies);
  const [searchQuery, setSearchQuery] = useState("");
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState<any>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [currentCompany, setCurrentCompany] = useState<any>(null);

  const handleCreateCompany = async (companyData: any) => {
    try {
      const company = await withError(createCompany(companyData));
      setCompany([...companies, company]);
      toast.success("Company created");
    } catch (error: any) {
      toast.error(error?.message || "Failed to create company");
    }
    setIsFormOpen(false);
  };

  const handleUpdateCompany = async (companyData: any, companyId: string) => {
    try {
      const company = await withError(updateCompany(companyId, companyData));
      setCompany(
        companies.map((s) => (s.id === company.id ? { ...s, ...company } : s))
      );
      toast.success("Company updated");
    } catch (error: any) {
      toast.error(error?.message || "Failed to update company");
    }
    setIsFormOpen(false);
    setSelectedCompany(null);
  };

  const handleDeleteCompany = async (companyId: string) => {
    try {
      await withError(deleteCompany(companyId));
      toast.success("Company deleted");
      setCompany(companies.filter((company) => company.id !== companyId));
    } catch (error: any) {
      toast.error(error?.message || "Failed to delete company");
    }
  };

  // Open form for editing
  const handleEditCompany = (company: any) => {
    setSelectedCompany(company);
    setIsFormOpen(true);
  };

  // Open details dialog
  const handleViewDetails = (company: any) => {
    setCurrentCompany(company);
    setIsDetailsOpen(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
        <div className="w-full sm:w-auto">
          <Input
            placeholder="Search companies by name or location..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-md"
          />
        </div>
        <Button
          onClick={() => {
            setSelectedCompany(null);
            setIsFormOpen(true);
          }}
        >
          <Plus className="mr-2 h-4 w-4" /> Add New Company
        </Button>
      </div>

      <CompanyList
        companies={props.companies}
        onEdit={handleEditCompany}
        onDelete={handleDeleteCompany}
        onViewDetails={handleViewDetails}
      />

      <CompanyFormDialog
        isOpen={isFormOpen}
        onClose={() => {
          setIsFormOpen(false);
          setSelectedCompany(null);
        }}
        company={selectedCompany}
        onSubmit={
          selectedCompany
            ? (data) => handleUpdateCompany(data, selectedCompany.id) as any
            : handleCreateCompany
        }
      />

      <CompanyDetailsDialog
        isOpen={isDetailsOpen}
        onClose={() => {
          setIsDetailsOpen(false);
          setCurrentCompany(null);
        }}
        company={currentCompany}
      />
    </div>
  );
}
