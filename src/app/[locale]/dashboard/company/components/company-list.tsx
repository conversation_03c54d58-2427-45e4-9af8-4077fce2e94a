"use client";

import { useState } from "react";
import {
  Edit,
  Trash2,
  Store,
  ChevronDown,
  ChevronUp,
  Eye,
  BuildingIcon,
} from "lucide-react";
import { But<PERSON> } from "@udoy/components/ui/button";
import { Card, CardContent } from "@udoy/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@udoy/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@udoy/components/ui/alert-dialog";
import { Company } from "@prisma/client";

interface CompanyListProps {
  companies: (Company & { _count: { products: number } })[];
  onEdit: (company: any) => void;
  onDelete: (companyId: string) => void;
  onViewDetails: (company: any) => void;
}

export function CompanyList({
  companies,
  onEdit,
  onDelete,
  onViewDetails,
}: CompanyListProps) {
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [companyToDelete, setCompanyToDelete] = useState<string | null>(null);

  // Format date
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Confirm delete
  const confirmDelete = (companyId: string) => {
    setCompanyToDelete(companyId);
    setDeleteConfirmOpen(true);
  };

  // Handle delete confirmation
  const handleConfirmDelete = () => {
    if (companyToDelete) {
      onDelete(companyToDelete);
      setCompanyToDelete(null);
      setDeleteConfirmOpen(false);
    }
  };

  return (
    <Card>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[250px] cursor-pointer">
                  <div className="flex items-center">Company Name</div>
                </TableHead>
                <TableHead className="w-[250px] cursor-pointer">
                  <div className="flex items-center">Bangla Name</div>
                </TableHead>
                <TableHead className="w-[250px] cursor-pointer">
                  <div className="flex items-center">Slug</div>
                </TableHead>
                <TableHead className="cursor-pointer">
                  <div className="flex items-center">Products</div>
                </TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {companies.length === 0 ? (
                <TableRow>
                  <TableCell
                    colSpan={5}
                    className="text-center py-8 text-muted-foreground"
                  >
                    No companies found. Add a new company to get started.
                  </TableCell>
                </TableRow>
              ) : (
                companies.map((company) => (
                  <TableRow key={company.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center">
                        <BuildingIcon className="mr-2 h-4 w-4 text-muted-foreground -mt-0.5" />
                        {company.name}
                      </div>
                    </TableCell>
                    <TableCell>{company.nam}</TableCell>
                    <TableCell>{company.slug}</TableCell>
                    <TableCell>{company?._count?.products}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => onViewDetails(company)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => onEdit(company)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => confirmDelete(company.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>

      <AlertDialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              company and remove its data from our servers.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmDelete}>
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
}
