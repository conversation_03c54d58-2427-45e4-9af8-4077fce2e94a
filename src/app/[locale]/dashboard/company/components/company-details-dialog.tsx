"use client";

import { useState } from "react";
import { Store, Package, Calendar, MapPin, Search } from "lucide-react";
import { Button } from "@udoy/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@udoy/components/ui/dialog";
import { Input } from "@udoy/components/ui/input";
import { Badge } from "@udoy/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@udoy/components/ui/table";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@udoy/components/ui/tabs";
import { Company, Product } from "@prisma/client";

interface CompanyDetailsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  company: Company & { _count: { products: number }; products: Product[] };
}

export function CompanyDetailsDialog({
  isOpen,
  onClose,
  company,
}: CompanyDetailsDialogProps) {
  const [searchQuery, setSearchQuery] = useState("");

  if (!company) return null;

  // Filter products based on search query

  // Format date
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Format price
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
    }).format(price / 100);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Store className="mr-2 h-5 w-5" />
            {company.name}
          </DialogTitle>
          <DialogDescription>
            Company details and associated products
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <Tabs defaultValue="products">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="products">
                Products ({company._count.products})
              </TabsTrigger>
              <TabsTrigger value="info">Company Info</TabsTrigger>
            </TabsList>

            <TabsContent value="products" className="space-y-4">
              <div className="flex items-center">
                <Search className="mr-2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="max-w-sm"
                />
              </div>

              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product</TableHead>
                      <TableHead>Price</TableHead>
                      <TableHead>Source Price</TableHead>
                      <TableHead>Supply</TableHead>
                      <TableHead>Discount</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {company?.products?.length === 0 ? (
                      <TableRow>
                        <TableCell
                          colSpan={5}
                          className="text-center py-6 text-muted-foreground"
                        >
                          No products found.
                        </TableCell>
                      </TableRow>
                    ) : (
                      company.products?.map((product: Product) => (
                        <TableRow key={product.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium flex items-center">
                                <Package className="mr-2 h-4 w-4 text-muted-foreground" />
                                {product.name}
                              </div>
                              {product.nam && (
                                <div className="text-xs text-muted-foreground mt-1">
                                  {product.nam}
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>{formatPrice(product.price)}</TableCell>
                          <TableCell>
                            {formatPrice(product.sourcePrice)}
                          </TableCell>
                          <TableCell>{product.supply}</TableCell>
                          <TableCell>
                            {product.discount > 0 ? (
                              <Badge variant="secondary">
                                {product.discount}% off
                              </Badge>
                            ) : (
                              <span className="text-muted-foreground">-</span>
                            )}
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>

            <TabsContent value="info">
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">
                      Company Name
                    </h3>
                    <p>{company.name}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">
                      Slug
                    </h3>
                    <p>{company.slug}</p>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">
                    Company Statistics
                  </h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-muted rounded-lg p-4">
                      <div className="text-2xl font-bold">
                        {company._count.products}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Total Products
                      </div>
                    </div>
                    <div className="bg-muted rounded-lg p-4">
                      <div className="text-2xl font-bold">
                        {formatPrice(4999)}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Average Product Price
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <DialogFooter>
          <Button onClick={onClose}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
