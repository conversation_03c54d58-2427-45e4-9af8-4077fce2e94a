"use client";

import { useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { optional, z } from "zod";
import { Button } from "@udoy/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@udoy/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@udoy/components/ui/form";
import { Input } from "@udoy/components/ui/input";
import { isValidPlusCode } from "@udoy/utils";
import { CheckCircle } from "lucide-react";
import { cn } from "@udoy/utils/shadcn";
import Link from "next/link";
import { companyFormSchema } from "../utils";

// Type for our form values
type CompanyFormValues = z.infer<typeof companyFormSchema>;

interface CopanyFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  company?: any;
  onSubmit: (values: CompanyFormValues) => void;
}

export function CompanyFormDialog({
  isOpen,
  onClose,
  company,
  onSubmit,
}: CopanyFormDialogProps) {
  const form = useForm<CompanyFormValues>({
    resolver: zodResolver(companyFormSchema),
    defaultValues: {
      name: "",
      slug: "",
      nam: "",
    },
  });

  useEffect(() => {
    if (company) {
      form.reset({
        name: company.name,
        slug: company.slug,
        nam: company.nam,
      });
    } else {
      form.reset({
        name: "",
        slug: "",
        nam: "",
      });
    }
  }, [company, form]);

  // Generate slug from name
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^\w\s-]/g, "")
      .replace(/\s+/g, "-");
  };

  // Handle name change to auto-generate slug
  const handleNameChange = (value: string) => {
    form.setValue("name", value);

    if (
      !company ||
      form.getValues("slug") === "" ||
      form.getValues("slug") === generateSlug(company.name)
    ) {
      form.setValue("slug", generateSlug(value));
    }
  };

  // Handle form submission
  const handleSubmit = (values: CompanyFormValues) => {
    onSubmit({
      ...values,
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {company ? "Edit Company" : "Add New Company"}
          </DialogTitle>
          <DialogDescription>
            {company
              ? "Make changes to the company details here."
              : "Fill in the details to create a new company."}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-4 py-4"
          >
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Company Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter company name"
                      {...field}
                      onChange={(e) => handleNameChange(e.target.value)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="nam"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Bangla name</FormLabel>
                  <FormControl>
                    <Input placeholder="name in bangla" {...(field as any)} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="slug"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Slug</FormLabel>
                  <FormControl>
                    <Input placeholder="company-url-slug" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="pt-4">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit">
                {company ? "Update Company" : "Create Company"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
