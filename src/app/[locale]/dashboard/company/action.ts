"use server";

import { Role } from "@prisma/client";
import { ActionError } from "@udoy/utils/app-error";
import { CookieUtil } from "@udoy/utils/cookie-util";
import { getPrisma } from "@udoy/utils/db-utils";
import { revalidatePath } from "next/cache";
import { companyFormSchema } from "./utils";

export async function createCompany(data: any) {
  try {
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }
    const prisma = getPrisma();

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const companyData = companyFormSchema.parse(data);

    const company = await prisma.company.create({
      data: {
        name: companyData.name,
        slug: companyData.slug,
        nam: companyData.nam,
      },
      include: {
        _count: {
          select: {
            products: true,
          },
        },
      },
    });

    revalidatePath("/dashboard/shops");
    return company;
  } catch (error) {
    console.log(error);
    return ActionError("Failed To Create Shop");
  }
}

export async function updateCompany(companyId: string, data: any) {
  try {
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();
    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const companyData = companyFormSchema.parse(data);

    const company = await prisma.company.update({
      where: { id: companyId },
      data: {
        name: companyData.name,
        slug: companyData.slug,
        nam: companyData.nam,
      },

      include: {
        _count: {
          select: {
            products: true,
          },
        },
      },
    });
    revalidatePath("/dashboard/company");
    return company;
  } catch (error) {
    console.log(error);
    return ActionError("Failed To Update Shop");
  }
}

export async function deleteCompany(companyId: string) {
  try {
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();
    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const shop = await prisma.company.delete({
      where: { id: companyId },
    });
    revalidatePath("/dashboard/shops");
    return shop;
  } catch (error) {
    console.log(error);
    return ActionError("Failed To Delete Shop");
  }
}
