import { getPrisma } from "@udoy/utils/db-utils";
import Layout from "../components/Layout";
import { CompanyManagement } from "./components/company-management";

export default async function ShopsPage() {
  const shops = await getPrisma().company.findMany({
    include: {
      _count: {
        select: {
          products: true,
        },
      },
    },
  });
  return (
    <Layout>
      <div className="">
        <h1 className="text-3xl font-bold mb-6">Store Management</h1>
        <CompanyManagement companies={shops} />
      </div>
    </Layout>
  );
}
