"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";

import { But<PERSON> } from "@udoy/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import { Input } from "@udoy/components/ui/input";
import { Textarea } from "@udoy/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import { Switch } from "@udoy/components/ui/switch";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@udoy/components/ui/form";
import { createCampaign } from "../actions";
import { campaignFormSchema, type CampaignFormValues } from "../schema";
import { withError } from "@udoy/utils/app-error";

interface CampaignFormProps {
  deliveryZones: Array<{ id: string; name: string; slug: string }>;
}

export function CampaignForm({ deliveryZones }: CampaignFormProps) {
  // Initialize the form using the useForm hook
  const form = useForm<CampaignFormValues>({
    resolver: zodResolver(campaignFormSchema),
    defaultValues: {
      title: "",
      body: "",
      imageUrl: "",
      targetRole: "ALL",
      targetZoneId: "ALL",
      targetUserIds: "",
      targetUrl: "",
      sendNow: false,
      iconUrl: "",
      // Default schedule time to 1 minute in the future for convenience
      scheduledFor: new Date(Date.now() + 60000).toISOString().slice(0, 16),
    },
  });

  // Watch the 'sendNow' field to conditionally render the schedule input
  const sendNow = form.watch("sendNow");
  const { isSubmitting } = form.formState;

  // Create the submit handler
  async function onSubmit(data: CampaignFormValues) {
    try {
      // Parse user IDs if provided
      let userIds: number[] | undefined;
      if (data.targetUserIds) {
        userIds = data.targetUserIds
          .split(",")
          .map((id) => id.trim())
          .filter((id) => id !== "")
          .map((id) => parseInt(id, 10))
          .filter((id) => !isNaN(id));
      }

      // Call the server action with properly formatted data and handle errors
      const result = await withError(
        createCampaign({
          title: data.title,
          body: data.body,
          imageUrl: data.imageUrl || undefined,
          iconUrl: data.iconUrl || undefined,
          scheduledFor: data.scheduledFor
            ? new Date(data.scheduledFor)
            : new Date(),
          sendNow: data.sendNow,
          targetAudience: {
            role: data.targetRole !== "ALL" ? data.targetRole : undefined,
            zoneId: data.targetZoneId !== "ALL" ? data.targetZoneId : undefined,
            userIds: userIds,
          },
          targetUrl: data.targetUrl || undefined,
        })
      );

      toast.success("Campaign Created", {
        description: data.sendNow
          ? "Campaign created and sent successfully!"
          : "Campaign created and scheduled successfully!",
      });
      form.reset(); // Reset form to default values on success
    } catch (error: any) {
      toast.error("Error", {
        description: error?.message || "Failed to create campaign",
      });
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Create New Campaign</CardTitle>
        <CardDescription>
          Create and send push notifications to your users
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* 5. Set up the Form provider and the form element */}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div className="space-y-4">
                {/* Title Field */}
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Notification Title</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter notification title"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Body Field */}
                <FormField
                  control={form.control}
                  name="body"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Message Body</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter your message"
                          rows={4}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Image URL Field */}
                <FormField
                  control={form.control}
                  name="imageUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Image URL (Optional)</FormLabel>
                      <FormControl>
                        <Input
                          type="url"
                          placeholder="https://example.com/image.jpg"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Target URL Field */}
                <FormField
                  control={form.control}
                  name="targetUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Target URL (Optional)</FormLabel>
                      <FormControl>
                        <Input
                          type="url"
                          placeholder="https://example.com/page"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        URL to open when notification is clicked
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="iconUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Icon URL (Optional)</FormLabel>
                      <FormControl>
                        <Input
                          type="url"
                          placeholder="https://example.com/icon.png"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Icon To Show in Notification
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="space-y-4">
                {/* Target Role Field */}
                <FormField
                  control={form.control}
                  name="targetRole"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Target Role</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select target role" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="ALL">All Users</SelectItem>
                          <SelectItem value="USER">Regular Users</SelectItem>
                          <SelectItem value="DELIVERY_MAN">
                            Delivery Personnel
                          </SelectItem>
                          <SelectItem value="ADMIN">Admins</SelectItem>
                          <SelectItem value="MAINTAINER">
                            Maintainers
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Target Zone Field */}
                <FormField
                  control={form.control}
                  name="targetZoneId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Target Zone</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select delivery zone" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="ALL">All Zones</SelectItem>
                          {deliveryZones.map((zone) => (
                            <SelectItem key={zone.id} value={zone.id}>
                              {zone.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Target User IDs Field */}
                <FormField
                  control={form.control}
                  name="targetUserIds"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Target User IDs (Optional)</FormLabel>
                      <FormControl>
                        <Textarea placeholder="1, 2, 3, 4, 5" {...field} />
                      </FormControl>
                      <FormDescription>
                        Comma-separated list of user IDs to target specifically
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Send Now Field */}
                <FormField
                  control={form.control}
                  name="sendNow"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 rounded-md border p-4">
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Send Immediately</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />

                {/* Scheduled For Field (Conditional) */}
                {!sendNow && (
                  <FormField
                    control={form.control}
                    name="scheduledFor"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Schedule For</FormLabel>
                        <FormControl>
                          <Input type="datetime-local" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>
            </div>

            <Button type="submit" disabled={isSubmitting} className="w-full">
              {isSubmitting
                ? "Creating..."
                : sendNow
                ? "Create & Send Now"
                : "Schedule Campaign"}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
