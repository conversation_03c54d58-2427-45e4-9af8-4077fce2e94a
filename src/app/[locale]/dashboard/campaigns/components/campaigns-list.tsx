"use client";

import { useState } from "react";
import { toast } from "sonner";
import { withError } from "@udoy/utils/app-error";
import {
  getCampaigns,
  sendCampaignNow,
  deleteCampaign,
  CampaignWithStats,
} from "../actions";
import { useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@udoy/components/ui/table";
import { Button } from "@udoy/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@udoy/components/ui/alert-dialog";
import { Badge } from "@udoy/components/ui/badge";
import { Send, Trash2 } from "lucide-react";

export function CampaignsList({
  campaigns,
}: {
  campaigns: CampaignWithStats[];
}) {
  const [deleteId, setDeleteId] = useState<string | null>(null);

  async function handleSendNow(id: string) {
    try {
      await withError(sendCampaignNow(id));
      toast.success("Campaign Sent", {
        description: "Campaign has been sent successfully!",
      });
    } catch (error: any) {
      toast.error("Error", {
        description: error?.message || "Failed to send campaign",
      });
    }
  }

  async function handleDelete() {
    if (!deleteId) return;

    try {
      await withError(deleteCampaign(deleteId));
      toast.success("Campaign Deleted", {
        description: "Campaign has been deleted successfully!",
      });
    } catch (error: any) {
      toast.error("Error", {
        description: error?.message || "Failed to delete campaign",
      });
    } finally {
      setDeleteId(null);
    }
  }

  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-bold">Campaign History</h2>

      {campaigns.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          No campaigns found. Create your first campaign above.
        </div>
      ) : (
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Scheduled For</TableHead>
                <TableHead>Stats</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {campaigns.map((campaign) => (
                <TableRow key={campaign.id}>
                  <TableCell className="font-medium">
                    {campaign.title}
                  </TableCell>
                  <TableCell>
                    {new Date(campaign.createdAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    {campaign.isSent ? (
                      <Badge variant="default">Sent</Badge>
                    ) : (
                      <Badge variant="outline">Scheduled</Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    {new Date(campaign.scheduledFor).toLocaleString()}
                  </TableCell>
                  <TableCell>
                    <div className="text-xs space-y-1">
                      <div>Sent: {campaign.stats.totalSent}</div>
                      <div>Seen: {campaign.stats.totalSeen}</div>
                      <div>Clicked: {campaign.stats.totalClicked}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      {!campaign.isSent && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleSendNow(campaign.id)}
                        >
                          <Send className="h-4 w-4 mr-1" />
                          Send Now
                        </Button>
                      )}

                      <AlertDialog
                        open={deleteId === campaign.id}
                        onOpenChange={(open) => !open && setDeleteId(null)}
                      >
                        <AlertDialogTrigger asChild>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => setDeleteId(campaign.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Delete Campaign</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to delete this campaign?
                              This action cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={handleDelete}>
                              Delete
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
}
