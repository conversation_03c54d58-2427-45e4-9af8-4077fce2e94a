import { z } from "zod";

// Define the Zod schema for campaign validation
export const campaignFormSchema = z
  .object({
    title: z.string().min(3, { message: "Title must be at least 3 characters." }),
    body: z
      .string()
      .min(10, { message: "Message body must be at least 10 characters." }),
    imageUrl: z
      .string()
      .url({ message: "Please enter a valid URL." })
      .optional()
      .or(z.literal("")),
    targetRole: z.enum([
      "ALL",
      "USER",
      "DELIVERY_MAN",
      "ADMIN",
      "MAINTAINER",
    ]),
    targetZoneId: z.string(),
    targetUserIds: z.string().optional(),
    targetUrl: z.string().url({ message: "Please enter a valid URL." }).optional().or(z.literal("")),
    sendNow: z.boolean(),
    scheduledFor: z.string().optional(),
    iconUrl: z.string().url({ message: "Please enter a valid URL." }).optional().or(z.literal("")),
  })
  .refine(
    (data) => {
      // If not sending immediately, a schedule date must be provided.
      if (!data.sendNow) {
        return !!data.scheduledFor;
      }
      return true;
    },
    {
      message: "A schedule date is required when not sending immediately.",
      path: ["scheduledFor"], // Apply error to the scheduledFor field
    }
  );

// Infer the form values type from the schema
export type CampaignFormValues = z.infer<typeof campaignFormSchema>;

// Define the server action input schema
export const campaignActionSchema = z.object({
  title: z.string().min(3),
  body: z.string().min(10),
  imageUrl: z.string().url().optional(),
  scheduledFor: z.date(),
  sendNow: z.boolean(),
  targetAudience: z.object({
    role: z.string().optional(),
    zoneId: z.string().optional(),
    userIds: z.array(z.number()).optional(),
  }).optional(),
  targetUrl: z.string().url().optional(),
  iconUrl: z.string().url().optional(),
});

// Infer the server action input type from the schema
export type CampaignActionInput = z.infer<typeof campaignActionSchema>;