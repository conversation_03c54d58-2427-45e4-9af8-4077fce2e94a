"use server";

import { Role } from "@prisma/client";
import { revalidatePath } from "next/cache";
import { sendPushNotification } from "@udoy/libs/backend/push-service";
import { getTargetedUsers } from "./utils/targeting";
import { getPrisma } from "@udoy/utils/db-utils";
import { campaignActionSchema, type CampaignActionInput } from "./schema";
import { ActionError } from "@udoy/utils/app-error";
import { CookieUtil } from "@udoy/utils/cookie-util";

export async function createCampaign(input: CampaignActionInput) {
  try {
    // Get the current user ID
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to continue");
    }

    const prisma = getPrisma();

    // Check if user is an admin
    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized: Only admins can create campaigns");
    }

    // Validate input with Zod schema
    const data = campaignActionSchema.parse(input);

    // Build target audience
    const targetAudience: any = {};
    if (data.targetAudience?.role && data.targetAudience.role !== "ALL") {
      targetAudience.role = data.targetAudience.role;
    }
    if (data.targetAudience?.zoneId && data.targetAudience.zoneId !== "ALL") {
      targetAudience.zoneId = data.targetAudience.zoneId;
    }
    if (
      data.targetAudience?.userIds &&
      data.targetAudience.userIds.length > 0
    ) {
      targetAudience.userIds = data.targetAudience.userIds;
    }

    // Create notification
    const notification = await prisma.campaign.create({
      data: {
        title: data.title,
        body: data.body,
        imageUrl: data.imageUrl,
        scheduledFor: data.sendNow ? new Date() : data.scheduledFor,
        targetAudience:
          Object.keys(targetAudience).length > 0 ? targetAudience : null,
        isSent: false,
        targetUrl: data.targetUrl,
        iconUrl: data.iconUrl,
      },
    });

    // If send now is selected, send immediately
    if (data.sendNow) {
      await sendCampaignNow(notification.id);
    }

    revalidatePath("/dashboard/campaigns");
    return { success: true, id: notification.id };
  } catch (error) {
    console.error("Error creating campaign:", error);
    return ActionError("Failed to create campaign");
  }
}

export async function sendCampaignNow(campaignId: string) {
  try {
    // Get the current user ID
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to continue");
    }

    const prisma = getPrisma();

    // Check if user is an admin
    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized: Only admins can send campaigns");
    }

    const campaign = await prisma.campaign.findUnique({
      where: { id: campaignId },
    });

    if (!campaign || campaign.isSent) {
      return ActionError("Notification not found or already sent");
    }

    // Get targeted users
    const targetedUsers = await getTargetedUsers(campaign.targetAudience);

    // Create user notifications
    const notifications = [];
    for (const userId of targetedUsers) {
      const notification = await prisma.notification.create({
        data: {
          userId,
          campaignId: campaign.id,
          title: campaign.title,
          body: campaign.body,
          imageUrl: campaign.imageUrl,
          targetUrl: campaign.targetUrl,
          iconUrl: campaign.iconUrl,
          data: campaign.data || {},
        },
      });
      notifications.push(notification);
    }

    // Send push notifications
    let sentCount = 0;
    for (const notification of notifications) {
      const success = await sendPushNotification(notification.userId, {
        title: notification.title,
        body: notification.body,
        imageUrl: notification.imageUrl!,
        targetUrl: notification.targetUrl!,
        iconUrl: notification.iconUrl!,
        notificationId: notification.id, // Pass the notification ID
        data: notification.data as any,
      });
      if (success) sentCount++;
    }

    // Update notification as sent
    await prisma.campaign.update({
      where: { id: campaignId },
      data: {
        isSent: true,
        sentAt: new Date(),
      },
    });

    revalidatePath("/dashboard/campaigns");
    return { success: true, sentCount, totalUsers: targetedUsers.length };
  } catch (error) {
    console.error("Error sending campaign:", error);
    return ActionError("Failed to send campaign");
  }
}

export async function getCampaigns() {
  try {
    const prisma = getPrisma();
    const notifications = await prisma.campaign.findMany({
      include: {
        notifications: {
          select: {
            isSeen: true,
            isClicked: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
      take: 10,
    });

    return notifications.map((notification) => ({
      ...notification,
      stats: {
        totalSent: notification.notifications.length,
        totalSeen: notification.notifications.filter((un) => un.isSeen).length,
        totalClicked: notification.notifications.filter((un) => un.isClicked)
          .length,
        deliveryRate: notification.notifications.length > 0 ? 100 : 0,
        openRate:
          notification.notifications.length > 0
            ? (notification.notifications.filter((un) => un.isSeen).length /
                notification.notifications.length) *
              100
            : 0,
        clickRate:
          notification.notifications.length > 0
            ? (notification.notifications.filter((un) => un.isClicked).length /
                notification.notifications.length) *
              100
            : 0,
      },
    }));
  } catch (error) {
    console.error("Error fetching campaigns:", error);
    return [];
  }
}

export type CampaignWithStats = Awaited<ReturnType<typeof getCampaigns>>[0];

export async function deleteCampaign(notificationId: string) {
  try {
    const prisma = getPrisma();
    await prisma.notification.delete({
      where: { id: notificationId },
    });

    revalidatePath("/admin/campaigns");
    return { success: true };
  } catch (error) {
    console.error("Error deleting campaign:", error);
    return { success: false, error: "Failed to delete campaign" };
  }
}

export async function getDeliveryZones() {
  try {
    const prisma = getPrisma();
    return await prisma.deliveryZone.findMany({
      select: {
        id: true,
        name: true,
        slug: true,
      },
    });
  } catch (error) {
    console.error("Error fetching delivery zones:", error);
    return [];
  }
}
