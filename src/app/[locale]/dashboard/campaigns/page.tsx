import { Suspense } from "react";
import { Card, CardContent } from "@udoy/components/ui/card";
import { CampaignForm } from "./components/campaign-form";
import { CampaignsList } from "./components/campaigns-list";
import { getCampaigns, getDeliveryZones } from "./actions";

export default async function CampaignsPage() {
  const [campaigns, deliveryZones] = await Promise.all([
    getCampaigns(),
    getDeliveryZones(),
  ]);

  return (
    <div
      className="px-4 md:px-8 py-8 space-y-8 overflow-y-auto"
      style={{ height: "calc(100vh - 64px)" }}
    >
      <div>
        <h1 className="text-3xl font-bold">Push Notification Campaigns</h1>
        <p className="text-gray-600 mt-2">
          Create and manage push notification campaigns for your users
        </p>
      </div>

      <CampaignForm deliveryZones={deliveryZones} />

      <Suspense
        fallback={
          <Card>
            <CardContent className="flex items-center justify-center py-12">
              <div className="text-center">Loading campaigns...</div>
            </CardContent>
          </Card>
        }
      >
        <CampaignsList campaigns={campaigns} />
      </Suspense>
    </div>
  );
}
