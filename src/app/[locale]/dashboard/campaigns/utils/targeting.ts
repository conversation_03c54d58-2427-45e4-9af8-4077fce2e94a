import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function getTargetedUsers(targetAudience: any): Promise<number[]> {
  try {
    const whereClause: any = {};

    if (targetAudience) {
      if (targetAudience.role) {
        whereClause.role = targetAudience.role;
      }

      if (targetAudience.zoneId) {
        whereClause.address = {
          some: {
            zoneId: targetAudience.zoneId,
          },
        };
      }

      if (targetAudience.userIds && targetAudience.userIds.length > 0) {
        whereClause.id = {
          in: targetAudience.userIds,
        };
      }
    }

    const users = await prisma.user.findMany({
      where: {
        pushSubscriptions: {
          some: {},
        },
        ...whereClause,
      },
      select: { id: true },
    });

    return users.map((user) => user.id);
  } catch (error) {
    console.error("Error getting targeted users:", error);
    return [];
  }
}

export function getAudienceDescription(targetAudience: any): string {
  if (!targetAudience) return "All Users";

  const parts = [];
  if (targetAudience.role) parts.push(`Role: ${targetAudience.role}`);
  if (targetAudience.zoneId) parts.push(`Zone: ${targetAudience.zoneId}`);
  if (targetAudience.userIds?.length)
    parts.push(`${targetAudience.userIds.length} specific users`);

  return parts.length > 0 ? parts.join(", ") : "All Users";
}
