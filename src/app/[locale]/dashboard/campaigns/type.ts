export interface NotificationFormData {
  title: string
  body: string
  imageUrl?: string
  scheduledFor: Date
  targetAudience?: {
    role?: string
    zoneId?: string
    userIds?: number[]
  }
  data?: Record<string, any>
}

export interface CampaignStats {
  totalSent: number
  totalSeen: number
  totalClicked: number
  deliveryRate: number
  openRate: number
  clickRate: number
}

export interface NotificationWithStats {
  id: string
  title: string
  body: string
  imageUrl?: string
  scheduledFor: Date
  isSent: boolean
  sentAt?: Date
  targetAudience?: any
  stats: CampaignStats
  createdAt: Date
}
