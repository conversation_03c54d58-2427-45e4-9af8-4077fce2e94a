"use client";

import { useState } from "react";
import { OrderStatus } from "@prisma/client";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import { Button } from "@udoy/components/ui/button";
import { Textarea } from "@udoy/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@udoy/components/ui/dialog";
import { updateOrderStatus } from "../orders/actions";
import useStatus from "@udoy/hooks/useToastUtil";
import { withError } from "@udoy/utils/app-error";

interface StatusUpdateFormProps {
  orderId: number;
  currentStatus: OrderStatus;
}

export function StatusUpdateForm({ orderId, currentStatus }: StatusUpdateFormProps) {
  const [status, setStatus] = useState<OrderStatus>(currentStatus);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<OrderStatus | null>(null);
  const [note, setNote] = useState("");
  const statusToast = useStatus();

  const handleStatusSelect = (newStatus: OrderStatus) => {
    if (newStatus === status) return;
    setSelectedStatus(newStatus);
    setIsDialogOpen(true);
  };

  const handleStatusChange = async () => {
    if (!selectedStatus) return;
    
    setIsUpdating(true);
    statusToast.loading("Updating order status...");
    
    try {
      await withError(
        updateOrderStatus(orderId, selectedStatus, note || undefined),
        "Failed to update order status"
      );
      setStatus(selectedStatus);
      statusToast.success(`Order status updated to ${selectedStatus.replace("_", " ")}`);
      setIsDialogOpen(false);
      setNote("");
    } catch (error: any) {
      statusToast.error(error.message || "Failed to update order status");
      console.error(error);
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <>
      <Select
        value={status}
        onValueChange={(value) => handleStatusSelect(value as OrderStatus)}
        disabled={isUpdating}
      >
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Change status" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectLabel>Order Status</SelectLabel>
            <SelectItem value={OrderStatus.PENDING}>Pending</SelectItem>
            <SelectItem value={OrderStatus.CONFIRMED}>Confirmed</SelectItem>
            <SelectItem value={OrderStatus.SHIPPING}>Shipping</SelectItem>
            <SelectItem value={OrderStatus.DELIVERED}>Delivered</SelectItem>
            <SelectItem value={OrderStatus.CANCELLED}>Cancelled</SelectItem>
            <SelectItem value={OrderStatus.RETURNED}>Returned</SelectItem>
          </SelectGroup>
        </SelectContent>
      </Select>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Update Order Status</DialogTitle>
            <DialogDescription>
              Change the status of order #{orderId} to {selectedStatus?.replace("_", " ")}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="note" className="text-sm font-medium">
                Add a note (optional)
              </label>
              <Textarea
                id="note"
                placeholder="Add details about this status change"
                value={note}
                onChange={(e) => setNote(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleStatusChange} disabled={isUpdating}>
              Update Status
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
