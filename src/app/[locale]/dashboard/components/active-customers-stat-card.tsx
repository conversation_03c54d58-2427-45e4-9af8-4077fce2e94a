import { Users } from "lucide-react";
import { StatCard } from "./stat-card";
import { getActiveCustomers, getCustomersChange } from "@udoy/libs/backend/stats";
import { CacheKey, DashboardStatType } from "@udoy/utils/cache-key";
import {
  unstable_cacheTag as cacheTag,
  unstable_cacheLife as cacheLife,
} from "next/cache";

export async function ActiveCustomersStatCard() {
  "use cache";
  cacheTag(
    CacheKey.DashboardStats(DashboardStatType.CUSTOMERS_ACTIVE),
    CacheKey.DashboardStats()
  );
  cacheLife("hours");

  const activeCustomers = await getActiveCustomers();
  const customersChange = await getCustomersChange();

  const formattedCustomers = `+${activeCustomers.toLocaleString()}`;

  const percentChange = customersChange.percentChange;
  const changePrefix = percentChange > 0 ? "+" : "";
  const changeDescription = `${changePrefix}${percentChange}% from last month`;

  return (
    <StatCard
      title="Active Customers"
      value={formattedCustomers}
      icon={Users}
      description={changeDescription}
    />
  );
}