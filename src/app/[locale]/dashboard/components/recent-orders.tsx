import { OrderStatus } from "@prisma/client";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@udoy/components/ui/avatar";
import { Badge } from "@udoy/components/ui/badge";
import { getOrdersCount, getRecentOrders } from "@udoy/libs/backend/orders";
import { CacheKey, DashboardStatType } from "@udoy/utils/cache-key";
import { formatDistanceToNow } from "date-fns";
import {
  unstable_cacheTag as cacheTag,
  unstable_cacheLife as cacheLife,
} from "next/cache";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import { Fragment } from "react";
import { Separator } from "@udoy/components/ui/separator";
import Hide from "@udoy/components/Hide";

export async function RecentOrders() {
  "use cache";
  cacheTag(CacheKey.DashboardStats(DashboardStatType.RECENT_ORDERS));
  cacheLife("hours");

  const orders = await getRecentOrders(6);
  const todayOrdersCount = await getOrdersCount("today");

  return (
    <Card className="col-span-3">
      <CardHeader>
        <CardTitle>Recent Orders</CardTitle>
        <CardDescription>
          You have received {todayOrdersCount} orders today.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {orders.map((order, index) => (
            <Fragment key={order.id}>
              <Hide open={index !== 0}>
                <Separator />
              </Hide>
              <div className="flex items-center justify-between space-x-4">
                <div className="flex items-center space-x-4">
                  <Avatar className="h-8 w-8">
                    <AvatarImage
                      src={order.customer.avatar || "/placeholder.svg"}
                      alt={order.customer.name}
                    />
                    <AvatarFallback>
                      {order.customer.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="text-sm font-medium leading-none">
                      {order.customer.name}
                    </p>
                    <p className="text-sm text-muted-foreground">#{order.id}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge
                    variant={
                      order.status === "DELIVERED"
                        ? "default"
                        : order.status === OrderStatus.CONFIRMED
                        ? "secondary"
                        : order.status === "PENDING"
                        ? "outline"
                        : "destructive"
                    }
                  >
                    {order.status.toLowerCase()}
                  </Badge>
                  <p className="text-sm font-medium">৳{order.total}</p>
                </div>
              </div>
            </Fragment>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
