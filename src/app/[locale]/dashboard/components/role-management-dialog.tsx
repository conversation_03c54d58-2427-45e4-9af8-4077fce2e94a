"use client";

import { useState } from "react";
import { But<PERSON> } from "@udoy/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@udoy/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import { UserCog } from "lucide-react";
import { updateUserRoles } from "@udoy/actions/user";
import { toast } from "sonner";
import { Role, User } from "@prisma/client";

interface RoleManagementDialogProps {
  customers: User[];
  onRoleChange: (customerIds: number[], newRole: Role) => void;
  selectedCustomers?: number[];
}

export function RoleManagementDialog({
  customers,
  onRoleChange,
  selectedCustomers = [],
}: RoleManagementDialogProps) {
  const [open, setOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleRoleChange = async () => {
    if (selectedRole && selectedCustomers.length > 0) {
      setIsLoading(true);
      try {
        // Call the server action to update roles
        const result = await updateUserRoles(
          selectedCustomers,
          selectedRole as any
        );

        if (result.success) {
          // Update the UI optimistically
          onRoleChange(selectedCustomers, selectedRole);
          toast.success("Roles updated", {
            description: `Updated ${
              selectedCustomers.length
            } customer(s) to ${selectedRole.replace("_", " ")}`,
          });
        } else {
          toast.error("Error", {
            description: result.error || "Failed to update roles",
          });
        }
      } catch (error) {
        toast.error("Error", {
          description: "An unexpected error occurred",
        });
      } finally {
        setIsLoading(false);
        setOpen(false);
        setSelectedRole(null);
      }
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button disabled={selectedCustomers.length === 0}>
          <UserCog className="mr-2 h-4 w-4" />
          Manage Roles ({selectedCustomers.length})
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Manage Customer Roles</DialogTitle>
          <DialogDescription>
            Change roles for {selectedCustomers.length} selected customer(s).
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <label htmlFor="role" className="text-right">
              New Role
            </label>
            <Select
              onValueChange={(value) => setSelectedRole(value as Role)}
              value={selectedRole || undefined}
            >
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select a role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={Role.SUPER_ADMIN}>Super Admin</SelectItem>
                <SelectItem value={Role.ADMIN}>Admin</SelectItem>
                <SelectItem value={Role.MAINTAINER}>Maintainer</SelectItem>
                <SelectItem value={Role.USER}>User</SelectItem>
                <SelectItem value={Role.DELIVERY_MAN}>Delivery Man</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <DialogFooter>
          <Button
            onClick={handleRoleChange}
            disabled={!selectedRole || isLoading}
            // isLoading={isLoading}
          >
            Save changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
