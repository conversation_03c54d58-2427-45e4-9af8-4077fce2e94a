"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@udoy/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@udoy/components/ui/tabs"
import { Textarea } from "@udoy/components/ui/textarea"
import {
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Link,
  ImageIcon,
  Code,
} from "lucide-react"

interface RichTextEditorProps {
  value: string
  onChange: (value: string) => void
}

export function RichTextEditor({ value, onChange }: RichTextEditorProps) {
  const [activeTab, setActiveTab] = useState<"visual" | "html">("visual")
  const editorRef = useRef<HTMLDivElement>(null)
  const [htmlValue, setHtmlValue] = useState(value)

  // Initialize the editor
  useEffect(() => {
    if (editorRef.current) {
      editorRef.current.innerHTML = value
    }
  }, [value])

  // Handle content changes in visual editor
  const handleContentChange = () => {
    if (editorRef.current) {
      const newValue = editorRef.current.innerHTML
      setHtmlValue(newValue)
      onChange(newValue)
    }
  }

  // Handle HTML textarea changes
  const handleHtmlChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value
    setHtmlValue(newValue)
    onChange(newValue)
    if (editorRef.current) {
      editorRef.current.innerHTML = newValue
    }
  }

  // Execute command on the editor
  const execCommand = (command: string, value = "") => {
    document.execCommand(command, false, value)
    handleContentChange()
    editorRef.current?.focus()
  }

  // Handle link insertion
  const handleLink = () => {
    const url = prompt("Enter URL:", "https://")
    if (url) {
      execCommand("createLink", url)
    }
  }

  // Handle image insertion
  const handleImage = () => {
    const url = prompt("Enter image URL:", "https://")
    if (url) {
      execCommand("insertImage", url)
    }
  }

  return (
    <div className="border rounded-md">
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "visual" | "html")}>
        <div className="flex items-center justify-between border-b px-3 py-2">
          <TabsList>
            <TabsTrigger value="visual">Visual</TabsTrigger>
            <TabsTrigger value="html">HTML</TabsTrigger>
          </TabsList>
          {activeTab === "visual" && (
            <div className="flex items-center space-x-1">
              <Button type="button" variant="ghost" size="icon" className="h-8 w-8" onClick={() => execCommand("bold")}>
                <Bold className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => execCommand("italic")}
              >
                <Italic className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => execCommand("underline")}
              >
                <Underline className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => execCommand("insertUnorderedList")}
              >
                <List className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => execCommand("insertOrderedList")}
              >
                <ListOrdered className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => execCommand("justifyLeft")}
              >
                <AlignLeft className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => execCommand("justifyCenter")}
              >
                <AlignCenter className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => execCommand("justifyRight")}
              >
                <AlignRight className="h-4 w-4" />
              </Button>
              <Button type="button" variant="ghost" size="icon" className="h-8 w-8" onClick={handleLink}>
                <Link className="h-4 w-4" />
              </Button>
              <Button type="button" variant="ghost" size="icon" className="h-8 w-8" onClick={handleImage}>
                <ImageIcon className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => execCommand("formatBlock", "<pre>")}
              >
                <Code className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
        <TabsContent value="visual" className="p-0">
          <div
            ref={editorRef}
            contentEditable
            className="min-h-[200px] p-4 focus:outline-none"
            onInput={handleContentChange}
            onBlur={handleContentChange}
          />
        </TabsContent>
        <TabsContent value="html" className="p-0">
          <Textarea
            value={htmlValue}
            onChange={handleHtmlChange}
            className="min-h-[200px] rounded-none border-0 font-mono text-sm"
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}
