"use client";

import {
  OrderStatus,
  OrderTimeline as PrismaOrderTimeline,
} from "@prisma/client";
import {
  Circle,
  CheckCircle,
  Package,
  Truck,
  XCircle,
  RefreshCcw,
} from "lucide-react";

interface OrderTimelineProps {
  timeline: PrismaOrderTimeline[];
}

// Helper function to format date
function formatDate(dateString: Date) {
  return new Date(dateString).toLocaleString(undefined, {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  });
}

// Helper function to get status icon
function getStatusIcon(status: OrderStatus) {
  switch (status) {
    case OrderStatus.PENDING:
      return <Circle className="h-5 w-5 text-muted-foreground" />;
    case OrderStatus.CONFIRMED:
      return <Package className="h-5 w-5 text-blue-500" />;
    case OrderStatus.SHIPPING:
      return <Truck className="h-5 w-5 text-yellow-500" />;
    case OrderStatus.DELIVERED:
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    case OrderStatus.CANCELLED:
      return <XCircle className="h-5 w-5 text-red-500" />;
    case OrderStatus.RETURNED:
      return <RefreshCcw className="h-5 w-5 text-red-500" />;
    default:
      return <Circle className="h-5 w-5 text-muted-foreground" />;
  }
}

export function OrderTimeline({ timeline }: OrderTimelineProps) {
  // Sort timeline events by timestamp (newest first)
  // const sortedTimeline = [...timeline].sort(
  //   (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  // );

  return (
    <div className="space-y-6">
      {timeline.map((event, index) => (
        <div key={index} className="flex gap-4">
          <div className="flex flex-col items-center">
            <div className="flex min-h-8 min-w-8  items-center justify-center rounded-full border bg-background">
              {getStatusIcon(event.status)}
            </div>
            {index !== timeline.length - 1 && (
              <div className="h-full w-px bg-border" />
            )}
          </div>
          <div className="flex flex-col gap-1 pb-6">
            <p className="text-sm font-medium leading-none">
              {event.status.replace("_", " ")}
            </p>
            <p className="text-sm text-muted-foreground">
              {formatDate(event.createdAt)}
            </p>
            {event.note && <p className="text-sm">{event.note}</p>}
          </div>
        </div>
      ))}
    </div>
  );
}
