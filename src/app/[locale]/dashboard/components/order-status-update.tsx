"use client";

import { useState } from "react";
import { But<PERSON> } from "@udoy/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  Di<PERSON>Title,
  DialogTrigger,
} from "@udoy/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import { Textarea } from "@udoy/components/ui/textarea";
import { toast } from "sonner";

// Define the OrderStatus enum to match the Prisma schema
enum OrderStatus {
  PENDING = "PENDING",
  CANCELLED = "CANCELLED",
  PROCESSING = "PROCESSING",
  DELIVERY = "DELIVERY",
  DELIVERED = "DELIVERED",
  RETURNED = "RETURNED",
}

interface OrderStatusUpdateProps {
  orderId: number;
  currentStatus: OrderStatus;
  onStatusChange: (status: OrderStatus, note: string) => void;
}

export function OrderStatusUpdate({
  orderId,
  currentStatus,
  onStatusChange,
}: OrderStatusUpdateProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<OrderStatus | null>(
    null
  );
  const [note, setNote] = useState("");

  const handleSubmit = () => {
    if (!selectedStatus) {
      return;
    }

    onStatusChange(selectedStatus, note);
    setIsOpen(false);
    setSelectedStatus(null);
    setNote("");

    toast("Order status updated", {
      description: `Order #${orderId} status changed to ${selectedStatus.replace(
        "_",
        " "
      )}`,
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button>Update Status</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Update Order Status</DialogTitle>
          <DialogDescription>
            Change the status of order #{orderId}. Current status:{" "}
            {currentStatus.replace("_", " ")}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <label htmlFor="status" className="text-sm font-medium">
              New Status
            </label>
            <Select
              onValueChange={(value) => setSelectedStatus(value as OrderStatus)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select new status" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectLabel>Order Status</SelectLabel>
                  <SelectItem value={OrderStatus.PENDING}>Pending</SelectItem>
                  <SelectItem value={OrderStatus.PROCESSING}>
                    Processing
                  </SelectItem>
                  <SelectItem value={OrderStatus.DELIVERY}>
                    In Delivery
                  </SelectItem>
                  <SelectItem value={OrderStatus.DELIVERED}>
                    Delivered
                  </SelectItem>
                  <SelectItem value={OrderStatus.CANCELLED}>
                    Cancelled
                  </SelectItem>
                  <SelectItem value={OrderStatus.RETURNED}>Returned</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-2">
            <label htmlFor="note" className="text-sm font-medium">
              Note (Optional)
            </label>
            <Textarea
              id="note"
              placeholder="Add a note about this status change"
              value={note}
              onChange={(e) => setNote(e.target.value)}
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={!selectedStatus}>
            Update Status
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
