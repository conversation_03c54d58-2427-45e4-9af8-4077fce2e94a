import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@udoy/components/ui/table";
import { Badge } from "@udoy/components/ui/badge";
import { Button } from "@udoy/components/ui/button";
import Link from "next/link";
import { OrderStatus } from "@prisma/client";
import { OrderWithItems } from "@udoy/utils/types";

interface CustomerOrdersTableProps {
  orders: OrderWithItems[];
}

export function CustomerOrdersTable({ orders }: CustomerOrdersTableProps) {
  // Format date for display
  const formatDate = (dateString: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(dateString);
  };

  // Map OrderStatus to badge variant
  const getStatusVariant = (status: OrderStatus) => {
    switch (status) {
      case "DELIVERED":
        return "default";
      case "SHIPPING":
        return "secondary";
      case "CONFIRMED":
        return "success";
      case "PENDING":
        return "outline";
      case "CANCELLED":
        return "destructive";
      case "RETURNED":
        return "warning";
      default:
        return "outline";
    }
  };

  // Calculate total for an order
  const calculateTotal = (order: OrderWithItems) => {
    return order.subTotal + order.shipping - order.discount;
  };

  return (
    <div className="rounded-md border">
      <Table className="">
        <TableHeader className="">
          <TableRow className="">
            <TableHead>Order ID</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Items</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Total</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody className="">
          {orders.length > 0 ? (
            orders.map((order) => (
              <TableRow key={order.id}>
                <TableCell className="font-medium">#{order.id}</TableCell>
                <TableCell>{formatDate(order.createdAt)}</TableCell>
                <TableCell>{order.orderItems.length}</TableCell>
                <TableCell>
                  <Badge variant={getStatusVariant(order.status) as any}>
                    {order.status.replace("_", " ")}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  ৳{calculateTotal(order).toFixed(2)}
                </TableCell>
                <TableCell className="text-right">
                  <Button variant="ghost" size="sm" asChild>
                    <Link href={`/dashboard/orders/${order.id}`}>View</Link>
                  </Button>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell
                colSpan={6}
                className="text-center py-6 text-muted-foreground"
              >
                No orders found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}
