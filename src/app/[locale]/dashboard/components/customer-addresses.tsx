"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardFooter } from "@udoy/components/ui/card";
import { Button } from "@udoy/components/ui/button";
import { Badge } from "@udoy/components/ui/badge";
import { MapPin, Edit, Trash2, Plus } from "lucide-react";
import { Prisma } from "@prisma/client";
import { AddressEditDialog } from "./address-edit-dialog";

type AddressInfo = Prisma.AddressGetPayload<{
  include: { zone: true };
}>;
interface CustomerAddressesProps {
  addresses: AddressInfo[];
  currentAddressId?: string;
  zones: Prisma.DeliveryZoneGetPayload<{
    include: { subZones: true };
  }>[];
}

export function CustomerAddresses({
  addresses,
  currentAddressId,
  zones,
}: CustomerAddressesProps) {
  const [customerAddresses, setCustomerAddresses] =
    useState<AddressInfo[]>(addresses);
  const [editingAddress, setEditingAddress] = useState<AddressInfo | null>(null);

  // Function to set an address as default
  const setAsDefault = (id: string) => {
    setCustomerAddresses(
      customerAddresses.map((address) => ({
        ...address,
        isDefault: address.id === id,
      }))
    );
  };

  // Function to delete an address
  const deleteAddress = (id: string) => {
    setCustomerAddresses(
      customerAddresses.filter((address) => address.id !== id)
    );
  };
  
  // Function to handle address update
  const handleAddressUpdated = (updatedAddress: AddressInfo) => {
    setCustomerAddresses(
      customerAddresses.map((address) => 
        address.id === updatedAddress.id ? updatedAddress : address
      )
    );
  };

  return (
    <div className="space-y-4">
      

      {customerAddresses.length > 0 ? (
        <div className="grid gap-4 md:grid-cols-2">
          {customerAddresses.map((address) => (
            <Card key={address.id} className="relative">
              <CardContent className="pt-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-2">
                    <MapPin className="h-4 w-4 mt-0.5 text-muted-foreground" />
                    <div>
                      <p className="font-medium">{address.label} - {address.phone}</p>
                      <p className="text-sm text-muted-foreground">
                        {address.home}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {address.zone.name}
                      </p>
                    </div>
                  </div>
                  {address.id === currentAddressId && (
                    <Badge variant="outline" className="ml-auto">
                      Default
                    </Badge>
                  )}
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button
                  variant="ghost"
                  size="sm"
                  disabled={address.id === currentAddressId}
                  onClick={() => setAsDefault(address.id)}
                >
                  Set as Default
                </Button>
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    size="icon"
                    onClick={() => setEditingAddress(address)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    disabled={address.id === currentAddressId}
                    onClick={() => deleteAddress(address.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-8 text-muted-foreground">
          No addresses found
        </div>
      )}
      
      {editingAddress && (
        <AddressEditDialog
          isOpen={!!editingAddress}
          onClose={() => setEditingAddress(null)}
          address={editingAddress}
          zones={zones}
          onAddressUpdated={handleAddressUpdated}
        />
      )}
    </div>
  );
}
