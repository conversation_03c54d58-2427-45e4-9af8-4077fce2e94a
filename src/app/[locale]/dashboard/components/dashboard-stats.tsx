import { Users, Timer } from "lucide-react"
import { StatCard } from "./stat-card"
import { RevenueStatCard } from "./revenue-stat-card"
import { OrdersStatCard } from "./orders-stat-card"
import { DeliveryTimeStatCard } from "./delivery-time-stat-card"
import { ActiveCustomersStatCard } from "./active-customers-stat-card"

export async function DashboardStats() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <RevenueStatCard />
      <OrdersStatCard />
      <DeliveryTimeStatCard />
      <ActiveCustomersStatCard />
    </div>
  )
}
