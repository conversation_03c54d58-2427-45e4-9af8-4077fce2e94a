"use client";

import type React from "react";

import { useState } from "react";
import { But<PERSON> } from "@udoy/components/ui/button";
import { Input } from "@udoy/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@udoy/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@udoy/components/ui/dialog";
import { Label } from "@udoy/components/ui/label";
import { Edit, Trash2, Plus } from "lucide-react";
import { toast } from "sonner";

interface QuantityUnit {
  id: string;
  slug: string;
  full: string;
  productCount?: number;
}

interface ProductUnitsProps {
  units: QuantityUnit[];
}

export function ProductUnits({ units: initialUnits }: ProductUnitsProps) {
  const [units, setUnits] = useState<QuantityUnit[]>(initialUnits);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentUnit, setCurrentUnit] = useState<QuantityUnit | null>(null);
  const [newUnit, setNewUnit] = useState({ slug: "", full: "" });

  // Handle input change in add/edit dialog
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    field: "slug" | "full",
    isNew = true
  ) => {
    const value = e.target.value;

    if (isNew) {
      setNewUnit({ ...newUnit, [field]: value });
    } else if (currentUnit) {
      setCurrentUnit({ ...currentUnit, [field]: value });
    }
  };

  // Add new unit
  const addUnit = () => {
    if (!newUnit.slug || !newUnit.full) return;

    const newId = `unit_${Date.now()}`;
    const newUnitWithId = {
      id: newId,
      slug: newUnit.slug,
      full: newUnit.full,
      productCount: 0,
    };

    setUnits([...units, newUnitWithId]);
    setNewUnit({ slug: "", full: "" });
    setIsAddDialogOpen(false);

    toast("Unit added", {
      description: `${newUnitWithId.full} (${newUnitWithId.slug}) has been added successfully.`,
    });
  };

  // Edit unit
  const editUnit = () => {
    if (!currentUnit || !currentUnit.slug || !currentUnit.full) return;

    const updatedUnits = units.map((unit) =>
      unit.id === currentUnit.id
        ? {
            ...unit,
            slug: currentUnit.slug,
            full: currentUnit.full,
          }
        : unit
    );

    setUnits(updatedUnits);
    setCurrentUnit(null);
    setIsEditDialogOpen(false);

    toast("Unit updated", {
      description: `${currentUnit.full} (${currentUnit.slug}) has been updated successfully.`,
    });
  };

  // Delete unit
  const deleteUnit = () => {
    if (!currentUnit) return;

    const updatedUnits = units.filter((unit) => unit.id !== currentUnit.id);
    setUnits(updatedUnits);
    setCurrentUnit(null);
    setIsDeleteDialogOpen(false);

    toast("Unit deleted", {
      description: `${currentUnit.full} (${currentUnit.slug}) has been deleted successfully.`,
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">Quantity Units</h2>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Unit
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Quantity Unit</DialogTitle>
              <DialogDescription>
                Create a new quantity unit for products. The slug is a short
                abbreviation, while the full name is the complete unit name.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="slug">Slug (Abbreviation)</Label>
                <Input
                  id="slug"
                  value={newUnit.slug}
                  onChange={(e) => handleInputChange(e, "slug")}
                  placeholder="e.g. kg, g, l, ml"
                  maxLength={10}
                />
                <p className="text-xs text-muted-foreground">
                  {`A short abbreviation for the unit, like "kg" for kilogram.`}
                </p>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="full">Full Name</Label>
                <Input
                  id="full"
                  value={newUnit.full}
                  onChange={(e) => handleInputChange(e, "full")}
                  placeholder="e.g. Kilogram, Gram, Liter"
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsAddDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={addUnit}
                disabled={!newUnit.slug || !newUnit.full}
              >
                Add Unit
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Slug</TableHead>
              <TableHead>Full Name</TableHead>
              <TableHead className="text-right">Products</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {units.length > 0 ? (
              units.map((unit) => (
                <TableRow key={unit.id}>
                  <TableCell className="font-medium">{unit.slug}</TableCell>
                  <TableCell>{unit.full}</TableCell>
                  <TableCell className="text-right">
                    {unit.productCount || 0}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          setCurrentUnit(unit);
                          setIsEditDialogOpen(true);
                        }}
                      >
                        <Edit className="h-4 w-4" />
                        <span className="sr-only">Edit</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          setCurrentUnit(unit);
                          setIsDeleteDialogOpen(true);
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                        <span className="sr-only">Delete</span>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={4} className="h-24 text-center">
                  No units found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Edit Unit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Quantity Unit</DialogTitle>
            <DialogDescription>
              Update the quantity unit details.
            </DialogDescription>
          </DialogHeader>
          {currentUnit && (
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-slug">Slug (Abbreviation)</Label>
                <Input
                  id="edit-slug"
                  value={currentUnit.slug}
                  onChange={(e) => handleInputChange(e, "slug", false)}
                  placeholder="e.g. kg, g, l, ml"
                  maxLength={10}
                />
                <p className="text-xs text-muted-foreground">
                  {`A short abbreviation for the unit, like "kg" for kilogram.`}
                </p>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-full">Full Name</Label>
                <Input
                  id="edit-full"
                  value={currentUnit.full}
                  onChange={(e) => handleInputChange(e, "full", false)}
                  placeholder="e.g. Kilogram, Gram, Liter"
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={editUnit}
              disabled={!currentUnit?.slug || !currentUnit?.full}
            >
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Unit Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Quantity Unit</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this quantity unit? This action
              cannot be undone.
            </DialogDescription>
          </DialogHeader>
          {currentUnit && (
            <div className="py-4">
              <p>
                You are about to delete the unit{" "}
                <strong>{currentUnit.full}</strong> ({currentUnit.slug}).
              </p>
              {(currentUnit.productCount || 0) > 0 && (
                <p className="mt-2 text-destructive">
                  Warning: This unit is used by {currentUnit.productCount}{" "}
                  products. Deleting it will affect these products.
                </p>
              )}
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={deleteUnit}>
              Delete Unit
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
