"use client";

import { useState, useEffect } from "react";
import { SummaryChartData } from "./SummaryOverviewChart";

type TimeRange = "daily" | "weekly" | "monthly" | "yearly";

export function useSummaryData(timeRange: TimeRange) {
  const [data, setData] = useState<SummaryChartData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchSummaryData() {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/dashboard/summary-data?timeRange=${timeRange}`);
        
        if (!response.ok) {
          throw new Error(`Failed to fetch data: ${response.statusText}`);
        }

        const result = await response.json();
        setData(result.data || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : "An error occurred");
        setData([]);
      } finally {
        setLoading(false);
      }
    }

    fetchSummaryData();
  }, [timeRange]);

  return { data, loading, error };
}
