"use client";

import type React from "react";

import { useState } from "react";
import { But<PERSON> } from "@udoy/components/ui/button";
import { Input } from "@udoy/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@udoy/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@udoy/components/ui/dialog";
import { Label } from "@udoy/components/ui/label";
import { Edit, Trash2, Plus } from "lucide-react";
import { toast } from "sonner";

interface Category {
  id: string;
  name: string;
  slug: string;
  productCount?: number;
}

interface ProductCategoriesProps {
  categories: Category[];
}

export function ProductCategories({
  categories: initialCategories,
}: ProductCategoriesProps) {
  const [categories, setCategories] = useState<Category[]>(initialCategories);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentCategory, setCurrentCategory] = useState<Category | null>(null);
  const [newCategory, setNewCategory] = useState({ name: "", slug: "" });

  // Generate slug from name
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/^-|-$/g, "");
  };

  // Handle name change in add/edit dialog
  const handleNameChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    isNew = true
  ) => {
    const name = e.target.value;
    const slug = generateSlug(name);

    if (isNew) {
      setNewCategory({ name, slug });
    } else if (currentCategory) {
      setCurrentCategory({ ...currentCategory, name, slug });
    }
  };

  // Handle slug change in add/edit dialog
  const handleSlugChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    isNew = true
  ) => {
    const slug = generateSlug(e.target.value);

    if (isNew) {
      setNewCategory({ ...newCategory, slug });
    } else if (currentCategory) {
      setCurrentCategory({ ...currentCategory, slug });
    }
  };

  // Add new category
  const addCategory = () => {
    if (!newCategory.name) return;

    const newId = `cat_${Date.now()}`;
    const newCategoryWithId = {
      id: newId,
      name: newCategory.name,
      slug: newCategory.slug || generateSlug(newCategory.name),
      productCount: 0,
    };

    setCategories([...categories, newCategoryWithId]);
    setNewCategory({ name: "", slug: "" });
    setIsAddDialogOpen(false);

    toast("Category added", {
      description: `${newCategoryWithId.name} has been added successfully.`,
    });
  };

  // Edit category
  const editCategory = () => {
    if (!currentCategory || !currentCategory.name) return;

    const updatedCategories = categories.map((category) =>
      category.id === currentCategory.id
        ? {
            ...category,
            name: currentCategory.name,
            slug: currentCategory.slug || generateSlug(currentCategory.name),
          }
        : category
    );

    setCategories(updatedCategories);
    setCurrentCategory(null);
    setIsEditDialogOpen(false);

    toast("Category updated", {
      description: `${currentCategory.name} has been updated successfully.`,
    });
  };

  // Delete category
  const deleteCategory = () => {
    if (!currentCategory) return;

    const updatedCategories = categories.filter(
      (category) => category.id !== currentCategory.id
    );
    setCategories(updatedCategories);
    setCurrentCategory(null);
    setIsDeleteDialogOpen(false);

    toast("Category deleted", {
      description: `${currentCategory.name} has been deleted successfully.`,
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">Product Categories</h2>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Category
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Category</DialogTitle>
              <DialogDescription>
                Create a new product category. The slug will be automatically
                generated from the name.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Category Name</Label>
                <Input
                  id="name"
                  value={newCategory.name}
                  onChange={(e) => handleNameChange(e)}
                  placeholder="e.g. Fresh Fruits"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="slug">Slug</Label>
                <Input
                  id="slug"
                  value={newCategory.slug}
                  onChange={(e) => handleSlugChange(e)}
                  placeholder="e.g. fresh-fruits"
                />
                <p className="text-xs text-muted-foreground">
                  The slug is used in URLs and should contain only lowercase
                  letters, numbers, and hyphens.
                </p>
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsAddDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button onClick={addCategory} disabled={!newCategory.name}>
                Add Category
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Slug</TableHead>
              <TableHead className="text-right">Products</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {categories.length > 0 ? (
              categories.map((category) => (
                <TableRow key={category.id}>
                  <TableCell className="font-medium">{category.name}</TableCell>
                  <TableCell>{category.slug}</TableCell>
                  <TableCell className="text-right">
                    {category.productCount || 0}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          setCurrentCategory(category);
                          setIsEditDialogOpen(true);
                        }}
                      >
                        <Edit className="h-4 w-4" />
                        <span className="sr-only">Edit</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          setCurrentCategory(category);
                          setIsDeleteDialogOpen(true);
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                        <span className="sr-only">Delete</span>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={4} className="h-24 text-center">
                  No categories found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Edit Category Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Category</DialogTitle>
            <DialogDescription>
              Update the category details. The slug will be automatically
              updated if you change the name.
            </DialogDescription>
          </DialogHeader>
          {currentCategory && (
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-name">Category Name</Label>
                <Input
                  id="edit-name"
                  value={currentCategory.name}
                  onChange={(e) => handleNameChange(e, false)}
                  placeholder="e.g. Fresh Fruits"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-slug">Slug</Label>
                <Input
                  id="edit-slug"
                  value={currentCategory.slug}
                  onChange={(e) => handleSlugChange(e, false)}
                  placeholder="e.g. fresh-fruits"
                />
                <p className="text-xs text-muted-foreground">
                  The slug is used in URLs and should contain only lowercase
                  letters, numbers, and hyphens.
                </p>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button onClick={editCategory} disabled={!currentCategory?.name}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Category Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Category</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this category? This action cannot
              be undone.
            </DialogDescription>
          </DialogHeader>
          {currentCategory && (
            <div className="py-4">
              <p>
                You are about to delete the category{" "}
                <strong>{currentCategory.name}</strong>.
              </p>
              {(currentCategory.productCount || 0) > 0 && (
                <p className="mt-2 text-destructive">
                  Warning: This category contains {currentCategory.productCount}{" "}
                  products. Deleting it will remove the category from these
                  products.
                </p>
              )}
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={deleteCategory}>
              Delete Category
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
