import React from "react";
import { Overview } from "./overview";
import { getMonthlyRevenue } from "@udoy/libs/backend/stats";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import { C<PERSON><PERSON><PERSON>, DashboardStatType } from "@udoy/utils/cache-key";
import {
  unstable_cacheTag as cacheTag,
  unstable_cacheLife as cacheLife,
} from "next/cache";

async function SalesOverview() {
  "use cache";
  cacheTag(CacheKey.DashboardStats(DashboardStatType.RECENT_ORDERS));
  cacheLife("weeks");
  
  const data = await getMonthlyRevenue();

  return (
    <Card className="col-span-4">
      <CardHeader>
        <CardTitle>Overview</CardTitle>
        <CardDescription>
          View your sales and order statistics for the past 30 days.
        </CardDescription>
      </CardHeader>
      <CardContent className="pl-2">
        <Overview data={data} />
      </CardContent>
    </Card>
  );
}

export default SalesOverview;
