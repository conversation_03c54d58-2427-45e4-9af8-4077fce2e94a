"use client";

import { useState } from "react";
import { Bell, Search, Settings, User, Menu, Home, LogOut } from "lucide-react";

import { Button } from "@udoy/components/ui/button";
import { Input } from "@udoy/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@udoy/components/ui/dropdown-menu";
import { Sheet, SheetContent, SheetTrigger } from "@udoy/components/ui/sheet";
import { Sidebar } from "./sidebar";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@udoy/components/ui/avatar";
import { useSelector } from "@xstate/store/react";
import { store } from "@udoy/state";
import Link from "next/link";
import SearchProducts from "../../(client)/components/SearchProducts";
import { usePathname, useSelectedLayoutSegment } from "next/navigation";
import Hide from "@udoy/components/Hide";

export function TopBar() {
  const [notifications, setNotifications] = useState(3);
  const me = useSelector(store, (state) => state.context.me);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const segment = useSelectedLayoutSegment();

  return (
    <header className="sticky top-0 z-30 flex h-16 items-center gap-4 border-b bg-background px-4 md:px-6">
      <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
        <SheetTrigger asChild>
          <Button variant="outline" size="icon" className="md:hidden">
            <Menu className="h-5 w-5" />
            <span className="sr-only">Toggle Menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="p-0">
          <Sidebar onClick={() => setSidebarOpen(false)} className="w-full" />
        </SheetContent>
      </Sheet>

      <div className="flex-1">
        <Hide open={segment === "manage"}>
          <SearchProducts
            className="max-w-sm mx-auto"
            searchPage="/dashboard/manage/search"
          />
        </Hide>
      </div>

      <div className="flex items-center gap-2">
        {/* <Button variant="outline" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {notifications > 0 && (
            <span className="absolute right-1 top-1 flex h-4 w-4 items-center justify-center rounded-full bg-primary text-[10px] font-medium text-primary-foreground">
              {notifications}
            </span>
          )}
          <span className="sr-only">Notifications</span>
        </Button> */}

        {/* <Button variant="outline" size="icon">
          <Settings className="h-5 w-5" />
          <span className="sr-only">Settings</span>
        </Button> */}

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon" className="rounded-full">
              <Avatar className="size-8 rounded-full">
                <AvatarImage src={me?.avatar!} alt={me?.name} />
                <AvatarFallback className="rounded-lg">CN</AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>My Account</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <Link href="/">
              <DropdownMenuItem>
                <Home className="mr-2 h-4 w-4" />
                <span>Home</span>
              </DropdownMenuItem>
            </Link>
            <DropdownMenuItem>
              <User className="mr-2 h-4 w-4" />
              <span>Profile</span>
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Settings className="mr-2 h-4 w-4" />
              <span>Settings</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <LogOut />
              <span>Log out</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
}
