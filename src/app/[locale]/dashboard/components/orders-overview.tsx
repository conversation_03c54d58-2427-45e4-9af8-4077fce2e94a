import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@udoy/components/ui/card"
import { Badge } from "@udoy/components/ui/badge"

// Define the OrderStatus enum to match the Prisma schema
enum OrderStatus {
  PENDING = "PENDING",
  CANCELLED = "CANCELLED",
  PROCESSING = "PROCESSING",
  DELIVERY = "DELIVERY",
  DELIVERED = "DELIVERED",
  RETURNED = "RETURNED",
}

interface OrdersOverviewProps {
  totalOrders: number
  pendingOrders: number
  processingOrders: number
  deliveryOrders: number
  deliveredOrders: number
  cancelledOrders: number
  returnedOrders: number
  revenueToday: number
  revenueThisWeek: number
  revenueThisMonth: number
}

// Helper function to format currency
function formatCurrency(amount: number) {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
  }).format(amount / 100)
}

export function OrdersOverview({
  totalOrders,
  pendingOrders,
  processingOrders,
  deliveryOrders,
  deliveredOrders,
  cancelledOrders,
  returnedOrders,
  revenueToday,
  revenueThisWeek,
  revenueThisMonth,
}: OrdersOverviewProps) {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle>Total Orders</CardTitle>
          <CardDescription>All orders in the system</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold">{totalOrders}</div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle>Revenue Today</CardTitle>
          <CardDescription>Orders placed today</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold">{formatCurrency(revenueToday)}</div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle>Revenue This Week</CardTitle>
          <CardDescription>Last 7 days</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold">{formatCurrency(revenueThisWeek)}</div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle>Revenue This Month</CardTitle>
          <CardDescription>Current month</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold">{formatCurrency(revenueThisMonth)}</div>
        </CardContent>
      </Card>

      <Card className="md:col-span-2 lg:col-span-4">
        <CardHeader>
          <CardTitle>Order Status Overview</CardTitle>
          <CardDescription>Current order status distribution</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-6">
            <div className="flex flex-col items-center justify-center rounded-lg border p-4">
              <Badge variant="outline" className="mb-2">
                Pending
              </Badge>
              <span className="text-2xl font-bold">{pendingOrders}</span>
            </div>
            <div className="flex flex-col items-center justify-center rounded-lg border p-4">
              <Badge variant="secondary" className="mb-2">
                Processing
              </Badge>
              <span className="text-2xl font-bold">{processingOrders}</span>
            </div>
            <div className="flex flex-col items-center justify-center rounded-lg border p-4">
              <Badge className="mb-2 bg-yellow-500 hover:bg-yellow-600">In Delivery</Badge>
              <span className="text-2xl font-bold">{deliveryOrders}</span>
            </div>
            <div className="flex flex-col items-center justify-center rounded-lg border p-4">
              <Badge className="mb-2">Delivered</Badge>
              <span className="text-2xl font-bold">{deliveredOrders}</span>
            </div>
            <div className="flex flex-col items-center justify-center rounded-lg border p-4">
              <Badge variant="destructive" className="mb-2">
                Cancelled
              </Badge>
              <span className="text-2xl font-bold">{cancelledOrders}</span>
            </div>
            <div className="flex flex-col items-center justify-center rounded-lg border p-4">
              <Badge variant="destructive" className="mb-2">
                Returned
              </Badge>
              <span className="text-2xl font-bold">{returnedOrders}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
