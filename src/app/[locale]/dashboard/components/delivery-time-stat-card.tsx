import { Timer } from "lucide-react";
import { StatCard } from "./stat-card";
import { getAverageDeliveryTime, getDeliveryTimeChange } from "@udoy/libs/backend/stats";
import { CacheKey, DashboardStatType } from "@udoy/utils/cache-key";
import {
  unstable_cacheTag as cacheTag,
  unstable_cacheLife as cacheLife,
} from "next/cache";

export async function DeliveryTimeStatCard() {
  "use cache";
  cacheTag(
    CacheKey.DashboardStats(DashboardStatType.DELIVERY_TIME_AVG),
    CacheKey.DashboardStats()
  );
  cacheLife("hours");

  const avgDeliveryMinutes = await getAverageDeliveryTime();
  const deliveryTimeChange = await getDeliveryTimeChange();

  // Format the time (e.g., "1h 22m")
  const hours = Math.floor(avgDeliveryMinutes / 60);
  const minutes = avgDeliveryMinutes % 60;
  const formattedTime = `${hours}h ${minutes}m`;

  // Format the change description
  const changeInMinutes = deliveryTimeChange.changeInMinutes;
  const isFaster = changeInMinutes > 0;
  const changeDescription = isFaster 
    ? `${changeInMinutes}m faster than last month`
    : `${Math.abs(changeInMinutes)}m slower than last month`;

  return (
    <StatCard
      title="Average Delivery Time"
      value={formattedTime}
      icon={Timer}
      description={changeDescription}
    />
  );
}