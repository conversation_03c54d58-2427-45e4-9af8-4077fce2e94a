"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@udoy/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@udoy/components/ui/form";
import { Input } from "@udoy/components/ui/input";
import { Button } from "@udoy/components/ui/button";
import { toast } from "sonner";
import { withError } from "@udoy/utils/app-error";
import { updateAddress } from "../actions";
import { CheckCircle } from "lucide-react";
import { cn } from "@udoy/utils/shadcn";
import { isValidPlusCode } from "@udoy/utils";
import Link from "next/link";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import { useLocale } from "next-intl";
import { Address, DeliveryZone, Prisma } from "@prisma/client";
import { useSelector } from "@xstate/store/react";
import { store } from "@udoy/state";

type AddressInfo = Prisma.AddressGetPayload<{
  include: { zone: true };
}>;

interface AddressEditDialogProps {
  isOpen: boolean;
  onClose: () => void;
  address: AddressInfo;
  zones: Prisma.DeliveryZoneGetPayload<{
    include: { subZones: true };
  }>[];
  onAddressUpdated: (updatedAddress: AddressInfo) => void;
}

// Address form schema
const addressSchema = z.object({
  label: z.string().min(2, "Label must be at least 2 characters"),
  name: z.string().min(3, "Name must be at least 3 characters"),
  phone: z.string().min(10, "Phone number must be at least 10 characters"),
  home: z.string().min(3, "Home address must be at least 3 characters"),
  zoneId: z.string().cuid({ message: "Please select a valid zone" }),
  location: z.string().optional().transform((val) => (isValidPlusCode(val) ? val : undefined)),
});

// Form type
type AddressFormValues = z.infer<typeof addressSchema>;

export function AddressEditDialog({
  isOpen,
  onClose,
  address,
  zones,
  onAddressUpdated,
}: AddressEditDialogProps) {
  const locale = useLocale();
  const isBangla = locale === "bn";

  // Initialize form with current address values
  const form = useForm<AddressFormValues>({
    resolver: zodResolver(addressSchema),
    defaultValues: {
      label: address.label,
      name: address.name,
      phone: address.phone,
      home: address.home,
      zoneId: address.zoneId,
      location: address.location || "",
    },
  });

  const location = form.watch("location");

  // Handle address edit submission
  async function handleEditAddress(values: AddressFormValues) {
    try {
      const result = await withError(updateAddress(address.id, values));
      if (result) {
        toast.success("Address updated successfully");
        onAddressUpdated(result);
        onClose();
      }
    } catch (error: any) {
      toast.error(error?.message || "Failed to update address");
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Address</DialogTitle>
          <DialogDescription>
            Update your delivery address information
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleEditAddress)}
            className="space-y-4"
          >
            <FormField
              control={form.control}
              name="label"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address Label</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={
                        isBangla
                          ? "বাড়ি, অফিস, ইত্যাদি..."
                          : "Home, Office, etc."
                      }
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Recipient Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder={isBangla ? "করিম আলি" : "Korim Ali"}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mobile Number</FormLabel>
                    <FormControl>
                      <Input
                        type="tel"
                        placeholder={isBangla ? "০১৭১..." : "0171..."}
                        inputMode="numeric"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="home"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Home Address</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={
                        isBangla
                          ? "বাড়ির নাম, রাস্তার নাম, ইত্যাদি..."
                          : "House no, Road name, etc."
                      }
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="zoneId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Delivery Zone</FormLabel>
                  <FormControl>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={
                            isBangla ? "--নির্বাচন করুন--" : "--Select--"
                          }
                        />
                      </SelectTrigger>
                      <SelectContent>
                        {/* Zones with subzones */}
                        {zones
                          .filter((zone) => zone.subZones.length > 0)
                          .map((zone) => (
                            <SelectGroup key={zone.id}>
                              <SelectLabel className="pl-2">
                                {isBangla ? zone.nam : zone.name}
                              </SelectLabel>
                              {zone.subZones.map((subZone) => (
                                <SelectItem key={subZone.id} value={subZone.id}>
                                  {isBangla ? subZone.nam : subZone.name}
                                </SelectItem>
                              ))}
                            </SelectGroup>
                          ))}

                        {/* Zones without subzones */}
                        {zones
                          .filter((zone) => zone.subZones.length === 0)
                          .map((zone) => (
                            <SelectItem key={zone.id} value={zone.id}>
                              {isBangla ? zone.nam : zone.name}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Google Plus Code
                    <span className="ml-2 text-muted-foreground">
                      (Optional)
                    </span>
                  </FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        placeholder="WRJ3+JH2"
                        {...field}
                        onChange={(e) => {
                          const value = e.target.value;
                          const code = value
                            .split(" ")
                            .find((part) => isValidPlusCode(part));

                          field.onChange(code || value);
                        }}
                      />
                      <CheckCircle
                        size={18}
                        className={cn(
                          "absolute right-2 top-1/2 -translate-y-1/2 text-green-700 opacity-0 duration-150",
                          isValidPlusCode(location) && "opacity-100"
                        )}
                      />
                    </div>
                  </FormControl>
                  <FormDescription>
                    Provide your Google Plus Code for precise location finding
                    <span className="ml-1 underline text-blue-700">
                      <Link target="_blank" href="https://plus.codes/map">
                        Find Your
                      </Link>
                    </span>
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={form.formState.isSubmitting}>
                {form.formState.isSubmitting ? "Saving..." : "Save Changes"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
