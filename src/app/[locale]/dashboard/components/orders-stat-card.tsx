import { ShoppingCart } from "lucide-react";
import { StatCard } from "./stat-card";
import { getTotalOrders, getOrdersChange } from "@udoy/libs/backend/stats";
import { CacheKey, DashboardStatType } from "@udoy/utils/cache-key";
import {
  unstable_cacheTag as cacheTag,
  unstable_cacheLife as cacheLife,
} from "next/cache";

export async function OrdersStatCard() {
  "use cache";
  cacheTag(
    CacheKey.DashboardStats(DashboardStatType.ORDERS_TOTAL),
    CacheKey.DashboardStats()
  );
  cacheLife("hours");

  const totalOrders = await getTotalOrders();
  const ordersChange = await getOrdersChange();

  const formattedOrders = `+${totalOrders.toLocaleString()}`;

  const percentChange = ordersChange.percentChange;
  const changePrefix = percentChange > 0 ? "+" : "";
  const changeDescription = `${changePrefix}${percentChange}% from last month`;

  return (
    <StatCard
      title="Orders"
      value={formattedOrders}
      icon={ShoppingCart}
      description={changeDescription}
    />
  );
}