"use client";

import type React from "react";

import { useMemo, useState } from "react";
import { Button } from "@udoy/components/ui/button";
import { Card } from "@udoy/components/ui/card";
import { Input } from "@udoy/components/ui/input";
import {
  DragDropContext,
  Droppable,
  Draggable,
  type DropResult,
} from "@hello-pangea/dnd";
import { Upload, X, GripVertical, ImageIcon } from "lucide-react";
import { createId } from "@paralleldrive/cuid2";

interface ProductImage {
  id?: string;
  url: string;
  productId?: string;
}

interface ImageUploaderProps {
  images: {
    uploads: { file: File; id: string }[];
    removed: string[];
  };
  existing: ProductImage[];
  onChange: React.Dispatch<
    React.SetStateAction<{
      uploads: { file: File; id: string }[];
      removed: string[];
    }>
  >;
}

export function ImageUploader({
  images,
  existing,
  onChange,
}: ImageUploaderProps) {
  const draggedImages = useMemo(() => {
    const uploads = images.uploads.map((image) => ({
      url: URL.createObjectURL(image.file),
      id: image.id,
    }));

    const existingImages = existing
      .map((image) => ({
        url: image.url,
        id: image.id!,
      }))
      .filter((image) => !images.removed.includes(image.id));

    return [...existingImages, ...uploads];
  }, [images]);

  // Handle file upload
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const newImages: { file: File; id: string }[] = [];

    // Process each file
    Array.from(files).forEach((file) => {
      newImages.push({ file, id: `temp_${createId()}` });
    });

    // Update the images state
    // setDraggedImages(newImages);
    onChange((pre) => ({
      ...pre,
      uploads: [...pre.uploads, ...newImages],
    }));

    // Reset the input value
    e.target.value = "";
  };

  // Handle image removal
  const handleRemoveImage = (imageId: string) => {
    // const updatedImages = [...draggedImages];
    // updatedImages.splice(index, 1);
    // setDraggedImages(updatedImages);
    if (imageId.includes("temp_")) {
      onChange((pre) => ({
        ...pre,
        uploads: pre.uploads.filter((image) => image.id !== imageId),
      }));
    } else {
      onChange((pre) => ({
        ...pre,
        removed: [...pre.removed, imageId],
      }));
    }
  };

  // Handle drag and drop reordering
  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const items = Array.from(draggedImages);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // setDraggedImages(items);
    // onChange(items);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          Drag and drop to reorder images. The first image will be the main
          product image.
        </p>
        <div>
          <Input
            type="file"
            accept="image/*"
            multiple
            className="hidden"
            id="image-upload"
            onChange={handleFileUpload}
          />
          <Button type="button" variant="outline" asChild>
            <label htmlFor="image-upload" className="cursor-pointer">
              <Upload className="mr-2 h-4 w-4" />
              Upload Images
            </label>
          </Button>
        </div>
      </div>

      {draggedImages.length === 0 ? (
        <div className="flex flex-col items-center justify-center rounded-md border border-dashed p-12">
          <ImageIcon className="h-8 w-8 text-muted-foreground mb-2" />
          <p className="text-sm text-muted-foreground">No images uploaded</p>
          <Button type="button" variant="link" asChild className="mt-2">
            <label htmlFor="image-upload" className="cursor-pointer">
              Upload images
            </label>
          </Button>
        </div>
      ) : (
        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="images" direction="horizontal">
            {(provided) => (
              <div
                {...provided.droppableProps}
                ref={provided.innerRef}
                className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4"
              >
                {draggedImages.map((image, index) => {
                  return (
                    <Draggable
                      key={image.id}
                      draggableId={image.id || `img-${index}`}
                      index={index}
                    >
                      {(provided) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          className="relative group"
                        >
                          <Card className="overflow-hidden">
                            <div className="aspect-square relative">
                              <img
                                src={image.url || "/placeholder.svg"}
                                alt={`Product image ${index + 1}`}
                                className="h-full w-full object-cover"
                              />
                              <div
                                {...provided.dragHandleProps}
                                className="absolute top-2 left-2 p-1 rounded-md bg-background/80 opacity-0 group-hover:opacity-100 transition-opacity"
                              >
                                <GripVertical className="h-4 w-4" />
                              </div>
                              <Button
                                type="button"
                                variant="destructive"
                                size="icon"
                                className="absolute top-2 right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={() => handleRemoveImage(image.id)}
                              >
                                <X className="h-3 w-3" />
                              </Button>
                              {index === 0 && (
                                <div className="absolute bottom-0 left-0 right-0 bg-primary/80 text-primary-foreground text-xs py-1 text-center">
                                  Main Image
                                </div>
                              )}
                            </div>
                          </Card>
                        </div>
                      )}
                    </Draggable>
                  );
                })}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      )}
    </div>
  );
}
