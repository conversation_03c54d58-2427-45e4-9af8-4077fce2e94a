import { DollarSign } from "lucide-react";
import { StatCard } from "./stat-card";
import { getTotalRevenue, getRevenueChange } from "@udoy/libs/backend/stats";
import { CacheKey, DashboardStatType } from "@udoy/utils/cache-key";
import {
  unstable_cacheTag as cacheTag,
  unstable_cacheLife as cacheLife,
} from "next/cache";

export async function RevenueStatCard() {
  "use cache";
  cacheTag(
    CacheKey.DashboardStats(DashboardStatType.REVENUE_TOTAL),
    CacheKey.DashboardStats()
  );
  cacheLife("hours");

  const totalRevenue = await getTotalRevenue();
  const revenueChange = await getRevenueChange();

  const formattedRevenue = `৳ ${totalRevenue}`;

  const percentChange = revenueChange.percentChange;
  const changePrefix = percentChange > 0 ? "+" : "";
  const changeDescription = `${changePrefix}${percentChange}% from last month`;

  return (
    <StatCard
      title="Total Revenue"
      value={formattedRevenue}
      icon={DollarSign}
      description={changeDescription}
    />
  );
}
