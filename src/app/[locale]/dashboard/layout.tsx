import type React from "react";
import { TopBar } from "./components/top-bar";
import { Sidebar } from "./components/sidebar";
import { CookieUtil } from "@udoy/utils/cookie-util";
import { notFound } from "next/navigation";
import { getPrisma } from "@udoy/utils/db-utils";
import { Role } from "@prisma/client";

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const userId = await CookieUtil.userId();

  if (!userId) {
    return notFound();
  }

  const prisma = getPrisma();
  const user = await prisma.user.findUnique({
    where: {
      id: userId,
      role: { in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER] },
    },
  });

  if (!user) {
    return notFound();
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="flex min-h-screen">
        <Sidebar className="hidden md:flex" />
        <div className="flex flex-1 flex-col">
          <TopBar />
          <main>{children}</main>
        </div>
      </div>
    </div>
  );
}
