"use client";

import { Product } from "@prisma/client";
import Locale from "@udoy/components/Locale/Client";
import { Button } from "@udoy/components/ui/button";
import { Separator } from "@udoy/components/ui/separator";
import useCartUtils from "@udoy/hooks/useCartUtils";
import { UnitUtil } from "@udoy/utils/product-unit";
import { Minus, Plus } from "lucide-react";
import { useLocale } from "next-intl";
import Image from "next/image";
import React, { Fragment } from "react";

function CartViewItem({ product, count }: { product: Product; count: number }) {
  const { handleAddToCart, handleRemoveFromCart } = useCartUtils(product);
  const price = product.price - product.discount;
  const locale = useLocale();

  return (
    <div key={product.id} className="flex gap-2 p-2 mt-1">
      <div className="">
        <Image
          src={(product as any).images?.[0]?.url}
          width={40}
          height={40}
          alt={product.name}
        />
      </div>
      <div className="flex-1">
        <h3 className="line-clamp-1 text-xs">
          <Locale bn={product.nam}>{product.name}</Locale>
        </h3>
        <div className="text-xs gap-3 flex items-center">
          <span className="text-sm text-brand font-bold">
            ৳{(count * price).toLocaleString(locale)}
          </span>
          <span className="text-xs ">
            ৳{price.toLocaleString(locale)} /{" "}
            {UnitUtil.getAmountUnit(
              product.amount,
              (product as any).unit,
              locale
            )}
          </span>

          <div className="flex gap-2 items-center flex-1 justify-end">
            <Button
              className="w-6 h-6 rounded-full "
              size="icon"
              variant="outline"
              onClick={handleRemoveFromCart}
            >
              <Minus size={14} />
            </Button>
            <span className="font-bold  text-sm">
              {count.toLocaleString(locale)}
            </span>
            <Button
              size="icon"
              className="w-6 h-6 rounded-full "
              variant="outline"
              onClick={handleAddToCart}
            >
              <Plus size={14} />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default CartViewItem;
