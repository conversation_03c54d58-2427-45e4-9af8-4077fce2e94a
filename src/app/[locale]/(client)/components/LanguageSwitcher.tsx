"use client";

import { useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import { usePathname } from "@udoy/i18n/navigation";
import { Switch } from "@udoy/components/ui/switch";
import { LanguagesIcon } from "lucide-react";

function LanguageSwitcher() {
  const lang = useLocale();
  const router = useRouter();
  const panthname = usePathname();

  function handleClick() {
    const locale = lang === "en" ? "bn" : "en";
    router.push(`/${locale}${panthname}`);
  }

  return (
    <div
      className="flex items-center justify-between w-full"
      suppressHydrationWarning
    >
       <LanguagesIcon />
      <span className="flex-1 mt-[3px] ml-2">English</span>
      <Switch onClick={handleClick} checked={lang === "en"} />
    </div>
  );
}

export default LanguageSwitcher;
