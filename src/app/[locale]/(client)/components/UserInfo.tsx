"use client";

import Link from "next/link";
// import Logout from "./Logout";
import { useQuery } from "@tanstack/react-query";
import { getUser } from "@udoy/actions/auth";
import Hide from "@udoy/components/Hide";
import Button from "@udoy/components/Button";
import Icon from "@udoy/components/Icon";
import useUser from "@udoy/hooks/useUser";

function UserInfo() {
  const { user, itemsCount } = useUser();
  return (
    <Hide
      open={!!user}
      fallback={
        <Link href={{ pathname: "/login" }}>
          <Button label="Login" />
        </Link>
      }
    >
      <div className="flex gap-3 items-center">
        <div className="flex gap-2 items-center ml-3">
          <img
            src={
              "https://bafybeiednnt3jfp5rw5fggr3xrjshnx2vl3s2oyfk42juo3ibzntoz53gi.ipfs.nftstorage.link/2580.png?ext=png"
            }
            width={40}
            height={40}
            className="object-cover aspect-square rounded-full "
          />
          <h3 className="text-lg font-semibold">{user?.name}</h3>
        </div>
        {/* <Logout /> */}
      </div>
    </Hide>
  );
}

export default UserInfo;
