"use client";

import { store } from "@udoy/state";
import { useSelector } from "@xstate/store/react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@udoy/components/ui/dialog";
import Locale from "@udoy/components/Locale/Client";
import GoogleLogin from "./GoogleLogin";

function LoginPopup() {
  const open = useSelector(
    store,
    (state) => state.context.loginPopup && !Boolean(state.context.me)
  );

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => store.send({ type: "setLoginPopup", open })}
    >
      <DialogContent className="max-w-xs" hideIcon>
        <DialogHeader>
          <DialogTitle className="text-center">
            <Locale bn="আপনার গুগল একাউন্ট দিয়ে লগইন করুন">
              Login with your google account
            </Locale>
          </DialogTitle>
        </DialogHeader>

        <div className="flex justify-center mt-3">
          <GoogleLogin />
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default LoginPopup;
