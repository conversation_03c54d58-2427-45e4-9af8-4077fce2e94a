"use client";

import useRecoilSync from "@udoy/hooks/useRecoilSync";
import { ReactElement, useEffect } from "react";
import { PathState } from "../(sidebar)/[slug]/state";
import { useSetRecoilState } from "recoil";

function PathSync({
  path,
  children,
}: {
  path: string[];
  children?: ReactElement;
}) {
  const set = useSetRecoilState(PathState);
  useEffect(() => {
    set(path);
  }, path);
  return children || null;
}

export default PathSync;
