"use client";

import React from "react";
import { Button } from "@udoy/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@udoy/components/ui/dropdown-menu";
import {
  BookIcon,
  FileClock,
  LayoutDashboardIcon,
  LogOutIcon,
  MoreVerticalIcon,
  StoreIcon,
  TruckIcon,
  UserCircle,
} from "lucide-react";
import { Switch } from "@udoy/components/ui/switch";
import ThemeSwitcher from "./ThemeSwitcher";
import LanguageSwitcher from "./LanguageSwitcher";
import { store } from "@udoy/state";
import { useSelector } from "@xstate/store/react";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@udoy/components/ui/avatar";
import useLogOut from "@udoy/hooks/useLogOut";
import Locale from "@udoy/components/Locale/Client";
import Link from "next/link";
import Hide from "@udoy/components/Hide";
import useIsAdmin from "@udoy/hooks/useIsAdmin";

function UserInfo() {
  const user = useSelector(store, ({ context }) => context.me!);
  const logout = useLogOut();
  const isAdmin = useIsAdmin();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon">
          <Avatar className="size-8 rounded-full">
            <AvatarImage src={user?.avatar!} alt={user.name} />
            <AvatarFallback className="rounded-lg">CN</AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
        side="bottom"
        align="end"
        sideOffset={4}
      >
        <DropdownMenuLabel className="p-0 font-normal">
          <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
            <Avatar className="h-8 w-8 rounded-full">
              <AvatarImage src={user?.avatar!} alt={user.name} />
              <AvatarFallback className="rounded-full">CN</AvatarFallback>
            </Avatar>
            <div className="grid flex-1 text-left text-sm leading-tight">
              <span className="truncate font-medium">{user.name}</span>
              <span className="text-muted-foreground truncate text-xs">
                {user.email}
              </span>
            </div>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem>
            <ThemeSwitcher />
          </DropdownMenuItem>
          <DropdownMenuItem>
            <LanguageSwitcher />
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <Link href="/orders">
            <DropdownMenuItem>
              <FileClock className="-mt-0.5" />
              <Locale bn="অর্ডার ইতিহাস">Order History</Locale>
            </DropdownMenuItem>
          </Link>
          {/* <DropdownMenuItem>
            <User2Icon className="-mt-0.5" />
            <Locale bn="ব্যক্তিগত তথ্য">Personal Info</Locale>
          </DropdownMenuItem> */}
          <Link href="/addresses">
            <DropdownMenuItem>
              <BookIcon className="-mt-0.5" />
              <Locale bn="ঠিকানা বই">Address Book</Locale>
            </DropdownMenuItem>
          </Link>
        </DropdownMenuGroup>
        <Hide open={Boolean(isAdmin)}>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <Link href="/deliver">
              <DropdownMenuItem>
                <TruckIcon className="-mt-0.5" />
                <Locale bn="ডেলিভারি">Deliver</Locale>
              </DropdownMenuItem>
            </Link>
            <Link href="/dashboard">
              <DropdownMenuItem>
                <LayoutDashboardIcon className="-mt-0.5" />
                <Locale bn="ড্যাশবোর্ড">Dashboard</Locale>
              </DropdownMenuItem>
            </Link>
          </DropdownMenuGroup>
        </Hide>
        <Hide open={Boolean(user.shop)}>
          <Link href="/shop">
            <DropdownMenuItem>
              <StoreIcon className="-mt-0.5" />
              <Locale bn="আমার দোকান">My Shop</Locale>
            </DropdownMenuItem>
          </Link>
        </Hide>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={logout}>
          <LogOutIcon />
          Log out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

function NoAuth() {
  return (
    <div className="flex gap-2 items-center">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button size="icon" variant="outline">
            <MoreVerticalIcon />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Settings</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem>
            <ThemeSwitcher />
          </DropdownMenuItem>
          <DropdownMenuItem>
            <LanguageSwitcher />
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <Button
        className="gap-1 items-center"
        onClick={() => store.send({ type: "setLoginPopup", open: true })}
      >
        <UserCircle size={20} />
        <h4 className=" font-semibold mt-0.5">Sign In</h4>
      </Button>
    </div>
  );
}

function HeaderActions() {
  const me = useSelector(store, (state) => state.context.me);

  if (!me) {
    return <NoAuth />;
  }

  return <UserInfo />;
}

export default HeaderActions;
