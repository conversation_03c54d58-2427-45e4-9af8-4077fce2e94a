"use client";

import { Switch } from "@udoy/components/ui/switch";
import { Sun } from "lucide-react";
import { useTheme } from "next-themes";

function ThemeSwitcher() {
  const { setTheme, resolvedTheme } = useTheme();

  function handleClick() {
    setTheme(resolvedTheme === "dark" ? "light" : "dark");
  }

  return (
    <div className="flex justify-between items-center w-full">
      <Sun />
      <span className="flex-1 mt-[3px] ml-2 mr-6">Dark Mode</span>
      <Switch onClick={handleClick} checked={resolvedTheme === "dark"} />
    </div>
  );
}

export default ThemeSwitcher;
