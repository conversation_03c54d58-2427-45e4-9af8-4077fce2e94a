"use client";

import { manageCart } from "@udoy/actions/cart";
import Icon from "@udoy/components/Icon";
import useStatus from "@udoy/hooks/useToastUtil";
import { withError } from "@udoy/utils/app-error";
import { useRouter } from "next/navigation";
import { useTransition } from "react";

function ManageCart({ productId, add }: { productId: string; add?: boolean }) {
  const status = useStatus();
  const [isPending, startTransition] = useTransition();
  const router = useRouter();
  async function handleClick() {
    try {
      status.loading("Adding to cart...");
      await withError(manageCart(productId, add ? "add" : "remove"));
      status.success("Added to cart");
      router.refresh();
    } catch (err: any) {
      status.error(err?.message || "Something went wrong");
    }
  }
  return (
    <button
      className="w-full border-white/10 rounded-full p-3 border grid place-items-center"
      onClick={() => startTransition(handleClick)}
    >
      <Icon icon={add ? "chevron-right" : "chevron-left"} />
    </button>
  );
}

export default ManageCart;
