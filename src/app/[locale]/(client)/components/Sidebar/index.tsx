import { Category, Prisma } from "@prisma/client";
import { getPrisma } from "@udoy/utils/db-utils";
import {
  unstable_cache,
  unstable_cacheLife,
  unstable_cacheTag,
} from "next/cache";
import SidebarContainer from "./Sidebar";
import { CategoryWithSubcategories } from "@udoy/utils/types";
import { CacheKey } from "@udoy/utils/cache-key";

function flattenPathToParent(
  category: (Category & { parentCategory?: Category | null }) | null | undefined
): string[] {
  if (!category) {
    return [];
  }

  if (!category?.parentCategory) {
    return [category.slug];
  }
  return [category.slug, ...flattenPathToParent(category.parentCategory)];
}

const props = {
  id: true,
  name: true,
  nam: true,
  slug: true,
  isBase: true,
  hide: false,
};

const orderBy = [
  {
    featured: "desc",
  },
  {
    position: "asc",
  },
] satisfies Prisma.CategoryOrderByWithRelationInput[];

async function getData() {
  "use cache";
  unstable_cacheLife("max");
  unstable_cacheTag(CacheKey.SidebarCategory());

  const categories = await getPrisma().category.findMany({
    where: { parentId: null, hide: false },

    select: {
      ...props,
      subCategories: {
        where: {
          hide: false,
        },
        select: {
          ...props,
          subCategories: {
            where: {
              hide: false,
            },
            select: {
              ...props,
              subCategories: {
                where: {
                  hide: false,
                },
                select: {
                  ...props,
                },
                orderBy,
              },
            },
            orderBy,
          },
        },
        orderBy,
      },
    },

    orderBy,
  });

  return { categories };
}

async function Sidebar() {
  const { categories } = await getData();
  return <SidebarContainer categories={categories as any} />;
}

export default Sidebar;
