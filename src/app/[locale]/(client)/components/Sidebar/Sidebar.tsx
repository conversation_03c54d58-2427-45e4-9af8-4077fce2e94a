"use client";

import { Category } from "@prisma/client";
import Accordion from "../CategoryAccordion";
import { useSelector } from "@xstate/store/react";
import { store } from "@udoy/state";
import { cn } from "@udoy/utils/shadcn";
import ClickAwayListener from "@udoy/components/ClickAwayListener";

function SidebarContainer({ categories }: { categories: Category[] }) {
  const open = useSelector(store, (state) => state.context.sideBarOpen);

  return (
    <ClickAwayListener
      onClickAway={() => open && store.send({ type: "toggleSidebar" })}
    >
      <div
        className={cn(
          "w-64 px-2 py-4 flex-col gap-2 border-r overflow-y-auto flex bg-background transition-all duration-150 -left-[256px] fixed 2xl:static z-50 scrollbar-thin",
          open && "flex fixed left-0"
        )}
        style={{ height: "calc(100vh - 64px)", overflowY: "auto" }}
        onClick={() => store.send({ type: "toggleSidebar" })}
      >
        {categories.map((category) => (
          <Accordion
            key={category.id}
            category={category as any}
            path={[category.slug]}
          />
        ))}
      </div>
    </ClickAwayListener>
  );
}

export default SidebarContainer;
