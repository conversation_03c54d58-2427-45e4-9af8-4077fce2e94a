"use client";

import { Category } from "@prisma/client";
import Hide from "@udoy/components/Hide";
import Icon from "@udoy/components/Icon";
import { classUtil } from "@udoy/utils/class-util";
import Link from "next/link";
import { useEffect, useState } from "react";
import { useParams, usePathname } from "next/navigation";
import { useIsBangla } from "@udoy/hooks/useIsBangla";
import Locale from "@udoy/components/Locale/Client";
import { store } from "@udoy/state";
import { useSelector } from "@xstate/store/react";

interface SubCategorie extends Category {
  subCategories?: SubCategorie[];
}

function Accordion({
  category,
  path,
}: {
  category: SubCategorie;
  path: string[];
}) {
  const isActive = useSelector(store, (state) =>
    state.context.activePath.includes(category.slug)
  );
  const { slug } = useParams();
  const currentActive = slug === category.slug;

  return (
    <div className="">
      <Link href={{ pathname: category.slug }}>
        <div
          className={classUtil(
            "flex justify-between items-center  px-2 py-2 rounded border hover:bg-muted",
            currentActive && "bg-muted"
          )}
          onClick={() => store.send({ type: "updateActivePath", path })}
        >
          <h3 className="text-sm">
            <Locale bn={category.nam}>{category.name}</Locale>
          </h3>
          <Hide open={!category.isBase}>
            <Hide
              open={isActive}
              fallback={<Icon icon="chevron-right" className="text-sm" />}
            >
              <Icon icon="chevron-down" className="text-sm" />
            </Hide>
          </Hide>
        </div>
      </Link>

      <Hide open={Boolean(isActive && category.subCategories?.length)}>
        <div className="ml-1 border-l pl-2 mt-2 grid gap-1">
          {category.subCategories?.map((subCategory) => (
            <Accordion
              path={[...path, subCategory.slug]}
              key={subCategory.id}
              category={subCategory}
            />
          ))}
        </div>
      </Hide>
    </div>
  );
}

export default Accordion;
