"use client";

import ClickAwayListener from "@udoy/components/ClickAwayListener";
import Locale from "@udoy/components/Locale/Client";
import { Button } from "@udoy/components/ui/button";
import { Separator } from "@udoy/components/ui/separator";
import { store } from "@udoy/state";
import { useCart } from "@udoy/state/selectors";
import { localeNumber } from "@udoy/utils";
import { cn } from "@udoy/utils/shadcn";
import { useSelector } from "@xstate/store/react";
import { Minus, Plus, ShoppingBagIcon, X } from "lucide-react";
import { useLocale } from "next-intl";
import Image from "next/image";
import React, { Fragment } from "react";
import { Link } from "@udoy/i18n/navigation";
import CartViewItem from "./CartViewItem";
import useIsAdmin from "@udoy/hooks/useIsAdmin";
import Hide from "@udoy/components/Hide";

function CartView() {
  const cartOpen = useSelector(store, (state) => state.context.cartViewOpen);
  const { items } = useCart();
  const locale = useLocale();
  const isBangla = locale === "bn";
  const { itemsCount, discountedPrice } = useCart();
  const isAdmin = useIsAdmin();

  return (
    <ClickAwayListener
      onClickAway={() => store.send({ type: "toggleCart", open: false })}
    >
      <div
        className={cn(
          "fixed right-0 top-0 w-full bottom-0 bg-background flex flex-col md:top-[64px] sm:w-80 shadow-2xl dark:border-l z-50 transform transition-transform duration-300 ease-in-out translate-x-full",
          cartOpen && "translate-x-0"
        )}
      >
        <div className="bg-brand flex justify-between items-center">
          <div className="flex gap-2 items-center px-2 py-4 font-bold">
            <ShoppingBagIcon />
            <span className="-mb-1">
              {isBangla ? `${itemsCount} টি পন্য` : `${itemsCount} Items`}
            </span>
          </div>

          <Button
            className="hover:bg-transparent active:opacity-50"
            size="icon"
            variant="ghost"
            onClick={() => store.send({ type: "toggleCart" })}
          >
            <X />
          </Button>
        </div>
        <div className="overflow-y-auto scrollbar-thin">
          {items.map(({ count, product }) => (
            <Fragment key={product.id}>
              <CartViewItem key={product.id} product={product} count={count} />
              <Separator />
            </Fragment>
          ))}
        </div>
        <div className="flex-1"></div>
        <Hide open={isAdmin}>
          <Button
            variant="secondary"
            asChild
            className=" rounded-none h-12 sm:h-10 border-x-0 "
          >
            <Link href="/custom-checkout">
              <Locale bn="কাস্টম অর্ডার তৈরি করুন">Create Custom Order</Locale>
            </Link>
          </Button>
        </Hide>
        <div className="grid grid-cols-2">
          <h4 className=" bg-brand px-4 flex justify-center items-center">
            <Locale bn="মোট">Total</Locale>: ৳
            {localeNumber(discountedPrice, locale)}
          </h4>

          <Button asChild className=" rounded-none h-12 sm:h-10 ">
            <Link
              href="/checkout"
              onClick={() => store.send({ type: "toggleCart" })}
            >
              <Locale bn="অর্ডার করুন">Place Order</Locale>
            </Link>
          </Button>
        </div>
      </div>
    </ClickAwayListener>
  );
}

export default CartView;
