import Link from "next/link";
import Icon from "@udoy/components/Icon";
import SidebarToggle from "./SidebarToggle";
import HeaderActions from "./HeaderActions";
import SearchProducts from "./SearchProducts";


async function Header() {
  return (
    <header className="h-16 flex justify-between items-center px-2 md:px-8 2xl:px-4 border-b">
      <div className="flex items-center gap-4">
        <SidebarToggle />
        <Link href={{ pathname: "/" }} className="hidden sm:block">
          <p className="text-xl font-bold flex items-center">
            <Icon icon="logo" className="text-2xl" />
            doyMart
          </p>
        </Link>
      </div>

      <SearchProducts className="max-w-xs flex-1 mx-2" />
      <HeaderActions />
    </header>
  );
}

export default Header;
