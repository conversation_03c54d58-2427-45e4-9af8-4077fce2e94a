import { Category } from "@prisma/client";
import Locale from "@udoy/components/Locale";
import { l } from "@udoy/libs/backend/locale";
import Image from "next/image";
import Link from "next/link";

async function CategoryItem({ category }: { category: Category }) {
  return (
    <div key={category.id} className="border border-white/10 rounded">
      <Link href={{ pathname: `/${category.slug}` }}>
        <div className="">
          <Image
            src={category?.image || ""}
            width={200}
            height={100}
            alt={await l(category.name, category.nam)}
            className="object-cover w-full rounded-b-none rounded"
          />
        </div>
        <h3 className="mb-4 text-center px-4 font-semibold mt-4">
          <Locale bn={category.nam}>{category.name}</Locale>
        </h3>
      </Link>
    </div>
  );
}

export default CategoryItem;
