"use client";

import { searchProducts, searchProductsAdmin } from "@udoy/actions/product";
import { Input } from "@udoy/components/ui/input";
import { withError } from "@udoy/utils/app-error";
import { useSetAtom } from "jotai";
import {
  useParams,
  useRouter,
  useSelectedLayoutSegment,
} from "next/navigation";
import React from "react";
import { useDebounce } from "react-use";
import { toast } from "sonner";
import { searchProductsAtom } from "../(sidebar)/search/state";
import { SearchIcon } from "lucide-react";
import { cn } from "@udoy/utils/shadcn";
import { useIsBangla } from "@udoy/hooks/useIsBangla";
import { usePathname } from "@udoy/i18n/navigation";
import useIsAdmin from "@udoy/hooks/useIsAdmin";

function SearchProducts({
  className,
  searchPage = "/search",
}: {
  className?: string;
  searchPage?: string;
}) {
  const [query, setQuery] = React.useState("");
  const setSearchProducts = useSetAtom(searchProductsAtom);
  const isBangla = useIsBangla();
  const path = usePathname();
  const router = useRouter();
  const isAdmin = useIsAdmin();

  useDebounce(
    async () => {
      if (!query) return;
      if (path !== searchPage) {
        router.push(searchPage);
      }
      try {
        const products = await withError(
          isAdmin ? searchProductsAdmin(query) : searchProducts(query)
        );
        setSearchProducts(products);
      } catch (error: any) {
        toast.error(error?.message);
      }
    },
    400,
    [query, isAdmin]
  );

  return (
    <div className={cn(className)}>
      <div className="relative flex-1">
        <Input
          type="text"
          placeholder={isBangla ? "পন্য খুঁজুন..." : "Search products..."}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className="pr-10"
        />
        <SearchIcon className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground pointer-events-none" />
      </div>
    </div>
  );
}

export default SearchProducts;
