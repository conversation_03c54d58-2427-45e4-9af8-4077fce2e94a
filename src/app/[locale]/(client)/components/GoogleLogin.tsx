"use client";

import Button from "@udoy/components/Button";
import GoogleGLogo from "@udoy/assets/images/google.png";
import Image from "next/image";
import useStatus from "@udoy/hooks/useToastUtil";
import { withError } from "@udoy/utils/app-error";
import { googleLogin } from "@udoy/actions/auth";
import { useGoogleLogin } from "@react-oauth/google";
import { store } from "@udoy/state";

function GoogleLogin() {
  const status = useStatus();

  async function handleAuthCode(code: string) {
    try {
      status.loading("Logging With Google...");
      const me = await withError(googleLogin(code));
      me && store.send({ type: "setMe", user: me });
      status.success("Successfully Logged In With Google");
    } catch (error: any) {
      status.error(error?.message || "Failed To Login With Google");
    }

    store.send({ type: "setLoginPopup", open: false });
  }

  const login = useGoogleLogin({
    onSuccess: (resp) => handleAuthCode(resp.code),
    flow: "auth-code",
  });

  return (
    <Button
      onClick={login}
      label="Login with Google"
      className="border gap-1 items-center"
      variant="ghost"
    >
      <Image src={GoogleGLogo} alt="Google Logo" width={20} height={20} />
    </Button>
  );
}

export default GoogleLogin;
