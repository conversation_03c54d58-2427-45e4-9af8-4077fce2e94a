import Button from "@udoy/components/Button";
import Hide from "@udoy/components/Hide";
import Link from "next/link";
// import Logout from "./Logout";
import { getPrisma } from "@udoy/utils/db-utils";
import { <PERSON>ieUtil } from "@udoy/utils/cookie-util";
import Icon from "@udoy/components/Icon";

async function getData() {
  const prisma = getPrisma();
  const userId = await CookieUtil.userId();

  if (!userId) {
    return { user: null, cartCount: 0 };
  }

  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      cart: {
        include: {
          items: true,
        },
      },
    },
  });

  let count = 0;

  user?.cart?.items.forEach((item) => {
    count += item.quantity;
  });

  return { user, cartCount: count };
}

async function UserInfoV2() {
  const { user, cartCount } = await getData();
  return (
    <Hide
      open={!!user}
      fallback={
        <Link href={{ pathname: "/login" }}>
          <Button label="Login" className="w-full" />
        </Link>
      }
    >
      <div className="flex gap-3 items-center">
        <div className="flex gap-2 items-center ml-3">
          <img
            src={
              "https://bafybeiednnt3jfp5rw5fggr3xrjshnx2vl3s2oyfk42juo3ibzntoz53gi.ipfs.nftstorage.link/2580.png?ext=png"
            }
            width={40}
            height={40}
            className="object-cover aspect-square rounded-full "
          />
          <h3 className="text-lg font-semibold">{user?.name}</h3>
        </div>
        {/* <Logout /> */}
      </div>
    </Hide>
  );
}

export default UserInfoV2;
