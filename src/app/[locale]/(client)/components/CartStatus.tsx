"use client";

import { store } from "@udoy/state";
import { Context } from "@udoy/state/context";
import { useCart } from "@udoy/state/selectors";
import { cn } from "@udoy/utils/shadcn";
import { useLocale } from "next-intl";
import React, { Fragment, useCallback, useEffect, useState } from "react";
import CountUp from "react-countup";
import CartView from "./CartView";

function CartStatus() {
  const { itemsCount, discountedPrice } = useCart();
  const [shake, setShake] = useState(false);
  const locale = useLocale();
  const isBangla = locale === "bn";
  const itemsLocale = itemsCount.toLocaleString(locale);

  const format = useCallback(
    (num: number) => num.toLocaleString(locale),
    [locale]
  );

  useEffect(() => {
    setShake(true);
    setTimeout(() => {
      setShake(false);
    }, 1000);
  }, [itemsCount, discountedPrice]);

  return (
    <Fragment>
      <button className="" onClick={() => store.send({ type: "toggleCart" })}>
        <div
          className={cn(
            "fixed bottom-1/2 z-50 block bg-brand p-px rounded shadow-brand right-2",
            shake && "animate-shake"
          )}
        >
          <div className="bg-brand flex flex-col text-center min-w-[80px] shadow-xl text-xs shadow-brand/50 rounded overflow-clip">
            <span className="py-2 bg-background">
              <span>
                {isBangla ? `${itemsLocale} টি পন্য` : `${itemsLocale} Items`}
              </span>
            </span>
            <span className="bg-foreground py-2 text-background">
              ৳{" "}
              <CountUp
                end={discountedPrice}
                preserveValue
                formattingFn={format}
              />
            </span>
          </div>
        </div>
      </button>
      <CartView />
    </Fragment>
  );
}

export default CartStatus;
