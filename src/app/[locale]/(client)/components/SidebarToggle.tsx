"use client";

import { Link } from "@udoy/i18n/navigation";
import { store } from "@udoy/state";
import { Home, MenuIcon } from "lucide-react";
import { useSelectedLayoutSegment } from "next/navigation";
import React from "react";

function SidebarToggle() {
  const segment = useSelectedLayoutSegment();

  if (segment && ["checkout", "addresses"].includes(segment)) {
    return (
      <Link href={{ pathname: "/" }} className="active:opacity-50 2xl:hidden">
        <Home size={32} className="text-muted-foreground" />
      </Link>
    );
  }

  return (
    <button
      onClick={() => store.send({ type: "toggleSidebar" })}
      className="active:opacity-50 2xl:hidden"
    >
      <MenuIcon size={32} className="" />
    </button>
  );
}

export default SidebarToggle;
