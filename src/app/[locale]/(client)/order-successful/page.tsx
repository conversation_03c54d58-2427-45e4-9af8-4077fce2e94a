import Icon from "@udoy/components/Icon";
import Link from "next/link";
import Locale from "@udoy/components/Locale/Client";
import { Button } from "@udoy/components/ui/button";
import ConfettiWraper from "./components/ConfettiWraper";
import { Suspense } from "react";

function Page() {
  return (
    <main className="flex-1 flex justify-center items-center">
      <Suspense>
        <ConfettiWraper />
      </Suspense>
      <div className="flex-1 max-w-sm text-center border border-white/10 rounded p-4 flex flex-col items-center relative backdrop-blur z-50 shadow">
        <Icon
          icon="check2-circle"
          className="text-6xl text-green-600 animate-pulse"
        />
        <h3 className="text-2xl font-bold mt-2">
          <Locale bn="আপনার অর্ডারটি সম্পন্ন হয়েছে">
            Your Order is Confirmed
          </Locale>
        </h3>
        <p className="text-sm opacity-70 py-3 pt-1">
          <Locale bn="উদয়মার্ট থেকে কেনা-কাটার জন্য আপনাকে ধন্যবাদ । আমরা দ্রুততম সময়ে আাপনার পন্যটি পৌছে দেয়ার জন্য দৃঢ় প্রতিজ্ঞ ।">
            Thank you for shopping with Udaymart! We are fully committed to
            delivering your order as quickly as possible.
          </Locale>
        </p>
        <Link href="/">
          <Button>
            <Locale bn="হোম এ ফিরে যান">Go To Home</Locale>
          </Button>
        </Link>
      </div>
    </main>
  );
}

export default Page;
