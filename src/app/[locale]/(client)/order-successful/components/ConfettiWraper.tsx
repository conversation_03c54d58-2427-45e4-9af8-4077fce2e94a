"use client";

import { store } from "@udoy/state";
import dynamic from "next/dynamic";
import React, { useEffect } from "react";
const Confetti = dynamic(() => import("./Confetti"), { ssr: false });

function ConfettiWraper() {
  useEffect(() => {
    store.send({ type: "clearCartItems" });
    store.send({ type: "toggleCart", open: false });
  }, []);

  return <Confetti />;
}

export default ConfettiWraper;
