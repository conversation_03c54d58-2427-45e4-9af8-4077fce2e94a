"use client";

import React from "react";
import ReactConfetti from "react-confetti";
import { useWindowSize } from "react-use";

const Confetti = () => {
  const { width, height } = useWindowSize();
  return (
    <ReactConfetti
      suppressHydrationWarning
      width={width}
      height={height}
      numberOfPieces={width > 600 ? 200 : 60}
      initialVelocityY={18}
    />
  );
};
export default Confetti;
