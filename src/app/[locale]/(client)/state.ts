import { ProductDetails } from "@udoy/utils/types";
import { atom, selector } from "recoil";
import Cookies from "js-cookie";

export const CartState = atom({
  key: "CartState",
  default: [] as ProductDetails[],
});

export const CartInfoState = selector({
  key: "CartInfoState",
  get({ get }) {
    const cart = get(CartState);

    const info = {} as Record<
      string,
      { id: string; count: number; price: number; name: string; image: string }
    >;
    let total = 0;
    let count = 0;

    for (const item of cart) {
      const record = info[item.id];

      total += item.price;
      count++;

      if (!record) {
        info[item.id] = {
          id: item.id,
          count: 1,
          price: item.price,
          name: item.name,
          image: item.images[0].url,
        };
      } else {
        record.count++;
        record.price += item.price;
      }
    }
    return {
      total,
      count,
      records: Object.values(info),
    };
  },
});

export const LocaleState = atom<"en" | "bn">({
  key: "LocaleState",
  default: (() => {
    return Cookies.get("lang") === "bn" ? "bn" : ("en" as const);
  })(),
  effects: [
    ({ onSet }) => {
      onSet((newValue) => {
        Cookies.set("lang", newValue);
      });
    },
  ],
});
