"use client";

import { store } from "@udoy/state";
import React from "react";
import { OrdersTable } from "./OrdersTable";
import { OrderWithItems } from "@udoy/utils/types";
import { useSize, useWindowSize } from "react-use";
import { OrdersViewMobile } from "./OrdersViewMobile";
import { Address } from "@prisma/client";

function RenderOrders({ orders }: { orders: (OrderWithItems & { address: Address })[] }) {
  store.send({ type: "setOrders", orders });

  const { width } = useWindowSize();

  if (width < 600) {
    return <OrdersViewMobile />;
  }

  return <OrdersTable />;
}

export default RenderOrders;
