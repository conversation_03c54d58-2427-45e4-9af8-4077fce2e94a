"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { format } from "date-fns";
import { ChevronDownIcon, CalendarIcon, ArrowUpDownIcon } from "lucide-react";

import { Button } from "@udoy/components/ui/button";
import { Badge } from "@udoy/components/ui/badge";
import { Card, CardContent, CardFooter } from "@udoy/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@udoy/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@udoy/components/ui/dropdown-menu";
import { useSelector } from "@xstate/store/react";
import { store } from "@udoy/state";


// Status badge styling
const getStatusBadge = (status: string) => {
  switch (status) {
    case "processing":
      return (
        <Badge
          variant="outline"
          className="bg-yellow-50 text-yellow-700 border-yellow-200"
        >
          Processing
        </Badge>
      );
    case "shipped":
      return (
        <Badge
          variant="outline"
          className="bg-blue-50 text-blue-700 border-blue-200"
        >
          Shipped
        </Badge>
      );
    case "delivered":
      return (
        <Badge
          variant="outline"
          className="bg-green-50 text-green-700 border-green-200"
        >
          Delivered
        </Badge>
      );
    case "cancelled":
      return (
        <Badge
          variant="outline"
          className="bg-red-50 text-red-700 border-red-200"
        >
          Cancelled
        </Badge>
      );
    default:
      return <Badge variant="outline">{status}</Badge>;
  }
};

interface OrdersTableProps {
  status?: string;
}

export function OrdersTable() {
  const orders = useSelector(store, (state) => state.context.orders);
  const [expandedOrder, setExpandedOrder] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<string>("date");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");



  const toggleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortOrder("asc");
    }
  };

  const toggleOrderExpand = (orderId: string) => {
    setExpandedOrder(expandedOrder === orderId ? null : orderId);
  };

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">
                <Button
                  variant="ghost"
                  className="flex items-center gap-1 p-0 h-auto font-medium"
                  onClick={() => toggleSort("id")}
                >
                  Order #
                  <ArrowUpDownIcon className="h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  className="flex items-center gap-1 p-0 h-auto font-medium"
                  onClick={() => toggleSort("date")}
                >
                  Date
                  <ArrowUpDownIcon className="h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>Items</TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  className="flex items-center gap-1 p-0 h-auto font-medium"
                  onClick={() => toggleSort("total")}
                >
                  Total
                  <ArrowUpDownIcon className="h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {orders.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={6}
                  className="text-center py-8 text-muted-foreground"
                >
                  No orders found
                </TableCell>
              </TableRow>
            ) : (
              orders.map(({ orderItems: items, ...order }) => (
                <>
                  <TableRow key={order.id} className="group">
                    <TableCell className="font-medium">#{order.id}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1 min-w-max">
                        <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                        <span className="mt-0.5">{format(order.createdAt, "MMM dd, yyyy")}</span>
                      </div>
                      <div className="text-xs text-muted-foreground min-w-max">
                        {format(order.createdAt, "h:mm a")}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="relative h-10 w-10 overflow-hidden rounded-md">
                          <Image
                            src={
                              items[0]?.product?.images?.at(0)?.url ||
                              "/placeholder.svg"
                            }
                            alt={items[0].product.name}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <div>
                          <p className="line-clamp-1 font-medium">
                            {items[0].product.name}
                          </p>
                          {items.length > 1 && (
                            <p className="text-xs text-muted-foreground">
                              +{items.length - 1} more items
                            </p>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">
                      ৳{order.subTotal + order.shipping}
                    </TableCell>
                    <TableCell>{getStatusBadge(order.status)}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => toggleOrderExpand(order.id.toString())}
                        >
                          <ChevronDownIcon
                            className={`h-4 w-4 transition-transform ${
                              expandedOrder === order.id.toString()
                                ? "rotate-180"
                                : ""
                            }`}
                          />
                          <span className="sr-only">Toggle details</span>
                        </Button>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              Actions
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                              <Link href={`/orders/${order.id}`}>
                                View details
                              </Link>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  </TableRow>
                  {expandedOrder === order.id.toString() && (
                    <TableRow>
                      <TableCell colSpan={6} className="p-0">
                        <div className="bg-muted/50 p-4">
                          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                            <Card>
                              <CardContent className="p-4">
                                <h3 className="font-semibold mb-2">
                                  Order Items
                                </h3>
                                <ul className="space-y-3">
                                  {items.map((item, index) => (
                                    <li
                                      key={index}
                                      className="flex justify-between gap-2"
                                    >
                                      <div className="flex items-center gap-2">
                                        <div className="relative h-10 w-10 overflow-hidden rounded-md">
                                          <Image
                                            src={
                                              item.product.images.at(0)?.url ||
                                              "/placeholder.svg"
                                            }
                                            alt={item.product.name}
                                            fill
                                            className="object-cover"
                                          />
                                        </div>
                                        <div>
                                          <p className="text-sm font-medium">
                                            {item.product.name}
                                          </p>
                                          <p className="text-xs text-muted-foreground">
                                            Qty: {item.quantity}
                                          </p>
                                        </div>
                                      </div>
                                      <p className="text-sm font-medium">
                                        ৳{item.price}
                                      </p>
                                    </li>
                                  ))}
                                </ul>
                              </CardContent>
                            </Card>

                            <Card>
                              <CardContent className="p-4">
                                <h3 className="font-semibold mb-2">
                                  Payment Details
                                </h3>
                                <div className="space-y-2">
                                  <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">
                                      Sub-total:
                                    </span>
                                    <span className="text-sm">
                                      ৳{order.subTotal}
                                    </span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">
                                      Shipping:
                                    </span>
                                    <span className="text-sm">
                                      ৳{order.shipping}
                                    </span>
                                  </div>
                                  <div className="flex justify-between border-t pt-2 mt-2">
                                    <span className="font-medium">Total:</span>
                                    <span className="font-medium">
                                      ৳{order.subTotal + order.shipping}
                                    </span>
                                  </div>
                                  <div className="pt-2">
                                    <span className="text-sm text-muted-foreground">
                                      Payment Method:
                                    </span>
                                    <p className="text-sm">Cash On Delivery</p>
                                  </div>
                                </div>
                              </CardContent>
                            </Card>

                            <Card>
                              <CardContent className="p-4">
                                <h3 className="font-semibold mb-2">
                                  Delivery Address
                                </h3>
                                <div className="space-y-1">
                                  <p className="text-sm font-medium">
                                    {order.address.label}
                                  </p>
                                  <p className="text-sm">
                                    {order.address.phone}
                                  </p>
                                  <p className="text-sm">
                                    {order.address.name}
                                  </p>
                                  <p className="text-sm">
                                    {order.address.home}
                                  </p>
                                </div>
                              </CardContent>
                              {/* <CardFooter className="flex justify-end p-4 pt-0">
                                <Button variant="outline" size="sm" asChild>
                                  <Link href={`/orders/${order.id}`}>
                                    View full details
                                  </Link>
                                </Button>
                              </CardFooter> */}
                            </Card>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* {totalPages > 1 && (
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                href="#"
                onClick={(e: any) => {
                  e.preventDefault();
                  if (currentPage > 1) setCurrentPage(currentPage - 1);
                }}
                className={
                  currentPage === 1 ? "pointer-events-none opacity-50" : ""
                }
              />
            </PaginationItem>
            {Array.from({ length: totalPages }).map((_, i) => (
              <PaginationItem key={i}>
                <PaginationLink
                  href="#"
                  onClick={(e: any) => {
                    e.preventDefault();
                    setCurrentPage(i + 1);
                  }}
                  isActive={currentPage === i + 1}
                >
                  {i + 1}
                </PaginationLink>
              </PaginationItem>
            ))}
            <PaginationItem>
              <PaginationNext
                href="#"
                onClick={(e: any) => {
                  e.preventDefault();
                  if (currentPage < totalPages) setCurrentPage(currentPage + 1);
                }}
                className={
                  currentPage === totalPages
                    ? "pointer-events-none opacity-50"
                    : ""
                }
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )} */}
    </div>
  );
}
