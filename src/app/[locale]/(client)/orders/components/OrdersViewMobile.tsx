"use client";

import { useState } from "react";

import { useSelector } from "@xstate/store/react";
import { store } from "@udoy/state";
import OrderCard from "./OrderCard";


export function OrdersViewMobile() {
  const orders = useSelector(store, (state) => state.context.orders);

  return (
    <div className="space-y-4">
      <div className="rounded-md">
        {orders.map((order) => (
          <OrderCard key={order.id} order={order} />
        ))}
      </div>
    </div>
  );
}
