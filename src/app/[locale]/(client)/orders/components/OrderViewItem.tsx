import { Product } from "@prisma/client";
import Locale from "@udoy/components/Locale/Client";
import { Button } from "@udoy/components/ui/button";
import { UnitUtil } from "@udoy/utils/product-unit";
import { Minus, Plus } from "lucide-react";
import { getLocale } from "next-intl/server";
import Image from "next/image";
import React, { Fragment } from "react";

async function OrderViewItem({
  product,
  count,
  price,
}: {
  product: Product;
  count: number;
  price: number;
}) {
  const locale = await getLocale();

  return (
    <div key={product.id} className="flex gap-2 mt-1">
      <div className="">
        <Image
          src={(product as any).images?.[0]?.url}
          width={40}
          height={40}
          alt={product.name}
        />
      </div>
      <div className="flex-1">
        <h3 className="line-clamp-1 text-xs">
          <Locale bn={product.nam}>{product.name}</Locale>
        </h3>
        <div className="text-xs gap-3 flex items-center">
          <span className="text-xs ">৳{price.toLocaleString(locale)}</span>

          <div className="flex gap-2 items-center flex-1 justify-end"></div>
        </div>
      </div>

      <div className="">
        <span className=" font-semibold">
          ৳{(count * price).toLocaleString(locale)}
        </span>
      </div>
    </div>
  );
}

export default OrderViewItem;
