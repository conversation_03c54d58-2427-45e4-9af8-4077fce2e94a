"use client";

import { useState } from "react";
import { CalendarIcon, FilterIcon } from "lucide-react";
import { format } from "date-fns";

import { Button } from "@udoy/components/ui/button";
import { Calendar } from "@udoy/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@udoy/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetFooter,
  SheetClose,
} from "@udoy/components/ui/sheet";
import { Label } from "@udoy/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@udoy/components/ui/radio-group";
import { Slider } from "@udoy/components/ui/slider";

export function OrderFilters() {
  const [date, setDate] = useState<Date>();
  const [priceRange, setPriceRange] = useState([0, 1000]);

  return (
    <div className="flex flex-wrap items-center gap-2">
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline" className="flex items-center gap-2">
            <CalendarIcon className="h-4 w-4" />
            {date ? format(date, "PPP") : "Filter by date"}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="single"
            selected={date}
            onSelect={setDate}
            initialFocus
          />
        </PopoverContent>
      </Popover>

      <Select>
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Sort by" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="date-desc">Newest first</SelectItem>
          <SelectItem value="date-asc">Oldest first</SelectItem>
          <SelectItem value="price-desc">Price: High to low</SelectItem>
          <SelectItem value="price-asc">Price: Low to high</SelectItem>
        </SelectContent>
      </Select>

      <Sheet>
        <SheetTrigger asChild>
          <Button variant="outline" className="flex items-center gap-2">
            <FilterIcon className="h-4 w-4" />
            Advanced filters
          </Button>
        </SheetTrigger>
        <SheetContent>
          <SheetHeader>
            <SheetTitle>Filter Orders</SheetTitle>
            <SheetDescription>
              Apply filters to narrow down your order history
            </SheetDescription>
          </SheetHeader>
          <div className="grid gap-6 py-6">
            <div className="space-y-2">
              <Label>Order Status</Label>
              <RadioGroup defaultValue="all">
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="all" id="all" />
                  <Label htmlFor="all">All</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="processing" id="processing" />
                  <Label htmlFor="processing">Processing</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="shipped" id="shipped" />
                  <Label htmlFor="shipped">Shipped</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="delivered" id="delivered" />
                  <Label htmlFor="delivered">Delivered</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="cancelled" id="cancelled" />
                  <Label htmlFor="cancelled">Cancelled</Label>
                </div>
              </RadioGroup>
            </div>

            <div className="space-y-2">
              <Label>Price Range</Label>
              <div className="pt-4">
                <Slider
                  defaultValue={[0, 1000]}
                  max={1000}
                  step={10}
                  onValueChange={(value) => setPriceRange(value)}
                />
                <div className="flex items-center justify-between mt-2">
                  <span className="text-sm">₹{priceRange[0]}</span>
                  <span className="text-sm">₹{priceRange[1]}</span>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Time Period</Label>
              <RadioGroup defaultValue="all-time">
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="all-time" id="all-time" />
                  <Label htmlFor="all-time">All time</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="last-month" id="last-month" />
                  <Label htmlFor="last-month">Last month</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="last-3-months" id="last-3-months" />
                  <Label htmlFor="last-3-months">Last 3 months</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="last-6-months" id="last-6-months" />
                  <Label htmlFor="last-6-months">Last 6 months</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="last-year" id="last-year" />
                  <Label htmlFor="last-year">Last year</Label>
                </div>
              </RadioGroup>
            </div>
          </div>
          <SheetFooter>
            <SheetClose asChild>
              <Button type="submit">Apply Filters</Button>
            </SheetClose>
          </SheetFooter>
        </SheetContent>
      </Sheet>

      {date && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setDate(undefined)}
          className="h-8 text-xs"
        >
          Clear date
        </Button>
      )}
    </div>
  );
}
