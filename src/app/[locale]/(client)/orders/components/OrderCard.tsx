"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { format } from "date-fns";
import { CalendarIcon, ChevronDownIcon, Clock } from "lucide-react";

import { Button } from "@udoy/components/ui/button";
import { Card, CardContent } from "@udoy/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@udoy/components/ui/dropdown-menu";
import { Badge } from "@udoy/components/ui/badge";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@udoy/components/ui/collapsible";
import { OrderWithItems } from "@udoy/utils/types";

// Helper function to get the status badge
const getStatusBadge = (status: keyof typeof statusMap) => {
  const statusMap = {
    PENDING: { label: "Pending", variant: "outline" },
    PROCESSING: { label: "Processing", variant: "secondary" },
    SHIPPED: { label: "Shipped", variant: "default" },
    DELIVERED: { label: "Delivered", variant: "success" },
    CANCELLED: { label: "Cancelled", variant: "destructive" },
  };

  const config = statusMap[status] || { label: status, variant: "outline" };

  return <Badge variant={config.variant as any}>{config.label}</Badge>;
};

export default function OrderCard({ order }: { order: OrderWithItems }) {
  const [isExpanded, setIsExpanded] = useState(false);
  const items = order.orderItems;

  return (
    <Card className="w-full mb-3 overflow-hidden">
      <CardContent className="p-4">
        {/* Header with Order ID and Status */}
        <div className="flex justify-between items-center mb-3">
          <div className="flex gap-2">
            <span className="font-medium text-sm">#{order.id}</span>
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <span>{format(order.createdAt, "MMM dd, yyyy")}</span> |{" "}
              <span>{format(order.createdAt, "h:mm a")}</span>
            </div>
          </div>
          <div>{getStatusBadge(order.status as any)}</div>
        </div>

        {/* Main content with product info and price */}
        <div className="flex items-start gap-3">
          {/* Product image */}
          <div className="relative h-16 w-16 overflow-hidden rounded-md shrink-0">
            <Image
              src={items[0]?.product?.images?.at(0)?.url || "/placeholder.svg"}
              alt={items[0].product.name}
              fill
              className="object-cover"
            />
          </div>

          {/* Product details */}
          <div className="flex-1 min-w-0">
            <p className="font-medium">{items[0].product.name}</p>
            {items.length > 1 && (
              <p className="text-sm text-muted-foreground">
                +{items.length - 1} more items
              </p>
            )}
          </div>

          {/* Price */}
          <div className="text-right font-medium">
            ৳{order.subTotal + order.shipping}
          </div>
        </div>

        {/* Actions */}
        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <div className="">
            <div className="flex items-center gap-2">
              <CollapsibleTrigger asChild>
                <Button variant="outline" size="sm" className="w-full mt-2">
                  View ALL
                </Button>
              </CollapsibleTrigger>
            </div>
          </div>

          {/* Expanded content */}
          <CollapsibleContent className="pt-3">
            <div className="space-y-3 text-sm">
              {/* Items list */}
              {items.map((item, index) => (
                <div
                  key={index}
                  className="flex items-center gap-3 py-2 border-t"
                >
                  <div className="relative h-10 w-10 overflow-hidden rounded-md shrink-0">
                    <Image
                      src={
                        item.product?.images?.at(0)?.url || "/placeholder.svg"
                      }
                      alt={item.product.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium line-clamp-1">
                      {item.product.name}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Qty: {item.quantity} × ৳{item.price}
                    </p>
                  </div>
                  <div className="text-right font-medium">
                    ৳{item.quantity * item.price}
                  </div>
                </div>
              ))}

              {/* Order summary */}
              <div className="grid grid-cols-2 gap-2 pt-2 border-t">
                <div className="text-muted-foreground">Subtotal:</div>
                <div className="text-right">৳{order.subTotal}</div>

                <div className="text-muted-foreground">Shipping:</div>
                <div className="text-right">৳{order.shipping}</div>

                <div className="text-muted-foreground font-medium">Total:</div>
                <div className="text-right font-medium">
                  ৳{order.subTotal + order.shipping}
                </div>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
  );
}
