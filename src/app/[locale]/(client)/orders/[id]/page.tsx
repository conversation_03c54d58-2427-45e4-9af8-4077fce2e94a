import type { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import Image from "next/image";
import { ArrowLeftIcon, TruckIcon } from "lucide-react";
import { format } from "date-fns";

import { Button } from "@udoy/components/ui/button";
import { Badge } from "@udoy/components/ui/badge";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import { Separator } from "@udoy/components/ui/separator";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@udoy/components/ui/breadcrumb";
import { cacheTag } from "next/dist/server/use-cache/cache-tag";
import { CacheKey } from "@udoy/utils/cache-key";
import { getPrisma } from "@udoy/utils/db-utils";
import { notFound } from "next/navigation";
import { OrderStatus } from "@prisma/client";
import OrderViewItem from "../components/OrderViewItem";

export const metadata: Metadata = {
  title: "Order Details | User Dashboard",
  description: "View details of your order",
};

async function getOrderDetails(orderId: string) {
  "use cache";
  cacheTag(CacheKey.UserOrder(orderId));

  const order = await getPrisma().order.findUnique({
    where: {
      id: parseInt(orderId),
    },
    include: {
      orderItems: {
        include: {
          product: {
            include: {
              images: true,
              unit: true,
            },
          },
        },
      },
      address: true,
    },
  });

  return order;
}

export default async function OrderDetailsPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;

  if (Number.isNaN(parseInt(id))) {
    return notFound();
  }

  const order = await getOrderDetails(id);

  if (!order) {
    return notFound();
  }

  // Get status badge styling
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "processing":
        return (
          <Badge
            variant="outline"
            className="bg-yellow-50 text-yellow-700 border-yellow-200"
          >
            Processing
          </Badge>
        );
      case "shipped":
        return (
          <Badge
            variant="outline"
            className="bg-blue-50 text-blue-700 border-blue-200"
          >
            Shipped
          </Badge>
        );
      case "delivered":
        return (
          <Badge
            variant="outline"
            className="bg-green-50 text-green-700 border-green-200"
          >
            Delivered
          </Badge>
        );
      case "cancelled":
        return (
          <Badge
            variant="outline"
            className="bg-red-50 text-red-700 border-red-200"
          >
            Cancelled
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="">
            {status}
          </Badge>
        );
    }
  };

  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex flex-col gap-6">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/orders">My orders</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href={`/orders/${id}`}>
                Order #{id}
              </BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div className="flex items-center gap-2">
            <Button variant="outline" size="icon" asChild>
              <Link href="/orders">
                <ArrowLeftIcon className="h-4 w-4" />
                <span className="sr-only">Back to orders</span>
              </Link>
            </Button>
            <h1 className="text-2xl font-bold tracking-tight">
              Order #{id}
              <span className="ml-2 text-muted-foreground font-normal">
                {format(order?.createdAt, "MMM dd, yyyy")} |{" "}
                {format(order.createdAt, "h:mm a")}
              </span>
            </h1>
          </div>
          {/* <div className="flex items-center gap-2">
            {order.status === OrderStatus.CANCELLED ? (
              <Button>Re-Order</Button>
            ) : (
              <Button>Track Order</Button>
            )}
          </div> */}
        </div>

        <div className="grid gap-6 md:grid-cols-3">
          <div className="md:col-span-2 space-y-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-xl">Order Items</CardTitle>
                {getStatusBadge(order.status)}
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {order.orderItems.map((item, index) => (
                    <OrderViewItem
                      key={item.id}
                      product={item.product}
                      count={item.quantity}
                      price={item.price}
                    />
                  ))}
                </div>
              </CardContent>
              <CardFooter className="flex justify-between border-t pt-6">
                <p className="text-muted-foreground">Sub Total:</p>
                <p className="font-medium">৳{order.subTotal}</p>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-xl">Delivery Information</CardTitle>
              </CardHeader>
              <CardContent className="grid gap-6 md:grid-cols-2">
                <div>
                  <h3 className="font-medium mb-2">Delivery Address</h3>
                  <div className="space-y-1 text-sm">
                    <p className="font-medium">{order.address.label}</p>
                    <p>{order.address.phone}</p>
                    <p>{order.address.name}</p>
                    <p>{order.address.home}</p>
                  </div>
                </div>
                <div>
                  <h3 className="font-medium mb-2">Delivery Method</h3>
                  <div className="flex items-center gap-2">
                    <TruckIcon className="h-5 w-5 text-muted-foreground" />
                    <span>Standard Delivery</span>
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">
                    Estimated delivery: 3-5 business days
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-xl">Order Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Sub-total</span>
                  <span>৳{order.subTotal}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Shipping</span>
                  <span>৳{order.shipping}</span>
                </div>
                <Separator />
                <div className="flex justify-between font-medium">
                  <span>Total</span>
                  <span>৳{order.subTotal + order.shipping}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-xl">Payment Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Payment Method</span>
                  <span>Cash On Delivery</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Payment Status</span>
                  <Badge
                    variant={
                      order.status === OrderStatus.CANCELLED
                        ? "destructive"
                        : "outline"
                    }
                  >
                    {order.status === OrderStatus.CANCELLED
                      ? "Cancelled"
                      : "Paid"}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* <Card>
              <CardHeader>
                <CardTitle className="text-xl">Need Help?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  If you have any questions or concerns about your order, our
                  customer support team is here to help.
                </p>
                <Button variant="outline" className="w-full">
                  Contact Support
                </Button>
              </CardContent>
            </Card> */}
          </div>
        </div>
      </div>
    </div>
  );
}
