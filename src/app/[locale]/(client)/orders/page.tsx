import type { Metada<PERSON> } from "next";
import { HomeIcon, SearchIcon } from "lucide-react";
import { Input } from "@udoy/components/ui/input";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON>Trigger,
} from "@udoy/components/ui/tabs";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@udoy/components/ui/breadcrumb";
import { OrderFilters } from "./components/OrderFilters";
import { OrdersTable } from "./components/OrdersTable";
import { getPrisma } from "@udoy/utils/db-utils";
import { CookieUtil } from "@udoy/utils/cookie-util";
import RenderOrders from "./components/SyncOrders";

export const metadata: Metadata = {
  title: "Orders | User Dashboard",
  description: "View and manage your order history",
};

async function getData() {
  const prisma = getPrisma();
  const userId = await <PERSON><PERSON><PERSON><PERSON>.userId();

  if (!userId) {
    return [];
  }

  const orders = await prisma.order.findMany({
    where: {
      buyerId: userId,
    },
    include: {
      orderItems: {
        include: {
          product: {
            include: {
              images: true,
            },
          },
        },
      },
      address: true,
      buyer: true,
    },
    take: 30,
  });

  return orders;
}

export default async function OrdersPage() {
  const orders = await getData();
  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex flex-col gap-6">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink className="flex" href="/">
                <HomeIcon className="h-4 w-4 mr-1" />
                Home
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/orders">My orders</BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <h1 className="text-3xl font-bold tracking-tight">My orders</h1>
          {/* <div className="flex items-center gap-2 w-full md:w-auto">
            <div className="relative w-full md:w-[300px]">
              <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search orders..."
                className="pl-8 w-full"
              />
            </div>
          </div> */}
        </div>

        {/* <OrderFilters /> */}

        <RenderOrders orders={orders} />
      </div>
    </div>
  );
}
