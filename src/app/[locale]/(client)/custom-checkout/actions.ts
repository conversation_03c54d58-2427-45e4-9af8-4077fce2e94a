"use server";

import { Role } from "@prisma/client";
import { ActionError } from "@udoy/utils/app-error";
import { CookieUtil } from "@udoy/utils/cookie-util";
import { getPrisma } from "@udoy/utils/db-utils";
import { CustomerFormValues } from "./schema";
import { AddressInputSchema } from "@udoy/libs/zod-schema";

export async function searchUsers(query: string) {
  try {
    const userId = await CookieUtil.userId();
    const prisma = getPrisma();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const users = await prisma.user.findMany({
      where: {
        OR: [
          { name: { contains: query, mode: "insensitive" } },
          { email: { contains: query, mode: "insensitive" } },
          { phone: { contains: query, mode: "insensitive" } },
          {
            address: {
              some: {
                OR: [
                  {
                    phone: { contains: query, mode: "insensitive" },
                  },
                  {
                    zone: {
                      name: { contains: query, mode: "insensitive" },
                    },
                  },
                ],
              },
            },
          },
        ],
      },
      include: {
        address: {
          include: {
            zone: true,
          },
        },
        cart: true,
      },
      take: 10,
    });
    return users;
  } catch (error) {
    return ActionError("Failed To Search Users");
  }
}

export async function createCustomer(data: CustomerFormValues) {
  try {
    const userId = await CookieUtil.userId();
    const prisma = getPrisma();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const customer = await prisma.user.create({
      data: {
        name: data.name,
        phone: data.phone,
        email: data.email,
        cart: {
          create: {},
        },
      },
      include: {
        address: {
          include: {
            zone: true,
          },
        },
        cart: true,
      },
    });

    return customer;
  } catch (error) {
    return ActionError("Failed To Create Customer");
  }
}

export async function updateCurrentAddress(
  customerId: number,
  addressId: string
) {
  try {
    const userId = await CookieUtil.userId();
    const prisma = getPrisma();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const address = await prisma.address.findUnique({
      where: {
        userId: customerId,
        id: addressId,
      },
    });

    if (!address) {
      return ActionError("Invalid Address");
    }

    await prisma.cart.update({
      where: {
        userId,
      },
      data: {
        addressId,
      },
    });

    return await prisma.user.findUnique({
      where: {
        id: customerId,
      },
      include: {
        address: {
          include: {
            zone: true,
          },
        },
        cart: true,
      },
    });
  } catch (error) {
    return ActionError("Failed to add to cart");
  }
}

export async function createAddress(
  data: AddressInputSchema,
  customerId: number
) {
  try {
    let userId = await CookieUtil.userId();
    const prisma = getPrisma();

    if (!userId) {
      return ActionError("User Not Logged In");
    }

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const { zoneId, name, home, phone, label, location } =
      AddressInputSchema.parse(data);

    const cart = await prisma.cart.update({
      where: {
        userId: customerId,
      },
      data: {
        address: {
          create: {
            home,
            name,
            label,
            phone,
            location,
            userId: customerId,
            zoneId,
          },
        },
      },
    });

    const user = await prisma.user.findUnique({
      where: {
        id: customerId,
      },
      include: {
        address: {
          include: {
            zone: true,
          },
        },
        cart: true,
      },
    });

    return user;
  } catch (error: any) {
    return ActionError(error?.message || "Failed To Add Address");
  }
}
