"use client";

import Locale from "@udoy/components/Locale/Client";
import { But<PERSON> } from "@udoy/components/ui/button";
import { Edit, SearchIcon, Trash } from "lucide-react";
import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@udoy/components/ui/dialog";
import { Address, Prisma, User } from "@prisma/client";
import { store } from "@udoy/state";
import { useSelector } from "@xstate/store/react";
import { useLocale } from "next-intl";
import Hide from "@udoy/components/Hide";
import { Badge } from "@udoy/components/ui/badge";
import { cn } from "@udoy/utils/shadcn";
import ManageAddresses from "./ManageAddress";
import useStatus from "@udoy/hooks/useToastUtil";
import { withError } from "@udoy/utils/app-error";
import { updateCurrentAddress } from "@udoy/actions/cart";
import { deleteUserAddress } from "@udoy/actions/delivary-zone";
import { Input } from "@udoy/components/ui/input";
import { searchUsers } from "../actions";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@udoy/components/ui/avatar";
import { AddressWithZone } from "@udoy/utils/types";
import { useAtom } from "jotai";
import { currentUserAtom } from "../state";
import AddCustomUser from "./AddCustomerForm";

function TargetCustomer() {
  const locale = useLocale();
  const addresses = useSelector(store, (state) => state.context.addresses);
  const status = useStatus();
  const [open, setOpen] = useState(false);
  const [users, setUsers] = useState(
    [] as (User & { address: AddressWithZone[] })[]
  );

  const [currentUser, setCurrentUser] = useAtom(currentUserAtom);

  async function handleUpdateAddress(addressId: string) {
    try {
      status.loading("Updating User Delivery Address...");
      await withError(updateCurrentAddress(addressId));
      status.success("Updated User Address Successfully!");
      store.send({ type: "changeAddress", addressId });
    } catch (error: any) {
      status.error(error.message);
    }
    setOpen(false);
  }

  async function handleDeleteAddress(addressId: string) {
    try {
      status.loading("Deleting User Delivery Address...");
      await withError(deleteUserAddress(addressId));
      status.success("Deleted User Address Successfully!");
      store.send({ type: "deleteAddress", addressId });
    } catch (error: any) {
      status.error(error.message);
    }
    setOpen(false);
  }

  async function handleSearch(query: string) {
    try {
      const users = await withError(searchUsers(query));
      setUsers(users);
    } catch (error: any) {
      status.error(error.message);
    }
  }

  return (
    <div className="rounded shadow overflow-clip">
      <div className="flex justify-between bg-brand items-center pl-4">
        <h3 className="font-medium min-w-max">
          <Locale bn="কাস্টমার তথ্য">Customer Information</Locale>
        </h3>

        <AddCustomUser />
      </div>

      <div className="p-4 border rounded-b">
        <Dialog open={open} onOpenChange={(open) => setOpen(open)}>
          {/* <DialogTrigger asChild>
            
          </DialogTrigger> */}
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Search Customers</DialogTitle>
            </DialogHeader>
            <div className="">
              <Input
                type="search"
                placeholder="Search customers..."
                // className="pl-8"
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleSearch(e.currentTarget.value);
                  }
                }}
              />

              <div className="mt-4 gap-2 flex flex-col">
                {users.map((user) => (
                  <div
                    key={user.id}
                    className={cn(
                      "p-2 flex gap-2 border items-center  rounded cursor-pointer hover:bg-brand/10 duration-150",
                      currentUser?.id === user.id && "!bg-brand/20"
                    )}
                    onClick={() => {
                      setCurrentUser(user as any);
                      setOpen(false);
                    }}
                  >
                    <Avatar className="h-8 w-8">
                      <AvatarImage
                        src={user.avatar || "/placeholder.svg"}
                        alt={user.name}
                      />
                      <AvatarFallback>
                        {user.name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </AvatarFallback>
                    </Avatar>

                    <div className="">
                      <p className="font-medium text-sm">{user.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {user.email}
                      </p>
                    </div>
                    <div className="flex-1"></div>

                    <Hide open={Boolean(user.address.length)}>
                      <Badge>
                        {user.address.at(0)?.zone?.name}
                        {user.address.length > 1
                          ? ` +${user.address.length - 1}`
                          : ""}
                      </Badge>
                    </Hide>
                  </div>
                ))}
              </div>
            </div>
          </DialogContent>
        </Dialog>
        <Hide
          open={Boolean(currentUser)}
          fallback={
            <Button onClick={() => setOpen(true)} className="w-full gap-2">
              <SearchIcon className="-mt-0.5" size={18} />
              <span>Search...</span>
            </Button>
          }
        >
          <div className="text-sm relative">
            {/* <Dialog open={open} onOpenChange={(open) => setOpen(open)}>
              <DialogTrigger asChild>
                <Button
                  className="absolute -right-2 -top-2"
                  variant="ghost"
                  size="icon"
                >
                  <Edit />
                </Button>
              </DialogTrigger>
              <DialogContent className="">
                <DialogHeader>
                  <DialogTitle>Your Addresses</DialogTitle>
                </DialogHeader>
                <div className="grid gap-2 max-h-[400px] overflow-y-auto">
                  {addresses.map((address) => (
                    <div
                      key={address.id}
                      className={cn(
                        "border p-4 rounded text-sm flex items-center gap-2 cursor-pointer select-none hover:bg-brand/10 duration-150",
                        address.id === currentAddress?.id && "!bg-brand"
                      )}
                      onClick={() => handleUpdateAddress(address.id)}
                    >
                      <div className="flex-1">
                        <div className="">
                          <span className="font-semibold">Name:&nbsp;</span>
                          <span>{address.name}</span>
                        </div>
                        <div className="">
                          <span className="font-semibold">
                            Phone Number:&nbsp;
                          </span>
                          <span>{address.phone}</span>
                        </div>
                        <div className="">
                          <span className="font-semibold">
                            Home Address:&nbsp;
                          </span>
                          <span>{address.home}</span>
                        </div>
                      </div>
                      <Hide open={!Boolean(address.id === currentAddress?.id)}>
                        <Button
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleDeleteAddress(address.id);
                          }}
                          className=""
                          variant="outline"
                          size="icon"
                        >
                          <Trash />
                        </Button>
                      </Hide>
                    </div>
                  ))}
                </div>
              </DialogContent>
            </Dialog> */}
            <Button
              className="absolute -right-2 -top-2"
              variant="ghost"
              size="icon"
              onClick={() => setOpen(true)}
            >
              <Edit />
            </Button>
            <div className="flex gap-2 items-center">
              <Avatar>
                <AvatarImage
                  src={currentUser?.avatar!}
                  alt={currentUser?.name}
                />
                <AvatarFallback className="rounded-lg">CN</AvatarFallback>
              </Avatar>
              <div className="">
                <p className="">
                  <span className="font-bold">{currentUser?.name}</span>
                </p>
                <p className="flex gap-2 items-center">{currentUser?.email}</p>
              </div>
            </div>
          </div>
        </Hide>
      </div>
    </div>
  );
}

export default TargetCustomer;
