"use client";

import Locale from "@udoy/components/Locale/Client";
import { Button } from "@udoy/components/ui/button";
import {
  CheckCircle,
  DeleteIcon,
  Edit,
  LocateIcon,
  MapIcon,
  Plus,
  Trash,
} from "lucide-react";
import React, { Fragment, useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@udoy/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import { Address } from "@prisma/client";
import { store } from "@udoy/state";
import { useSelector } from "@xstate/store/react";
import { useLocale, useTranslations } from "next-intl";
import { createAddress, updateAddress } from "@udoy/actions/delivary-zone";
import Hide from "@udoy/components/Hide";
import { cn } from "@udoy/utils/shadcn";
import { SelectLabel } from "@radix-ui/react-select";
import { createAtom } from "@xstate/store";
import { useForm } from "react-hook-form";
import { AddressInputSchema } from "@udoy/libs/zod-schema";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@udoy/components/ui/form";
import { Input } from "@udoy/components/ui/input";
import { z } from "zod";
import { toast } from "sonner";
import useStatus from "@udoy/hooks/useToastUtil";
import { withError } from "@udoy/utils/app-error";
import Link from "next/link";
import { isValidPlusCode } from "@udoy/utils";
import { CustomerFormValues, customerSchema } from "../schema";
import { useSetAtom } from "jotai";
import { currentUserAtom } from "../state";
import { createCustomer } from "../actions";

function AddCustomUser({ button }: { button?: React.ReactNode }) {
  const locale = useLocale();
  const isBangla = locale === "bn";
  const form = useForm<CustomerFormValues>({
    resolver: zodResolver(customerSchema),
    defaultValues: {
      email: "",
      name: "",
      phone: "",
    },
  });
  const [open, setOpen] = useState(false);

  const setUser = useSetAtom(currentUserAtom);

  if (!button) {
    button = (
      <Button className="rounded-none gap-1">
        <Plus size={18} />
        <span>
          <Locale bn="যোগ করুন">Add New</Locale>
        </span>
      </Button>
    );
  }


  async function handleSubmit(values: CustomerFormValues) {
    try {
      const result = await withError(
        createCustomer(values)
      );
      if (result) {
        setUser(result as any);
      }
      
      toast.success("Customer created");
      form.reset();
      setOpen(false);
    } catch (error: any) {
      toast.error(error.message || "Failed to create customer");
    }
  }

  return (
    <Dialog open={open} onOpenChange={(open) => setOpen(open)}>
      <DialogTrigger asChild>{button}</DialogTrigger>
      <DialogContent
        className="rounded"
        style={{ width: "calc(100vw - 2rem)" }}
      >
        <DialogHeader>
          <DialogTitle className="flex gap-2 items-center">
            <MapIcon />
            <span>
              <Locale bn="নতুন কাস্টমার যোগ করুন">Add New Customer</Locale>
            </span>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="block">
            <div className="grid grid-cols-2 gap-3 pt-3 gap-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      <Locale bn="কাস্টমার নাম">Customer Name</Locale>
                    </FormLabel>
                    <FormControl>
                      <Input
                        required
                        placeholder={isBangla ? "করিম আলি" : "Korim Ali"}
                        minLength={3}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      <Locale bn="মোবাইল নম্বর">Mobile Number</Locale>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="tel"
                        placeholder={isBangla ? "০১৭১..." : "0171..."}
                        inputMode="numeric"
                        required
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem className="">
                    <FormLabel>
                      <Locale bn="ইমেইল">Email</Locale>
                    </FormLabel>
                    <FormControl>
                      <Input
                        required
                        minLength={3}
                        placeholder={
                          isBangla
                            ? "গ্রাহকের ইমেইল ঠিকানা"
                            : "Customer's email address"
                        }
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <DialogFooter className="grid grid-cols-2 mt-5 gap-2">
              <DialogTrigger asChild>
                <Button variant="outline">
                  <Locale bn="বাতিল করুন">Cancel</Locale>
                </Button>
              </DialogTrigger>
              {/* <DialogTrigger asChild> */}
                <Button type="submit">
                  <Locale bn="যোগ করুন">Add</Locale>
                </Button>
              {/* </DialogTrigger> */}
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

export default AddCustomUser;
