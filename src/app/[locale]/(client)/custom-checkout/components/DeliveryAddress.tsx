"use client";

import Locale from "@udoy/components/Locale/Client";
import { Button } from "@udoy/components/ui/button";
import { Edit, Trash } from "lucide-react";
import React, { useEffect, useMemo, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@udoy/components/ui/dialog";
import { Address, Prisma } from "@prisma/client";
import { store } from "@udoy/state";
import { useSelector } from "@xstate/store/react";
import { useLocale } from "next-intl";
import Hide from "@udoy/components/Hide";
import { Badge } from "@udoy/components/ui/badge";
import { cn } from "@udoy/utils/shadcn";
import ManageAddresses from "./ManageAddress";
import useStatus from "@udoy/hooks/useToastUtil";
import { withError } from "@udoy/utils/app-error";
import { deleteUserAddress } from "@udoy/actions/delivary-zone";
import { useAtom, useAtomValue } from "jotai";
import { currentUserAtom } from "../state";
import { updateCurrentAddress } from "../actions";

function DeliveryAddress(props: {
  zones: Prisma.DeliveryZoneGetPayload<{
    include: {
      subZones: true;
    };
  }>[];
}) {
  const locale = useLocale();
  const [currentUser, setCurrentUser] = useAtom(currentUserAtom);
  const addresses = useMemo(() => {
    return currentUser?.address || [];
  }, [currentUser]);

  const currentAddress = useMemo(() => {
    return currentUser?.address.find(
      (address) => address.id === currentUser.cart.addressId
    );
  }, [currentUser]);
  const status = useStatus();
  const [open, setOpen] = useState(false);

  useEffect(() => {
    // store.send({
    //   type: "setAddresses",
    //   addresses: props.addresses,
    // });

    store.send({
      type: "setZones",
      zones: props.zones,
    });
  }, [props.zones]);

  async function handleUpdateAddress(customerId: number, addressId: string) {
    try {
      status.loading("Updating User Delivery Address...");
      const user = await withError(updateCurrentAddress(customerId, addressId));
      setCurrentUser(user as any);
      status.success("Updated User Address Successfully!");
      setOpen(false);
    } catch (error: any) {
      status.error(error.message);
    }
    setOpen(false);
  }

  async function handleDeleteAddress(addressId: string) {
    try {
      status.loading("Deleting User Delivery Address...");
      await withError(deleteUserAddress(addressId));
      status.success("Deleted User Address Successfully!");
      store.send({ type: "deleteAddress", addressId });
    } catch (error: any) {
      status.error(error.message);
    }
    setOpen(false);
  }

  return (
    <div className="rounded shadow overflow-clip">
      <div className="flex justify-between bg-brand items-center pl-4">
        <h3 className="font-medium min-w-max">
          <Locale bn="ডেলিভারির ঠিকানা">Delivery Address</Locale>
        </h3>

        <ManageAddresses />
      </div>

      <div className="p-4 border rounded-b">
        <Hide
          open={Boolean(currentAddress)}
          fallback={
            <span className="text-muted-foreground">
              <Locale bn="কোন ঠিকানা নেই...">No Address Available</Locale>
            </span>
          }
        >
          <div className="text-sm relative">
            <Dialog open={open} onOpenChange={(open) => setOpen(open)}>
              <DialogTrigger asChild>
                <Button
                  className="absolute -right-2 -top-2"
                  variant="ghost"
                  size="icon"
                >
                  <Edit />
                </Button>
              </DialogTrigger>
              <DialogContent className="">
                <DialogHeader>
                  <DialogTitle>Customer Addresses</DialogTitle>
                </DialogHeader>
                <div className="grid gap-2 max-h-[400px] overflow-y-auto">
                  {addresses.map((address) => (
                    <div
                      key={address.id}
                      className={cn(
                        "border p-4 rounded text-sm flex items-center gap-2 cursor-pointer select-none hover:bg-brand/10 duration-150",
                        address.id === currentAddress?.id && "!bg-brand"
                      )}
                      onClick={() => {
                        handleUpdateAddress(currentUser?.id!, address.id);
                      }}
                    >
                      <div className="flex-1">
                        <div className="">
                          <span className="font-semibold">Name:&nbsp;</span>
                          <span>{address.name}</span>
                        </div>
                        <div className="">
                          <span className="font-semibold">
                            Phone Number:&nbsp;
                          </span>
                          <span>{address.phone}</span>
                        </div>
                        <div className="">
                          <span className="font-semibold">
                            Home Address:&nbsp;
                          </span>
                          <span>{address.home}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </DialogContent>
            </Dialog>

            <p className="">
              <span className="font-bold">{currentAddress?.name}</span>,{" "}
              {currentAddress?.phone}
            </p>
            <p className="flex gap-2 items-center">
              <span>Zone:</span>
              <Badge className="pt-1">
                <Locale bn={currentAddress?.zone?.nam}>
                  {currentAddress?.zone?.name}
                </Locale>
              </Badge>
            </p>

            <p className="">Home: {currentAddress?.home}</p>
          </div>
        </Hide>
      </div>
    </div>
  );
}

export default DeliveryAddress;
