"use client";

import Button from "@udoy/components/Button";
import Hide from "@udoy/components/Hide";
import ManageAddress from "./ManageAddress";
import { Address, DeliveryZone } from "@prisma/client";
import { useMemo, useState, useTransition } from "react";
import { classUtil } from "@udoy/utils/class-util";
import useStatus from "@udoy/hooks/useToastUtil";
import { toast } from "react-toastify";
import { placeOrder, updateCurrentAddress } from "@udoy/actions/cart";
import { withError } from "@udoy/utils/app-error";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";

function CheckoutForm({
  subtotal,
  addresses,
  currentAddress,
  zones,
}: {
  subtotal: number;
  currentAddress: string | null | undefined;
  addresses: (Address & { zone: DeliveryZone })[];
  zones: DeliveryZone[];
}) {
  const [addressId, setAddressId] = useState(currentAddress);
  const [isPending, startTransition] = useTransition();
  const router = useRouter();
  const { delivaryFee, total } = useMemo(() => {
    const address = addresses.find((address) => address.id === addressId);
    const delivaryFee = address?.zone.charge || 0;
    const total = subtotal + delivaryFee;
    return { delivaryFee, total };
  }, [addressId]);

  async function handlePlaceOrder() {
    try {
      await withError(placeOrder());
      router.replace("/order-successful");
    } catch (error: any) {
      toast.error(error?.message || "Failed to place order");
    }
  }

  async function handleAddressUpdate(addressId: string) {
    setAddressId(addressId);
    startTransition(async () => {
      try {
        await withError(updateCurrentAddress(addressId));
      } catch (error: any) {
        setAddressId(currentAddress);
        toast.error(error?.message || "Failed to update address");
      }
    });
  }

  return (
    <div className=" flex mx-auto flex-1 w-full mt-6 gap-4 justify-center ">
      <div className="max-w-xl">
        <div className="w-full border border-white/10 rounded p-4">
          <div className="flex justify-between">
            <h3 className="text-xl font-semibold">Addresses:</h3>
            {/* <ManageAddress
              zones={zones || []}
              button={<Button label="Add new address" />}
            /> */}
          </div>
          <div className="">
            <Hide
              open={addresses.length > 0}
              fallback={
                <div className="text-lg p-3 border-dashed border-white/30 mt-3 rounded text-center border">
                  No addresses found
                </div>
              }
            >
              <div className="mt-5 flex gap-4 flex-wrap">
                {addresses.map((address) => (
                  <div
                    onClick={() => handleAddressUpdate(address.id)}
                    key={address.id}
                    className={classUtil(
                      "flex flex-1 min-w-[40%] cursor-pointer active:opacity-60 flex-col items-center border-dashed border-white/30 rounded px-3 py-2 border",
                      addressId === address.id && "!border-white"
                    )}
                  >
                    <h3 className="text-lg font-semibold text-center">
                      {address.name}
                    </h3>

                    <h4 className="bg-orange-950 px-3 py-1 rounded mt-2">
                      <span className="opacity-70">Zone: </span>
                      <span className="font-semibold">{address.zone.name}</span>
                    </h4>
                    <h4 className="text-center">{address.home}</h4>
                  </div>
                ))}
              </div>
            </Hide>
          </div>
        </div>
        <div className="w-full border border-white/10 rounded p-4 mt-6">
          <h3 className="text-xl font-semibold text-center">Payment Method</h3>

          <div className="mt-5 flex gap-4 flex-wrap">
            <div className="flex flex-1 min-w-[40%] flex-col items-center border-dashed border-white rounded px-3 py-2 border">
              <h3 className="text-lg font-semibold text-center">
                Cash ON Delivery
              </h3>
            </div>
          </div>
        </div>
      </div>
      <div className="max-w-sm flex-1">
        <div className="w-full border border-white/10 rounded p-4 ">
          <h3 className="text-xl font-semibold text-center">Order Details</h3>

          <div className="mt-5">
            <h4 className="flex justify-between gap-4">
              <span className="text-white/70">SubTotal:</span>
              <span>{subtotal}</span>
            </h4>
            <h4 className="flex justify-between gap-4">
              <span className="text-white/70">Delivery Fee:</span>
              <span>{delivaryFee}</span>
            </h4>
            <hr className="my-2 opacity-30" />
            <h4 className="flex justify-between gap-4">
              <span className="text-white/90 font-semibold">Total</span>
              <span className="font-bold">{total}</span>
            </h4>

            {/* <Submit label="Place Order" /> */}
            <Button
              loading={isPending}
              onClick={() => startTransition(handlePlaceOrder)}
              className="w-full mt-3"
              label="Place Order"
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default CheckoutForm;
