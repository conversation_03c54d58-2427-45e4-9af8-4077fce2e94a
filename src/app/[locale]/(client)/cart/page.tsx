import { useRecoilValue } from "recoil";
import { CartInfoState } from "../state";
import Image from "next/image";
import { getPrisma } from "@udoy/utils/db-utils";
import { CookieUtil } from "@udoy/utils/cookie-util";
import Link from "next/link";
import { UnitUtil } from "@udoy/utils/product-unit";
import Button from "@udoy/components/Button";
import ManageCart from "../components/ManageCartItem";

async function getData() {
  const prisma = getPrisma();
  const userId = await CookieUtil.userId();

  if (!userId) {
    return { cartItems: [], subtotal: 0 };
  }

  const cartItems = await prisma.cartItem.findMany({
    where: {
      cart: { userId },
    },
    include: {
      product: {
        include: {
          images: true,
          unit: true,
        },
      },
    },
  });

  let subtotal = 0;

  for (const item of cartItems) {
    subtotal += item.quantity * item.product.price;
  }

  return {
    cartItems,
    subtotal,
  };
}

async function CartPage() {
  const { cartItems, subtotal } = await getData();
  return (
    <main className="max-w-xl mx-auto flex-1 w-full mt-6 ">
      <div className="w-full border border-white/10 rounded p-4">
        <div className="">
          {cartItems.map(({ cartId, product, quantity }) => (
            <div key={cartId} className="flex justify-between items-center">
              <Image
                className="object-cover rounded"
                src={product.images[0]?.url}
                width={40}
                height={40}
                alt={product.name}
              />

              <h3 className="text-lg font-semibold">{product.name}</h3>
              <div className="flex items-center gap-2">
                <ManageCart productId={product.id} />
                <h3 className="p-0 m-0">{quantity}</h3>
                <ManageCart productId={product.id} add />
              </div>
              <h3>
                {UnitUtil.getAmountUnit(
                  quantity * product.amount,
                  product.unit
                )}
              </h3>
              <h3>{product.price * quantity} BDT</h3>
            </div>
          ))}
        </div>
        <div className="">
          <div className="w-max ml-auto mt-5 border border-white/10 p-3 rounded min-w-[200px]">
            <h4 className="flex justify-between gap-4">
              <span className="text-white/70">SubTotal:</span>
              <span>{subtotal}</span>
            </h4>
            {/* <h4 className="flex justify-between gap-4">
              <span className="text-white/70">Delivery Fee:</span>
              <span>20</span>
            </h4>
            <h4 className="flex justify-between gap-4">
              <span className="text-white/90 font-semibold">Grand Total</span>
              <span className="font-bold">20</span>
            </h4> */}
          </div>
        </div>
      </div>
      <Link href="/checkout">
        <Button className="ml-auto mt-4" label="Checkout" />
      </Link>
    </main>
  );
}

export default CartPage;
