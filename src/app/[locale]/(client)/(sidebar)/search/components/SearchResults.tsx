"use client";

import React from "react";
import { useAtomValue } from "jotai";
import ProductItem from "../../../components/ProductItem";
import { searchProductsAtom } from "../state";
import { useLocale } from "next-intl";
import Locale from "@udoy/components/Locale/Client";

function SearchResults() {
  const products = useAtomValue(searchProductsAtom);
  const locale = useLocale();

  if (products.length === 0) {
    return (
      <div className="mt-8">
        <h3 className="text-center text-xl font-bold text-muted-foreground">
          <Locale bn="কোন পন্য নেই...">No Products Found</Locale>
        </h3>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 p-4 md:px-8 py-4 duration-150">
      {products.map((product) => (
        <ProductItem product={product} key={product.id} locale={locale} />
      ))}
    </div>
  );
}

export default SearchResults;
