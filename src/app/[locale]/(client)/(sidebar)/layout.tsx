import { ReactElement, Suspense } from "react";
import Sidebar from "../components/Sidebar";
import SyncCart from "../../../../components/SyncCart";
import CartStatus from "../components/CartStatus";

function Layout({ children }: { children: ReactElement }) {
  return (
    <div className="flex ">
      <Suspense fallback={<div>Loading...</div>}>
        <Sidebar />
      </Suspense>
      <div className="flex-1">{children}</div>
      <CartStatus />
    </div>
  );
}

export default Layout;
