import Icon from "@udoy/components/Icon";
import { Link } from "@udoy/i18n/navigation";
import { getPrisma } from "@udoy/utils/db-utils";
import { HomeIcon } from "lucide-react";
import { getLocale } from "next-intl/server";
import React from "react";
import { flattenPathToParent } from "../utils";
import Hide from "@udoy/components/Hide";

async function CategoryBreadcrumb({ categorySlug }: { categorySlug: string }) {
  const locale = await getLocale();
  const isBngla = locale === "bn";
  const pathToParent = await getPrisma().category.findUnique({
    where: { slug: categorySlug },
    select: {
      slug: true,
      nam: true,
      parentCategory: {
        select: {
          slug: true,
          nam: true,
          parentCategory: {
            select: { slug: true, nam: true, parentCategory: true },
          },
        },
      },
    },
  });
  const path = flattenPathToParent(pathToParent as any).reverse();

  return (
    <div className="flex gap-1 items-center bg-background z-40 sticky top-0 duration-200 px-4 md:px-8 py-4">
      <Link href={{ pathname: "/" }} className="">
        <HomeIcon className="h-4 w-4 text-muted-foreground" />
      </Link>

      <Icon icon="chevron-right" className="text-sm text-muted-foreground" />
      {path.map((category, i) => (
        <div key={category.en} className="flex items-center">
          <Link
            className="font-semibold text-sm text-muted-foreground truncate"
            href={{ pathname: `/${category.en}` }}
            style={{ maxWidth: Math.floor(90 / path.length) + "vw" }}
          >
            {isBngla ? category.bn : category.en}
          </Link>
          <Hide open={i < path.length - 1}>
            <Icon
              icon="chevron-right"
              className="text-sm ml-1 text-muted-foreground"
            />
          </Hide>
        </div>
      ))}
    </div>
  );
}

export default CategoryBreadcrumb;
