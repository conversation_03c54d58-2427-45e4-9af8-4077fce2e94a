import { Category } from "@prisma/client";

export function flattenPathToParent(
  category: (Category & { parentCategory?: Category | null }) | null | undefined
): { en: string; bn: string | null }[] {
  if (!category) {
    return [];
  }

  if (!category?.parentCategory) {
    return [{ en: category.slug, bn: category.nam }];
  }
  return [
    { en: category.slug, bn: category.nam },
    ...flattenPathToParent(category.parentCategory),
  ];
}
