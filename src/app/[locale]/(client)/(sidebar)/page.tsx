import PopularProducts from "./components/PopularProducts";
import { setRequestLocale } from "next-intl/server";
import PopularCategories from "./components/PopularCategories";
import { routing } from "@udoy/i18n/routing";

async function Home({ params }: { params: Promise<{ locale: typeof routing.defaultLocale }> }) {
  const { locale } = await params;

  setRequestLocale(locale);

  return (
    <div
      className="flex-1 overflow-y-scroll pb-6"
      style={{ height: "calc(100vh - 64px)" }}
    >
      <PopularCategories />
      <PopularProducts />
    </div>
  );
}

export default Home;
