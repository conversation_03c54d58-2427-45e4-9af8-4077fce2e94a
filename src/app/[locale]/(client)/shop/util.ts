import { z } from "zod";

export const productFormSchema = z.object({
  price: z.coerce.number().min(0, { message: "Price must be greater than 0" }),
  sourcePrice: z.coerce.number().min(0, { message: "Source Price must be greater than 0" }),
  discount: z.coerce.number().min(0, { message: "Discount must be greater than 0" }),
}).refine(data => data.price >= data.sourcePrice, {
  message: "Price cannot be less than Source Price",
  path: ["price"] // This will show the error on the price field
});
