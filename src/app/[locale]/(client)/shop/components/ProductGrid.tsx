"use client";

import React from "react";
import { Search } from "lucide-react";
import { Input } from "@udoy/components/ui/input";
import { useState } from "react";
import ProductItem from "./ProductItem";
import { ProductWithCategory } from "@udoy/utils/types";
import { useLocale } from "next-intl";

function ProductGrid(props: {
  products: Omit<ProductWithCategory, "category">[];
}) {
  const [searchQuery, setSearchQuery] = useState("");
  const [products] = useState(props.products);
  const locale = useLocale();

  const filteredProducts = products.filter((product) => {
    if (!searchQuery) {
      return true;
    }
    return (
      product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.nam?.toLowerCase().includes(searchQuery.toLowerCase())
    );
  });

  return (
    <div className="">
      <div className="flex flex-col sm:flex-row gap-4 mb-4 mt-6">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search products..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-2 sm:gap-4 mt-2 mb-6">
        {filteredProducts.map((product) => (
          <ProductItem locale={locale} key={product.id} product={product} />
        ))}
      </div>
    </div>
  );
}

export default ProductGrid;
