"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Edit } from "lucide-react";
import { useState, useEffect } from "react";

import { Button } from "@udoy/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@udoy/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@udoy/components/ui/form";
import { Input } from "@udoy/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import { Product } from "@prisma/client";
import { withError } from "@udoy/utils/app-error";
import { toast } from "sonner";
import { Separator } from "@udoy/components/ui/separator";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@udoy/components/ui/collapsible";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@udoy/components/ui/sheet";
import { useAtomValue } from "jotai";
import { UnitUtil } from "@udoy/utils/product-unit";
import { useProduct } from "./ProductItem";
import { productFormSchema } from "../util";
import { updateProduct } from "../action";

// Type for our form values
type ProductFormValues = z.infer<typeof productFormSchema>;

export default function ProductForm() {
  const { product, setProduct } = useProduct();
  const [open, setOpen] = useState(false);

  // Initialize the form with default values or existing product data
  const form = useForm<ProductFormValues>({
    resolver: zodResolver(productFormSchema),
    defaultValues: {
      price: product?.price || 0,
      sourcePrice: product?.sourcePrice || 0,
      discount: product?.discount || 0,
    },
  });

  // Reset form when product changes
  useEffect(() => {
    if (product) {
      form.reset({
        price: product.price,
        sourcePrice: product.sourcePrice,
        discount: product.discount,
      });
    }
  }, [product, form]);

  // Handle form submission
  const handleSubmit = async (values: ProductFormValues) => {
    try {
      const updated = await withError(updateProduct(product.id, values));
      if (updated) {
        setProduct(updated);
      }
      setOpen(false);
      toast.success("Product updated");
    } catch (error: any) {
      toast.error(error.message || "Failed to update product");
    }
  };

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button className="w-full">Edit Product</Button>
      </SheetTrigger>
      <SheetContent
        className="w-screen"
        onOpenAutoFocus={(e) => e.preventDefault()}
      >
        <SheetHeader>
          <SheetTitle>Update Product</SheetTitle>
        </SheetHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-4 py-4 flex flex-col flex-1 h-full "
          >
            <FormField
              control={form.control}
              name="price"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Price {"(MRP)"}</FormLabel>
                  <FormControl>
                    <Input type="number" min="0" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="sourcePrice"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Source Price {"(DP)"}</FormLabel>
                  <FormControl>
                    <Input type="number" min="0" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="discount"
              render={({ field }) => (
                <FormItem className="">
                  <FormLabel>Discount</FormLabel>
                  <FormControl>
                    <Input type="number" min="0" max="100" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="flex-1"></div>
            <DialogFooter className="pt-4 gap-2 flex ">
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button type="submit" className="flex-1">
                Save Changes
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </SheetContent>
    </Sheet>
  );
}
