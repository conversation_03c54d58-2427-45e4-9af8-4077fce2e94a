"use client";

import { hideProduct, hideProductShopOwner } from "@udoy/actions/product";
import Hide from "@udoy/components/Hide";
import { Button } from "@udoy/components/ui/button";
import useIsAdmin from "@udoy/hooks/useIsAdmin";
import { withError } from "@udoy/utils/app-error";
import { EyeIcon, EyeOff } from "lucide-react";
import React from "react";
import { toast } from "sonner";
import { Product } from "@prisma/client";
import { useProduct } from "./ProductItem";

function HideProduct({ product }: { product: Product }) {
  const isAdmin = useIsAdmin();
  const { setProduct } = useProduct();

  if (!isAdmin) {
    return null;
  }

  async function handleHideProduct() {
    try {
      const result = await withError(hideProductShopOwner(product.id, !product.hide));
      if (result) {
        setProduct((prev) => ({ ...prev, hide: !prev.hide }));
        toast.success("Product Updated");
      }
    } catch (error: any) {
      toast.error(error?.message || "Failed To Hide Product");
    }
  }

  return (
    <div className="absolute top-2 right-2 flex gap-2 z-10">
      <Button size="icon" variant="outline" onClick={handleHideProduct}>
        <Hide open={!product.hide} fallback={<EyeIcon />}>
          <EyeOff />
        </Hide>
      </Button>
    </div>
  );
}

export default HideProduct;
