import { <PERSON>ieUtil } from "@udoy/utils/cookie-util";
import { getPrisma } from "@udoy/utils/db-utils";
import React from "react";
import { getLocale } from "next-intl/server";
import SyncShop from "../../dashboard/manage/components/SyncShops";
import SyncUnit from "../../dashboard/manage/components/SyncUnit";
import { Card } from "@udoy/components/ui/card";
import { Calendar, Edit, MapPin, SearchIcon } from "lucide-react";
import ProductGrid from "./components/ProductGrid";

async function getData() {
  const userId = await CookieUtil.userId();

  if (!userId) {
    return null;
  }

  const prisma = getPrisma();
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      shop: {
        include: {
          products: {
            orderBy: [
              { hide: "asc" },
              {
                id: "asc",
              },
            ],
            include: {
              images: true,
              unit: true,
            },
          },
        },
      },
    },
  });

  return user?.shop;
}

async function Page() {
  const shop = await getData();

  if (!shop) {
    return <div className="mt-12 text-center place-items-center grid h-full">You don&apos;t have a shop</div>;
  }


  return (
    <SyncShop>
      <SyncUnit>
        <div className="px-4 md:px-8 mt-6">
          <Card className="p-6">
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
              <div>
                <h1 className="text-2xl font-bold">{shop.name}</h1>
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 mt-2 text-muted-foreground">
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 mr-1 -mt-0.5" />
                    <span>{shop.location}</span>
                  </div>
                  <div className="hidden sm:block">•</div>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1 -mt-0.5" />
                    <span>
                      Created {new Date(shop.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </Card>

          <ProductGrid products={shop.products} />
        </div>
      </SyncUnit>
    </SyncShop>
  );
}

export default Page;
