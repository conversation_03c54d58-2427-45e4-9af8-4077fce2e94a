"use server";

import { ActionError } from "@udoy/utils/app-error";
import { CookieUtil } from "@udoy/utils/cookie-util";
import { getPrisma } from "@udoy/utils/db-utils";
import { revalidatePath } from "next/cache";
import { productFormSchema } from "./util";

export async function updateProduct(productId: string, data: any) {
  try {
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const product = await getPrisma().product.findUnique({
      where: { id: productId },
      include: {
        shop: true,
        category: true,
      },
    });

    if (!product) {
      return ActionError("Invalid Product");
    }

    if (userId && product.shop?.ownerId !== userId) {
      return ActionError("You are not the owner of this product");
    }

    const { price, sourcePrice, discount } = productFormSchema.parse(data);
    const prisma = getPrisma();
    const updated = await prisma.product.update({
      where: { id: productId },
      data: {
        price,
        sourcePrice,
        discount,
      },
      include: {
        category: true,
        unit: true,
        images: true,
      },
    });

    if (product) {
      revalidatePath(`/${product.category?.slug}`);
      return updated;
    }

    return false;
  } catch (error) {
    console.log(error);
    return ActionError("Failed To Update Product");
  }
}
