"use client";

import Locale from "@udoy/components/Locale/Client";
import { useCart } from "@udoy/state/selectors";
import { useLocale } from "next-intl";
import React, { Fragment } from "react";
import CartViewItem from "../../components/CartViewItem";
import { Separator } from "@udoy/components/ui/separator";
import Hide from "@udoy/components/Hide";

function CartPreview() {
  const { items, itemsCount } = useCart();
  const locale = useLocale();

  return (
    <div className="rounded shadow overflow-clip">
      <h3 className="flex gap-1 bg-brand px-4 py-2 font-medium ">
        <span>{itemsCount.toLocaleString(locale)}</span>
        <Locale bn="টি পন্য">Items</Locale>
      </h3>

      <div className="px-2 py-2 sm:max-h-[600px] overflow-y-auto scrollbar-thin border rounded-b">
        {items.map(({ product, count }, index) => (
          <Fragment key={product.id}>
            <Hide open={index !== 0}>
              <Separator />
            </Hide>
            <CartViewItem key={product.id} product={product} count={count} />
          </Fragment>
        ))}
      </div>
    </div>
  );
}

export default CartPreview;
