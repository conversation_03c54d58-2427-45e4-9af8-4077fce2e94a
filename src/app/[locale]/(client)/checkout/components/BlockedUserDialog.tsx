"use client";

import React from "react";
import {
  Alert<PERSON>ial<PERSON>,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@udoy/components/ui/alert-dialog";
import { Button } from "@udoy/components/ui/button";
import { ShieldOff } from "lucide-react";
import Locale from "@udoy/components/Locale/Client";

interface BlockedUserDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export function BlockedUserDialog({ isOpen, onClose }: BlockedUserDialogProps) {
  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
            <ShieldOff className="h-8 w-8 text-red-600" />
          </div>
          <AlertDialogTitle className="text-xl font-semibold text-red-600 text-center">
            <Locale bn="অ্যাকাউন্ট ব্লক করা হয়েছে">Account Blocked</Locale>
          </AlertDialogTitle>
          <AlertDialogDescription className="text-center space-y-2">
            <p className="text-base">
              <Locale bn="আপনার অ্যাকাউন্ট সাময়িকভাবে ব্লক করা হয়েছে।">
                Your account has been temporarily blocked.
              </Locale>
            </p>
            <p className="text-sm text-muted-foreground">
              <Locale bn="আপনি এই মুহূর্তে কোনো অর্ডার দিতে পারবেন না। আরও তথ্যের জন্য আমাদের সাথে যোগাযোগ করুন।">
                You cannot place orders at this time. Please contact us for more information.
              </Locale>
            </p>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="justify-center">
          <Button onClick={onClose} className="w-full">
            <Locale bn="বুঝেছি">I Understand</Locale>
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
