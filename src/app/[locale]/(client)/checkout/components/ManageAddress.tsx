"use client";

import Locale from "@udoy/components/Locale/Client";
import { Button } from "@udoy/components/ui/button";
import {
  CheckCircle,
  DeleteIcon,
  Edit,
  LocateIcon,
  MapIcon,
  Plus,
  Trash,
} from "lucide-react";
import React, { Fragment, useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@udoy/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import { Address } from "@prisma/client";
import { store } from "@udoy/state";
import { useSelector } from "@xstate/store/react";
import { useLocale, useTranslations } from "next-intl";
import { createAddress, updateAddress } from "@udoy/actions/delivary-zone";
import Hide from "@udoy/components/Hide";
import { cn } from "@udoy/utils/shadcn";
import { SelectLabel } from "@radix-ui/react-select";
import { createAtom } from "@xstate/store";
import { useForm } from "react-hook-form";
import { AddressInputSchema } from "@udoy/libs/zod-schema";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@udoy/components/ui/form";
import { Input } from "@udoy/components/ui/input";
import { z } from "zod";
import { toast } from "sonner";
import useStatus from "@udoy/hooks/useToastUtil";
import { withError } from "@udoy/utils/app-error";
import Link from "next/link";
import { isValidPlusCode } from "@udoy/utils";

export const addressPopupAtom = createAtom(false);

function ManageAddresses({
  button,
  address,
}: {
  button?: React.ReactNode;
  address?: Address;
}) {
  const locale = useLocale();
  const isBangla = locale === "bn";
  const zones = useSelector(store, (state) => state.context.zones);
  const form = useForm<AddressInputSchema>({
    resolver: zodResolver(AddressInputSchema),
    defaultValues: {
      home: address?.home || "",
      location: address?.location || "",
      name: address?.name || "",
      phone: address?.phone || "",
      zoneId: address?.zoneId || "",
      label: address?.label || "",
    },
  });
  const status = useStatus();
  const isNew = !address;
  const tStatus = useTranslations(
    `manageAddress.${isNew ? "create" : "update"}.status`
  );
  const location = form.watch("location");

  async function handleSubmit(values: AddressInputSchema) {
    try {
      status.loading(tStatus("loading"));
      addressPopupAtom.set(false);
      const result = await withError(
        isNew ? createAddress(values) : updateAddress(address.id, values)
      );
      if (result) {
        store.send({
          type: isNew ? "addNewAddress" : "updateAddress",
          address: result,
        });
      }
      status.success(tStatus("success"));
      form.reset();
    } catch (error: any) {
      status.error(error.message || tStatus("error"));
      addressPopupAtom.set(true);
    }
  }

  if (!button) {
    button = (
      <Button className="rounded-none gap-1">
        <Plus size={18} />
        <span>
          <Locale bn="যোগ করুন">Add New</Locale>
        </span>
      </Button>
    );
  }

  return (
    <Dialog>
      <DialogTrigger asChild>{button}</DialogTrigger>
      <DialogContent
        className="rounded"
        style={{ width: "calc(100vw - 2rem)" }}
      >
        <DialogHeader>
          <DialogTitle className="flex gap-2 items-center">
            <MapIcon />
            <span>
              <Hide
                open={isNew}
                fallback={
                  <Locale bn="ঠিকানা সম্পাদনা করুন">Edit Address</Locale>
                }
              >
                <Locale bn="নতুন ঠিকানা যোগ করুন">Add New Address</Locale>
              </Hide>
            </span>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="block">
            <div className="grid grid-cols-2 gap-3 pt-3 gap-y-4">
              <FormField
                control={form.control}
                name="label"
                render={({ field }) => (
                  <FormItem className="col-span-2">
                    <FormLabel>
                      <Locale bn="ঠিকানার নাম">Address Label</Locale>
                    </FormLabel>
                    <FormControl>
                      <Input
                        required
                        minLength={2}
                        placeholder={
                          isBangla
                            ? "বাড়ি, অফিস, ইত্যাদি..."
                            : "Home, Office, etc."
                        }
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      <Locale bn="গ্রহনকারীর নাম">Receipent Name</Locale>
                    </FormLabel>
                    <FormControl>
                      <Input
                        required
                        placeholder={isBangla ? "করিম আলি" : "Korim Ali"}
                        minLength={3}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      <Locale bn="মোবাইল নম্বর">Mobile Number</Locale>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="tel"
                        placeholder={isBangla ? "০১৭১..." : "0171..."}
                        inputMode="numeric"
                        required
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="home"
                render={({ field }) => (
                  <FormItem className="">
                    <FormLabel>
                      <Locale bn="বাড়ির ঠিকানা">Home Address</Locale>
                    </FormLabel>
                    <FormControl>
                      <Input
                        required
                        minLength={3}
                        placeholder={
                          isBangla
                            ? "বাড়ির নাম, রাস্তার নাম, ইত্যাদি..."
                            : "House no, Road name, etc."
                        }
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="zoneId"
                render={({ field }) => (
                  <FormItem className="">
                    <FormLabel>
                      <Locale bn="ডেলিভারি এলাকা">Delivery Zone</Locale>
                    </FormLabel>
                    <FormControl>
                      <Select
                        required
                        {...field}
                        onValueChange={field.onChange}
                      >
                        <SelectTrigger>
                          <SelectValue
                            placeholder={
                              <Locale bn="--নির্বাচন করুন--">--Select--</Locale>
                            }
                          />
                        </SelectTrigger>
                        <SelectContent>
                          {/* Zones with subzones */}
                          {zones
                            .filter((zone) => zone.subZones.length > 0)
                            .map((zone) => (
                              <SelectGroup key={zone.id}>
                                <SelectLabel className="pl-2">
                                  <Locale bn={zone.nam}>{zone.name}</Locale>
                                </SelectLabel>
                                {zone.subZones.map((subZone) => (
                                  <SelectItem
                                    key={subZone.id}
                                    value={subZone.id}
                                  >
                                    {isBangla ? subZone.nam : subZone.name}
                                  </SelectItem>
                                ))}
                              </SelectGroup>
                            ))}

                          {/* Other villages section */}
                          <SelectGroup>
                            <SelectLabel className="pl-2">
                              <Locale bn="অন্য গ্রামগুলি">
                                Other Villages
                              </Locale>
                            </SelectLabel>
                            {zones
                              .filter((zone) => zone.subZones.length === 0)
                              .map((zone) => (
                                <SelectItem key={zone.id} value={zone.id}>
                                  {isBangla ? zone.nam : zone.name}
                                </SelectItem>
                              ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem className="col-span-2">
                    <FormLabel>
                      <Locale bn="গুগল প্লাস কোড">Google Plus Code</Locale>
                      <span className="ml-2 text-muted-foreground">
                        (<Locale bn="ঐচ্ছিক">Optional</Locale>)
                      </span>
                    </FormLabel>
                    <FormControl className="">
                      <div className="relative">
                        <Input
                          placeholder="WRJ3+JH2"
                          {...field}
                          onChange={(e) => {
                            const value = e.target.value;
                            const code = value
                              .split(" ")
                              .find((part) => isValidPlusCode(part));

                            field.onChange(code || value);
                          }}
                        />
                        <CheckCircle
                          size={18}
                          className={cn(
                            "absolute right-2 top-1/2 -translate-y-1/2 text-green-700 opacity-0 duration-150",
                            isValidPlusCode(location) && "opacity-100"
                          )}
                        />
                      </div>
                    </FormControl>
                    <FormDescription>
                      <Locale
                        bn="ডেলিভারি সহজ করতে আপনার গুগল প্লাস কোড দিন"
                        en="Provide your Google Plus Code for precise location finding"
                      />
                      <span className="ml-1 underline text-blue-700">
                        <Link
                          target="__blank"
                          href={`https://plus.codes/${
                            address?.location || "map"
                          }`}
                        >
                          <Locale bn="আপনারটি খুজুন">Find Your</Locale>
                        </Link>
                      </span>
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <DialogFooter className="grid grid-cols-2 mt-5 gap-2">
              <DialogTrigger asChild>
                <Button variant="outline">
                  <Locale bn="বাতিল করুন">Cancel</Locale>
                </Button>
              </DialogTrigger>
              <DialogTrigger asChild>
                <Button type="submit">
                  <Hide
                    open={isNew}
                    fallback={<Locale bn="আপডেট করুন">Update</Locale>}
                  >
                    <Locale bn="যোগ করুন">Add</Locale>
                  </Hide>
                </Button>
              </DialogTrigger>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

export default ManageAddresses;
