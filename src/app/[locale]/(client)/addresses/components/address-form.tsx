"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { addressSchema, type AddressFormValues } from "../schema";
import { Button } from "@udoy/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@udoy/components/ui/form";
import { Input } from "@udoy/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import { createAddress, updateAddress, getDeliveryZones } from "../actions";
import type { Address, DeliveryZone } from "../types";
import { useEffect } from "react";
import { toast } from "sonner";

interface AddressFormProps {
  existingAddress?: Address & { zone: DeliveryZone };
  onSuccess?: () => void;
}

export function AddressForm({ existingAddress, onSuccess }: AddressFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [zones, setZones] = useState<DeliveryZone[]>([]);
  const [isLoadingZones, setIsLoadingZones] = useState(true);

  const form = useForm<AddressFormValues>({
    resolver: zodResolver(addressSchema),
    defaultValues: existingAddress
      ? {
          label: existingAddress.label,
          name: existingAddress.name,
          home: existingAddress.home,
          phone: existingAddress.phone,
          location: existingAddress.location || "",
          zoneId: existingAddress.zoneId,
        }
      : {
          label: "",
          name: "",
          home: "",
          phone: "",
          location: "",
          zoneId: "",
        },
  });

  useEffect(() => {
    const fetchZones = async () => {
      try {
        setIsLoadingZones(true);
        const data = await getDeliveryZones();
        setZones(data);
      } catch (error) {
        console.error("Failed to fetch delivery zones:", error);
        toast.error("Failed to load delivery zones. Please try again.");
      } finally {
        setIsLoadingZones(false);
      }
    };

    fetchZones();
  }, [toast]);

  const onSubmit = async (data: AddressFormValues) => {
    try {
      setIsSubmitting(true);

      if (existingAddress) {
        await updateAddress(existingAddress.id, data);
        toast.success("Your address has been updated successfully.");
      } else {
        await createAddress(data);
        toast("Your new address has been added successfully.");
        form.reset({
          label: "",
          name: "",
          home: "",
          phone: "",
          location: "",
          zoneId: "",
        });
      }

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Failed to save address:", error);
      toast.error("Failed to save address. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="label"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Address Label</FormLabel>
              <FormControl>
                <Input placeholder="Home, Work, etc." {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Full Name</FormLabel>
              <FormControl>
                <Input placeholder="John Doe" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="home"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Address</FormLabel>
              <FormControl>
                <Input placeholder="123 Main St, Apt 4B" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Phone Number</FormLabel>
              <FormControl>
                <Input placeholder="+****************" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="location"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Location Details (Optional)</FormLabel>
              <FormControl>
                <Input
                  placeholder="Near the park, 2nd floor, etc."
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="zoneId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Delivery Zone</FormLabel>
              <Select
                disabled={isLoadingZones}
                onValueChange={field.onChange}
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a delivery zone" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {isLoadingZones ? (
                    <SelectItem value="loading" disabled>
                      Loading zones...
                    </SelectItem>
                  ) : (
                    zones.map((zone) => (
                      <SelectItem key={zone.id} value={zone.id}>
                        {zone.name}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button
          type="submit"
          className="w-full"
          disabled={isSubmitting || isLoadingZones}
        >
          {isSubmitting
            ? existingAddress
              ? "Updating..."
              : "Adding..."
            : existingAddress
            ? "Update Address"
            : "Add Address"}
        </Button>
      </form>
    </Form>
  );
}
