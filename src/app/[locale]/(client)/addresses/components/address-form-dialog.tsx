"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@udoy/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@udoy/components/ui/dialog"
import { AddressForm } from "./address-form"
import { Edit, Plus } from "lucide-react"
import type { Address, DeliveryZone } from "../types"

interface AddressFormDialogProps {
  existingAddress?: Address & { zone: DeliveryZone }
  button: React.ReactNode
}

export function AddressFormDialog({
  existingAddress,
  button,
}: AddressFormDialogProps) {
  const [open, setOpen] = useState(false)
  const isEditing = !!existingAddress

  const handleSuccess = () => {
    setOpen(false)
    // Optionally trigger a refresh of the address list
    window.location.reload()
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
       {button} 
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Edit Address" : "Add New Address"}</DialogTitle>
          <DialogDescription>
            {isEditing
              ? "Update your delivery address details below."
              : "Add a new delivery address to your account. Fill in all the required information below."}
          </DialogDescription>
        </DialogHeader>
        <div className="mt-4">
          <AddressForm existingAddress={existingAddress} onSuccess={handleSuccess} />
        </div>
      </DialogContent>
    </Dialog>
  )
}
