"use client";

import { useEffect, useState } from "react";
import { AddressCard } from "./address-card";
import { AddressListSkeleton } from "./address-list-skeleton";
import { getAddresses } from "../actions";
import type { Address, DeliveryZone } from "../types";
import { AlertCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@udoy/components/ui/alert";
import { AddressWithZone, ZoneWithSubzones } from "@udoy/utils/types";
import { store } from "@udoy/state";
import { useSelector } from "@xstate/store/react";

export function AddressList(props: {
  addresses: AddressWithZone[];
  zones: ZoneWithSubzones[];
}) {
  const addresses = useSelector(store, (state) => state.context.addresses);
 
  useEffect(() => {
    store.send({
      type: "setAddresses",
      addresses: props.addresses,
    });

    store.send({
      type: "setZones",
      zones: props.zones,
    });
  }, [props.addresses, props.zones]);

  if (addresses.length === 0) {
    return (
      <div className="text-center p-6 border rounded-lg bg-muted/50">
        <p className="text-muted-foreground">No addresses found</p>
        <p className="text-sm text-muted-foreground">
          Add a new address to get started
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {addresses.map((address) => (
        <AddressCard
          key={address.id}
          address={address}
          onDelete={() => {
            // setAddresses(addresses.filter((a) => a.id !== address.id));
          }}
        />
      ))}
    </div>
  );
}
