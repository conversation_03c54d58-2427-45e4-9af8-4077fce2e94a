"use client";

import { useState } from "react";
import type { Address, DeliveryZone } from "../types";
import { <PERSON>, CardContent, CardFooter } from "@udoy/components/ui/card";
import { Button } from "@udoy/components/ui/button";
import { Trash2, MapPin, Phone, Home, Edit } from "lucide-react";
import { Badge } from "@udoy/components/ui/badge";
import { deleteAddress } from "../actions";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@udoy/components/ui/alert-dialog";
import { AddressFormDialog } from "./address-form-dialog";
import ManageAddresses from "../../checkout/components/ManageAddress";
import { useSelector } from "@xstate/store/react";
import { store } from "@udoy/state";
import Hide from "@udoy/components/Hide";
import Locale from "@udoy/components/Locale/Client";
import useStatus from "@udoy/hooks/useToastUtil";
import { withError } from "@udoy/utils/app-error";
import { updateCurrentAddress } from "@udoy/actions/cart";
import { deleteUserAddress } from "@udoy/actions/delivary-zone";

interface AddressCardProps {
  address: Address & { zone: DeliveryZone };
  onDelete: () => void;
}

export function AddressCard({ address, onDelete }: AddressCardProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const cart = useSelector(store, (state) => state.context.cartInfo);
  const status = useStatus();

  // const handleDelete = async () => {
  //   try {
  //     setIsDeleting(true);
  //     await deleteAddress(address.id);
  //     onDelete();
  //   } catch (error) {
  //     console.error("Failed to delete address:", error);
  //   } finally {
  //     setIsDeleting(false);
  //   }
  // };

  async function handleChangeAddress(addressId: string) {
    try {
      status.loading("Updating User Delivery Address...");
      await withError(updateCurrentAddress(addressId));
      status.success("Updated User Address Successfully!");
      store.send({ type: "changeAddress", addressId });
    } catch (error: any) {
      status.error(error.message);
    }
  }

  async function handleDeleteAddress(addressId: string) {
    setIsDeleting(true);
    try {
      status.loading("Deleting User Delivery Address...");
      await withError(deleteUserAddress(addressId));
      status.success("Deleted User Address Successfully!");
      store.send({ type: "deleteAddress", addressId });
    } catch (error: any) {
      status.error(error.message);
    }
    setIsDeleting(false);
  }

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="flex justify-between items-start mb-2">
          <div className="flex items-center gap-2">
            <h3 className="font-medium">{address.label}</h3>
            <Badge variant="outline">{address.zone.name}</Badge>
          </div>
          <div className="">
            <Hide
              open={address.id === cart?.addressId}
              fallback={
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleChangeAddress(address.id)}
                >
                  <Locale bn="ডিফল্ট ঠিকানা করুন">Set as Default</Locale>
                </Button>
              }
            >
              <Badge variant="outline">Default</Badge>
            </Hide>
          </div>
        </div>
        <div className="space-y-2 text-sm text-muted-foreground">
          <div className="flex items-center gap-2">
            <Home className="h-4 w-4" />
            <span>{address.name}</span>
          </div>
          <div className="flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            <span>{address.home}</span>
          </div>
          {address.location && (
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              <span>{address.location}</span>
            </div>
          )}
          <div className="flex items-center gap-2">
            <Phone className="h-4 w-4" />
            <span>{address.phone}</span>
          </div>
        </div>
      </CardContent>
      <CardFooter className="border-t px-6 py-4">
        <div className="flex justify-between w-full">
          <ManageAddresses
            address={address}
            button={
              <Button variant="outline" size="sm">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            }
          />
          <Hide open={address.id !== cart?.addressId}>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" size="sm" disabled={isDeleting}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete Address</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to delete this address? This action
                    cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={() => handleDeleteAddress(address.id)}
                  >
                    {isDeleting ? "Deleting..." : "Delete"}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </Hide>
        </div>
      </CardFooter>
    </Card>
  );
}
