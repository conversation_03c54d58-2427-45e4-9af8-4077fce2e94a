export function AddressListSkeleton() {
  return (
    <div className="space-y-4">
      {[...Array(2)].map((_, i) => (
        <div key={i} className="border rounded-lg p-4 space-y-3">
          <div className="h-5 w-1/3 bg-muted animate-pulse rounded"></div>
          <div className="h-4 w-2/3 bg-muted animate-pulse rounded"></div>
          <div className="h-4 w-1/2 bg-muted animate-pulse rounded"></div>
          <div className="h-4 w-3/4 bg-muted animate-pulse rounded"></div>
        </div>
      ))}
    </div>
  )
}
