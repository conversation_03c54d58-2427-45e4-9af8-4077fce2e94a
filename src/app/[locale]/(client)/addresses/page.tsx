import { Suspense } from "react";
import { AddressList } from "./components/address-list";
import { AddressFormDialog } from "./components/address-form-dialog";
import { Separator } from "@udoy/components/ui/separator";
import { AddressListSkeleton } from "./components/address-list-skeleton";
import { Button } from "@udoy/components/ui/button";
import { Plus } from "lucide-react";
import ManageAddresses from "../checkout/components/ManageAddress";
import { CookieUtil } from "@udoy/utils/cookie-util";
import { getPrisma } from "@udoy/utils/db-utils";

async function getData() {
  const userId = await CookieUtil.userId();

  if (!userId) {
    return { addresses: [], zones: [] };
  }

  const prisma = getPrisma();
  const addresses = await prisma.address.findMany({
    where: {
      userId,
    },
    include: {
      zone: true,
    },
  });

  const zones = await prisma.deliveryZone.findMany({
    where: {
      parentZone: {
        slug: "mollahat",
      },
    },
    include: {
      subZones: true,
    },
  });

  const cart = await prisma.cart.findUnique({
    where: {
      userId,
    },
  });

  return { addresses, zones, currentAddress: cart?.addressId };
}

export default async function AddressesPage() {
  const { addresses, zones } = await getData();

  return (
    <div
      className="container max-w-2xl py-6 space-y-6 px-4 overflow-y-auto"
      style={{ height: "calc(100vh - 64px)" }}
    >
      <div className="flex justify-between sm:items-center flex-col gap-2 sm:flex-row items-start">
        <div className="text-left">
          <h1 className="text-2xl font-bold tracking-tight">
            Manage Addresses
          </h1>
          <p className="text-muted-foreground">
            Add and manage your delivery addresses
          </p>
        </div>
        <ManageAddresses
          button={
            <Button className="w-full sm:w-auto">
              <Plus className="h-4 w-4 mr-2" />
              Add Address
            </Button>
          }
        />
      </div>
      <Separator />
      <AddressList addresses={addresses} zones={zones} />
    </div>
  );
}
