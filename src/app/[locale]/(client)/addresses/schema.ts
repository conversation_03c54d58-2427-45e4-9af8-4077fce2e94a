import { z } from "zod"

export const addressSchema = z.object({
  label: z.string().min(1, "Address label is required"),
  name: z.string().min(1, "Name is required"),
  home: z.string().min(1, "Address is required"),
  phone: z.string().min(1, "Phone number is required"),
  location: z.string().optional(),
  zoneId: z.string().min(1, "Delivery zone is required"),
})

export type AddressFormValues = z.infer<typeof addressSchema>
