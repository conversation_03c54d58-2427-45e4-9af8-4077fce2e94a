import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger,
} from "@udoy/components/ui/tabs";
import { AssignedOrdersTab } from "./components/assigned-orders-tab";
import { DeliveringOrdersTab } from "./components/delivering-orders-tab";
import { UnassignedOrdersTab } from "./components/unassigned-orders-tab";
import { getPrisma } from "@udoy/utils/db-utils";
import { OrderStatus } from "@prisma/client";
import { CookieUtil } from "@udoy/utils/cookie-util";
import TabWithState from "./components/TabWithState";
import Button from "@udoy/components/Button";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import HeaderActions from "../(client)/components/HeaderActions";

export default async function Home() {
  // Fetch orders from database
  const prisma = getPrisma();
  const userId = await CookieUtil.userId();

  const assignedOrders = await prisma.order.findMany({
    where: {
      status: OrderStatus.CONFIRMED,
      deliveryManId: userId,
    },
    include: {
      buyer: true,
      address: {
        include: {
          zone: true,
        },
      },
      deliveryMan: true,
      orderItems: {
        include: {
          product: {
            include: {
              images: true,
            },
          },
        },
      },
    },
    orderBy: {
      createdAt: "desc",
    },
    take: 20,
  });

  const readyOrders = await prisma.order.findMany({
    where: {
      status: {
        in: [OrderStatus.PACKED, OrderStatus.SHIPPING],
      },
      deliveryManId: userId,
    },
    include: {
      buyer: true,
      address: {
        include: {
          zone: true,
        },
      },
      deliveryMan: true,
      orderItems: {
        include: {
          product: {
            include: {
              images: true,
            },
          },
        },
      },
    },
    orderBy: [
      { status: "desc" },
      {
        address: {
          zone: {
            name: "asc",
          },
        },
      },
    ],
    take: 20,
  });

  const unassignedOrders = await prisma.order.findMany({
    where: {
      status: OrderStatus.PENDING,
    },
    include: {
      buyer: true,
      address: {
        include: {
          zone: true,
        },
      },
      deliveryMan: true,
      orderItems: {
        include: {
          product: {
            include: {
              images: true,
            },
          },
        },
      },
    },
    orderBy: {
      createdAt: "desc",
    },
    take: 20,
  });

  return (
    <main className="container mx-auto px-4 py-6 max-w-md">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center ">
          <Link href="/">
            <Button variant="secondary" size="icon" className="mr-2">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Deliver</h1>
        </div>
        <HeaderActions />
      </div>

      <TabWithState>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="assigned">Assigned</TabsTrigger>
          <TabsTrigger value="ready">Ready</TabsTrigger>
          <TabsTrigger value="unassigned">Unassigned</TabsTrigger>
        </TabsList>

        <TabsContent value="assigned" className="mt-6">
          <AssignedOrdersTab orders={assignedOrders} />
        </TabsContent>

        <TabsContent value="ready" className="mt-6">
          <DeliveringOrdersTab orders={readyOrders} />
        </TabsContent>

        <TabsContent value="unassigned" className="mt-6">
          <UnassignedOrdersTab orders={unassignedOrders} />
        </TabsContent>
      </TabWithState>
    </main>
  );
}
