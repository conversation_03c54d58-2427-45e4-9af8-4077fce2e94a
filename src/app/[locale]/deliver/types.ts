import { Prisma } from "@prisma/client";

export type OrderWithItems = Prisma.OrderGetPayload<{
  include: {
    orderItems: {
      include: {
        product: {
          include: {
            images: true;
            unit: true;
            shop: true;
          };
        };
      };
    };
    buyer: true;
    address: {
      include: {
        zone: true;
      };
    };
    deliveryMan: true;
  };
}>;
