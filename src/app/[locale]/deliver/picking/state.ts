import { OrderWithItems } from "../types";
import { atom } from "jotai";
import {
  groupByCustomer,
  groupByProduct,
  groupByShop,
  GroupedItems,
} from "./utils";
import { GroupByFilter } from "./type";

export const ordersAtom = atom<OrderWithItems[]>([]);

export const groupByFilterAtom = atom<GroupByFilter>("none");

export const orderItemsAtom = atom((get) => {
  const orders = get(ordersAtom);
  return orders.map((order) => order.orderItems).flat();
});

// Add a setter atom to update the picked state of an item
export const updatePickStateAtom = atom(null, (get, set, itemId: number) => {
  const orders = get(ordersAtom);
  const updatedOrders = orders.map((order) => ({
    ...order,
    orderItems: order.orderItems.map((item) =>
      item.id === itemId ? { ...item, picked: !item.picked } : item
    ),
  }));
  set(ordersAtom, updatedOrders);
});

export const pickStatAtom = atom((get) => {
  const items = get(orderItemsAtom);
  let totalPrice = 0;
  let pickedPrice = 0;
  let pickedItems = 0;

  for (const item of items) {
    totalPrice += item.product.sourcePrice * item.quantity;
    if (item.picked) {
      pickedPrice += item.product.sourcePrice * item.quantity;
      pickedItems++;
    }
  }

  const totalItems = items.length;
  const progress = totalItems > 0 ? (pickedItems / totalItems) * 100 : 0;

  return {
    totalPrice,
    pickedItems,
    totalItems,
    progress,
    pickedPrice,
    isAllPicked: totalItems === pickedItems,
  };
});

export const orderStatAtom = atom((get) => {
  const orders = get(ordersAtom);

  return {
    totalOrders: orders.length,
    isMultiOrder: orders.length > 1,
    orderId: orders[0].id,
  };
});

export const groupedAtom = atom<GroupedItems[]>((get) => {
  const orders = get(ordersAtom);
  const groupBy = get(groupByFilterAtom);

  if (groupBy === "shop") {
    return groupByShop(orders);
  }

  if (groupBy === "productId") {
    return groupByProduct(orders);
  }

  if (groupBy === "customer") {
    return groupByCustomer(orders);
  }

  return [
    {
      groupName: "All Items",
      items: orders.map((order) => order.orderItems).flat(),
    },
  ];
});

export const settingsOpenAtom = atom(false);
