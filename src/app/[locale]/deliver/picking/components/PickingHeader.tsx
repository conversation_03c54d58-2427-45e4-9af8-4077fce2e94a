"use client";

import { But<PERSON> } from "@udoy/components/ui/button";
import { Progress } from "@udoy/components/ui/progress";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { useAtomValue } from "jotai";
import { orderStatAtom, pickStatAtom } from "../state";
import Hide from "@udoy/components/Hide";
import { PickingSettings } from "./PickingSettings";

export function PickingHeader() {
  const router = useRouter();
  const { totalOrders, isMultiOrder, orderId } = useAtomValue(orderStatAtom);
  const { pickedItems, totalItems, progress } = useAtomValue(pickStatAtom);

  return (
    <div className="sticky top-0 z-10 bg-background pb-4 pt-3">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push("/deliver")}
            className="mr-2"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-xl font-bold">
            <Hide open={isMultiOrder} fallback={<span>Order #{orderId}</span>}>
              <span>Combined Picking ({totalOrders})</span>
            </Hide>
          </h1>
        </div>
        <PickingSettings />
      </div>

      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span>
            Picked {pickedItems} of {totalItems} items
          </span>
          <span>{Math.round(progress)}%</span>
        </div>
        <Progress value={progress} className="h-2" />
      </div>
    </div>
  );
}