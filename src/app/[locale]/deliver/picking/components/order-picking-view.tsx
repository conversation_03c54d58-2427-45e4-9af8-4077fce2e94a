"use client";

import { useState, useMemo } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent } from "@udoy/components/ui/card";
import { Button } from "@udoy/components/ui/button";
import { Checkbox } from "@udoy/components/ui/checkbox";
import { Badge } from "@udoy/components/ui/badge";
import { Progress } from "@udoy/components/ui/progress";
import { MapPin, Phone, ArrowLeft, Check, Settings } from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  Sheet<PERSON>itle,
  SheetTrigger,
  SheetFooter,
} from "@udoy/components/ui/sheet";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import { ScrollArea } from "@udoy/components/ui/scroll-area";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@udoy/components/ui/tabs";
import { OrderWithItems } from "../../types";
import Image from "next/image";
import { withError } from "@udoy/utils/app-error";
import { toast } from "sonner";
import { useAtom, useAtomValue } from "jotai";
import {
  groupByFilterAtom,
  groupedAtom,
  ordersAtom,
  orderStatAtom,
  pickStatAtom,
  settingsOpenAtom,
} from "../state";
import { GroupByFilter } from "../type";
import Hide from "@udoy/components/Hide";
import { pickOrderItem } from "../action";

interface OrderPickingViewProps {
  orders: OrderWithItems[];
  orderIds: number[];
}

function ProductsList() {
  const groupedItems = useAtomValue(groupedAtom);
  return (
    <div className="flex-1 mb-20">
      {groupedItems.map(({ groupName, items }) => (
        <div key={groupName} className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-medium text-sm">
              {groupName} ({items.length})
            </h3>
            <Button
              variant="outline"
              size="sm"
              // onClick={() => selectAllInGroup(groupName)}
              className="h-7 text-xs"
            >
              Select All
            </Button>
          </div>
          <div className="space-y-3">
            {items.map((item) => (
              <Card
                key={item.id}
                className={`transition-colors ${
                  item.picked ? "bg-muted/50 border-muted" : ""
                }`}
              >
                <CardContent className="p-4 flex items-center">
                  <Checkbox
                    checked={item.picked}
                    // onCheckedChange={() =>
                    //   handleToggleItem(item.id, item.picked)
                    // }
                    className="h-5 w-5 mr-4 flex-shrink-0"
                  />
                  <div className="flex items-center flex-1">
                    {item.product?.images?.[0]?.url && (
                      <div className="h-12 w-12 rounded-md overflow-hidden mr-3 flex-shrink-0">
                        <Image
                          src={item.product?.images?.[0]?.url}
                          alt={item.product.name}
                          width={48}
                          height={48}
                          className="h-full w-full object-cover"
                        />
                      </div>
                    )}
                    <div className="flex-1">
                      <h4
                        className={`font-medium ${
                          item.picked
                            ? "line-through text-muted-foreground"
                            : ""
                        }`}
                      >
                        {item.product.name}
                      </h4>
                      <div className="flex items-center justify-between mt-1">
                        <p className="text-sm text-muted-foreground">
                          {item.quantity} {item.product.unit.full}
                        </p>
                        <p className="text-sm font-medium">
                          ৳ {item.price * item.quantity}
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}

function PickingFooter() {
  const { totalPrice, pickedItems, totalItems, pickedPrice, isAllPicked } =
    useAtomValue(pickStatAtom);

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-background border-t p-4">
      <div className="container mx-auto max-w-md">
        <div className="flex justify-between items-center mb-3">
          <span className="text-sm text-muted-foreground">Total Amount</span>
          <span className="font-medium">৳{totalPrice}</span>
        </div>
        <div className="flex justify-between items-center mb-4">
          <span className="text-sm text-muted-foreground">Picked Items</span>
          <span className="font-medium">৳{pickedPrice}</span>
        </div>
        <Button
          // onClick={handleComplete}
          className="w-full"
          size="lg"
          disabled={!isAllPicked}
        >
          {isAllPicked ? (
            <>
              <Check className="mr-2 h-5 w-5" /> Complete Picking
            </>
          ) : (
            `Pick ${totalItems - pickedItems} more items`
          )}
        </Button>
      </div>
    </div>
  );
}

export function CustomersView() {
  const orders = useAtomValue(ordersAtom);

  return (
    <ScrollArea className="h-[calc(100vh-14rem)] pr-4">
      {orders.map((order, index) => (
        <Card key={order.id} className={index > 0 ? "mt-4" : ""}>
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <Badge variant="outline" className="mr-2">
                  #{order.id}
                </Badge>
                <h3 className="font-medium">{order.buyer.name}</h3>
              </div>
              {/* {orders.length > 1 && (
                <Button
                  variant={activeCustomer === order.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => {
                    setActiveCustomer(order.id);
                    if (groupBy === "customer") {
                      setGroupBy("customer");
                    }
                  }}
                >
                  View
                </Button>
              )} */}
            </div>

            <div className="space-y-2 mt-3 text-sm">
              <div className="flex items-start">
                <MapPin className="h-4 w-4 mr-2 mt-0.5 text-muted-foreground" />
                <div>
                  <p>{order.address?.home}</p>
                  <p className="text-muted-foreground">
                    {order.address?.zone?.name}
                  </p>
                </div>
              </div>
              {order.buyer.phone && (
                <div className="flex items-center">
                  <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                  <p>{order.buyer.phone}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </ScrollArea>
  );
}

function PickingSettings() {
  const [open, setOpen] = useAtom(settingsOpenAtom);
  const [groupBy, setGroupBy] = useAtom(groupByFilterAtom);

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button variant="outline" size="icon">
          <Settings className="h-5 w-5" />
        </Button>
      </SheetTrigger>
      <SheetContent>
        <SheetHeader>
          <SheetTitle>Picking Settings</SheetTitle>
        </SheetHeader>

        <Tabs defaultValue="settings" className="mt-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="settings">Settings</TabsTrigger>
            <TabsTrigger value="customers">Customers</TabsTrigger>
          </TabsList>

          <TabsContent value="customers" className="mt-4">
            <CustomersView />
          </TabsContent>

          <TabsContent value="settings" className="mt-4 space-y-4">
            <div>
              <h3 className="text-sm font-medium mb-2">Group Items By</h3>
              <Select
                value={groupBy}
                onValueChange={(value) => {
                  setGroupBy(value as GroupByFilter);
                  if (window.innerWidth < 640) {
                    setOpen(false);
                  }
                }}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select grouping" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">No Grouping</SelectItem>
                  <SelectItem value="shop">Group by Shop</SelectItem>
                  <SelectItem value="productId">Group by Product ID</SelectItem>
                  <SelectItem value="customer">Group by Customer</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </TabsContent>
        </Tabs>

        <SheetFooter className="mt-6">
          <Button onClick={() => setOpen(false)}>Done</Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}

function PickingHeader() {
  const router = useRouter();
  const { totalOrders, isMultiOrder, orderId } = useAtomValue(orderStatAtom);
  const { pickedItems, totalItems, progress } = useAtomValue(pickStatAtom);

  return (
    <div className="sticky top-0 z-10 bg-background pb-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push("/deliver")}
            className="mr-2"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-xl font-bold">
            <Hide open={isMultiOrder} fallback={<span>Order #{orderId}</span>}>
              <span>Combined Picking ({totalOrders})</span>
            </Hide>
          </h1>
        </div>
        <PickingSettings />
      </div>

      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span>
            Picked {pickedItems} of {totalItems} items
          </span>
          <span>{Math.round(progress)}%</span>
        </div>
        <Progress value={progress} className="h-2" />
      </div>
    </div>
  );
}

export function OrderPickingView() {
  const router = useRouter();

  // Transform order items into the format needed for the UI
  // const initialItems = useMemo(() => {
  //   const items: OrderWithItems[] = [];

  //   // If multiple orders, combine and aggregate items
  //   if (orders.length > 1) {
  //     return orders.map((order) => order.orderItems).flat();
  //     // const combinedItems: { [key: string]: OrderWithItems } = {};

  //     // orders.forEach((order) => {
  //     //   order.orderItems.forEach((orderItem) => {
  //     //     const product = orderItem.product;
  //     //     const key = `${product.id}-${(product as any).unit?.name || "piece"}`;

  //     //     if (combinedItems[key]) {
  //     //       combinedItems[key].quantity += orderItem.quantity;
  //     //       combinedItems[key].orderIds = [
  //     //         ...(combinedItems[key].orderIds || []),
  //     //         order.id,
  //     //       ];
  //     //     } else {
  //     //       combinedItems[key] = {
  //     //         id: itemId++,
  //     //         productId: product.id,
  //     //         productName: product.name,
  //     //         price: orderItem.price,
  //     //         quantity: orderItem.quantity,
  //     //         unit: (product as any).unit?.name || "piece",
  //     //         shopId: (product as any).shop?.id || "",
  //     //         shopName: (product as any).shop?.name || "Unknown Shop",
  //     //         imageUrl: (product as any).images[0]?.url,
  //     //         picked: false,
  //     //         orderIds: [order.id],
  //     //       };
  //     //     }
  //     //   });
  //     // });

  //     // return Object.values(combinedItems);
  //   }

  //   // Single order
  //   const order = orders[0];
  //   return order.orderItems || [];
  // }, [orders]);

  // const [activeCustomer, setActiveCustomer] = useState<number>(orderIds[0]);
  // const [settingsSheetOpen, setSettingsSheetOpen] = useState(false);

  // const totalItems = items.length;
  // const pickedItems = items.filter((item) => item.picked).length;
  // const progress = totalItems > 0 ? (pickedItems / totalItems) * 100 : 0;

  // const totalAmount = useMemo(() => {
  //   return orders.reduce((sum, order) => {
  //     const orderTotal = order.orderItems.reduce(
  //       (orderSum, item) => orderSum + item.price * item.quantity,
  //       0
  //     );
  //     return sum + orderTotal;
  //   }, 0);
  // }, [orders]);

  // const pickedItemsTotal = useMemo(() => {
  //   return items
  //     .filter((item) => item.picked)
  //     .reduce((sum, item) => sum + item.price * item.quantity, 0);
  // }, [items]);

  // Get shop names for grouping
  // const shopNames = useMemo(() => {
  //   const shops: { [key: string]: string } = {};
  //   items.forEach((item) => {
  //     if (item.shopId) {
  //       shops[item.shopId] = item.shopName;
  //     }
  //   });
  //   return shops;
  // }, [items]);

  // Group items based on selected grouping
  // const groupedItems = useMemo(() => {
  //   if (groupBy === "none") {
  //     return { "All Items": items };
  //   }

  //   if (groupBy === "shop") {
  //     const grouped: { [key: string]: OrderWithItems["orderItems"] } = {};
  //     items.forEach((item) => {
  //       const shopName = (item.product as any)?.shopName || "Unknown Shop";
  //       if (!grouped[shopName]) {
  //         grouped[shopName] = [];
  //       }
  //       grouped[shopName].push(item);
  //     });
  //     return grouped;
  //   }

  //   if (groupBy === "productId") {
  //     const grouped: { [key: string]: OrderWithItems["orderItems"] } = {};
  //     items.forEach((item) => {
  //       if (!grouped[item.productId]) {
  //         grouped[item.productId] = [];
  //       }
  //       grouped[item.productId].push(item);
  //     });
  //     return grouped;
  //   }

  //   if (groupBy === "customer") {
  //     if (orders.length === 1) {
  //       return { [orders[0].buyer.name]: items };
  //     }

  //     const grouped: { [key: string]: OrderWithItems["orderItems"] } = {};
  //     orders.forEach((order) => {
  //       const customerName = order.buyer.name;
  //       grouped[customerName] = items.filter(
  //         (item) => item.orderId === order.id
  //       );
  //     });
  //     return grouped;
  //   }

  //   return { "All Items": items };
  // }, [groupBy, items, orders, activeCustomer]);

  // console.log({ groupedItems });

  function updatePickState(itemId: number) {
    // setItems((prev) =>
    //   prev.map((item) =>
    //     item.id === itemId ? { ...item, picked: !item.picked } : item
    //   )
    // );
  }

 

  const handleComplete = () => {
    // In a real app, you would save the picked status to the database
    router.push("/deliver");
  };

  const selectAllInGroup = (groupName: string) => {
    // setItems((prev) =>
    //   prev.map((item) => {
    //     // Check if this item belongs to the group
    //     const belongsToGroup =
    //       (groupBy === "shop" && item.shopName === groupName) ||
    //       (groupBy === "productId" && item.productId === groupName) ||
    //       (groupBy === "customer" &&
    //         ((orders.length === 1 && groupName === orders[0].buyer.name) ||
    //           (item.orderIds?.includes(activeCustomer) &&
    //             groupName ===
    //               orders.find((o) => o.id === activeCustomer)?.buyer.name))) ||
    //       (groupBy === "none" && groupName === "All Items");
    //     if (belongsToGroup) {
    //       return { ...item, picked: true };
    //     }
    //     return item;
    //   })
    // );
  };

  // const isAllPicked = pickedItems === totalItems && totalItems > 0;

  return (
    <div className="flex flex-col min-h-[calc(100vh-2rem)]">
      {/* Header */}
      <PickingHeader />
      {/* Item List */}
      <ProductsList />
      {/* Footer */}
      <PickingFooter />
    </div>
  );
}
