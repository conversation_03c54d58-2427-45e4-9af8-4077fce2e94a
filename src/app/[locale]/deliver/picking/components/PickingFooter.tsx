"use client";

import { But<PERSON> } from "@udoy/components/ui/button";
import { Check } from "lucide-react";
import { useAtomValue } from "jotai";
import { pickStatAtom } from "../state";
import Hide from "@udoy/components/Hide";
import { completePicking } from "../action";
import { withError } from "@udoy/utils/app-error";
import { toast } from "sonner";
import { orderStatAtom } from "../state";
import { useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import Locale from "@udoy/components/Locale/Client";

export function PickingFooter() {
  const { totalPrice, pickedItems, totalItems, pickedPrice, isAllPicked } =
    useAtomValue(pickStatAtom);
  const { orderId } = useAtomValue(orderStatAtom);
  const router = useRouter();
  const locale = useLocale();

  async function handleComplete() {
    try {
      const result = await withError(completePicking(orderId));
      if (result) {
        router.push("/deliver");
        return toast.success("Picking completed");
      }

      toast.error("Failed to complete picking");
    } catch (error: any) {
      toast.error(error?.message || "Something went wrong!");
    }
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-background border-t py-4">
      <div className="mx-auto max-w-md px-4">
        <div className="flex justify-between items-center">
          <span className="text-sm text-muted-foreground">
            <Locale bn="মোট টাকা">Total Amount</Locale>
          </span>
          <span className="font-medium">
            ৳{totalPrice.toLocaleString(locale)}
          </span>
        </div>
        <div className="flex justify-between items-center mb-4">
          <span className="text-sm text-muted-foreground">
            <Locale bn="নেওয়া হয়েছে">Picked Items</Locale>
          </span>
          <span className="font-medium">
            ৳{pickedPrice.toLocaleString(locale)}
          </span>
        </div>
        <Button
          onClick={handleComplete}
          className="w-full"
          size="lg"
          disabled={!isAllPicked}
        >
          <Hide
            open={isAllPicked}
            fallback={<>Pick {totalItems - pickedItems} more items</>}
          >
            <Check className="mr-2 h-5 w-5" /> Complete Picking
          </Hide>
        </Button>
      </div>
    </div>
  );
}
