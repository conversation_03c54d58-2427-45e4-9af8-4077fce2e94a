"use client";

import { <PERSON><PERSON> } from "@udoy/components/ui/button";
import { Settings } from "lucide-react";
import {
  Sheet,
  Sheet<PERSON>ontent,
  Sheet<PERSON>eader,
  Sheet<PERSON>itle,
  SheetTrigger,
  SheetFooter,
} from "@udoy/components/ui/sheet";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@udoy/components/ui/tabs";
import { useAtom } from "jotai";
import { groupByFilterAtom, settingsOpenAtom } from "../state";
import { GroupByFilter } from "../type";
import { CustomersView } from "./CustomersView";

export function PickingSettings() {
  const [open, setOpen] = useAtom(settingsOpenAtom);
  const [groupBy, setGroupBy] = useAtom(groupByFilterAtom);

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button variant="outline" size="icon">
          <Settings className="h-5 w-5" />
        </Button>
      </SheetTrigger>
      <SheetContent>
        <SheetHeader>
          <SheetTitle>Picking Settings</SheetTitle>
        </SheetHeader>

        <Tabs defaultValue="settings" className="mt-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="settings">Settings</TabsTrigger>
            <TabsTrigger value="customers">Customers</TabsTrigger>
          </TabsList>

          <TabsContent value="customers" className="mt-4">
            <CustomersView />
          </TabsContent>

          <TabsContent value="settings" className="mt-4 space-y-4">
            <div>
              <h3 className="text-sm font-medium mb-2">Group Items By</h3>
              <Select
                value={groupBy}
                onValueChange={(value) => {
                  setGroupBy(value as GroupByFilter);
                  if (window.innerWidth < 640) {
                    setOpen(false);
                  }
                }}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select grouping" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">No Grouping</SelectItem>
                  <SelectItem value="shop">Group by Shop</SelectItem>
                  <SelectItem value="productId">Group by Product ID</SelectItem>
                  <SelectItem value="customer">Group by Customer</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </TabsContent>
        </Tabs>

        <SheetFooter className="mt-6">
          <Button onClick={() => setOpen(false)}>Done</Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}