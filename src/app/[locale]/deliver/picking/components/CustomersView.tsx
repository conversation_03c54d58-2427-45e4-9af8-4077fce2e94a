"use client";

import { Card, CardContent } from "@udoy/components/ui/card";
import { Badge } from "@udoy/components/ui/badge";
import { MapPin, Phone } from "lucide-react";
import { ScrollArea } from "@udoy/components/ui/scroll-area";
import { useAtomValue } from "jotai";
import { ordersAtom } from "../state";

export function CustomersView() {
  const orders = useAtomValue(ordersAtom);

  return (
    <ScrollArea className="h-[calc(100vh-14rem)] pr-4">
      {orders.map((order, index) => (
        <Card key={order.id} className={index > 0 ? "mt-4" : ""}>
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <Badge variant="outline" className="mr-2">
                  #{order.id}
                </Badge>
                <h3 className="font-medium">{order.buyer.name}</h3>
              </div>
              {/* {orders.length > 1 && (
                <Button
                  variant={activeCustomer === order.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => {
                    setActiveCustomer(order.id);
                    if (groupBy === "customer") {
                      setGroupBy("customer");
                    }
                  }}
                >
                  View
                </Button>
              )} */}
            </div>

            <div className="space-y-2 mt-3 text-sm">
              <div className="flex items-start">
                <MapPin className="h-4 w-4 mr-2 mt-0.5 text-muted-foreground" />
                <div>
                  <p>{order.address?.home}</p>
                  <p className="text-muted-foreground">
                    {order.address?.zone?.name}
                  </p>
                </div>
              </div>
              {order.buyer.phone && (
                <div className="flex items-center">
                  <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                  <p>{order.buyer.phone}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </ScrollArea>
  );
}