"use client";

import { useRouter } from "next/navigation";
import { OrderPickingView } from "./order-picking-view";
import { OrderWithItems } from "../../types";
import { useHydrateAtoms } from "jotai/utils";
import { ordersAtom } from "../state";
import { PickingHeader } from "./PickingHeader";
import { ProductsList } from "./ProductsList";
import { PickingFooter } from "./PickingFooter";

interface PickingPageClientProps {
  orders: OrderWithItems[];
}

export function PickingPageClient({ orders }: PickingPageClientProps) {
  useHydrateAtoms([[ordersAtom, orders]]);

  return (
    <main className="container mx-auto px-4 max-w-md">
      <div className="flex flex-col min-h-[calc(100vh-2rem)]">
        {/* Header */}
        <PickingHeader />
        {/* Item List */}
        <ProductsList />
        {/* Footer */}
        <PickingFooter />
      </div>
    </main>
  );
}
