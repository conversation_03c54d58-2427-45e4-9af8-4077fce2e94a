"use client";

import { Card, CardContent } from "@udoy/components/ui/card";
import { Button } from "@udoy/components/ui/button";
import { Checkbox } from "@udoy/components/ui/checkbox";
import Image from "next/image";
import { useAtomValue, useAtom, useSetAtom } from "jotai";
import { groupedAtom, updatePickStateAtom } from "../state";
import { pickOrderItem } from "../action";
import { withError } from "@udoy/utils/app-error";
import { toast } from "sonner";
import { vibrate } from "@udoy/utils";
import Locale from "@udoy/components/Locale/Client";
import { UnitUtil } from "@udoy/utils/product-unit";
import { useLocale } from "next-intl";
import Hide from "@udoy/components/Hide";

export function ProductsList() {
  const groupedItems = useAtomValue(groupedAtom);
  const togglePickState = useSetAtom(updatePickStateAtom);
  const locale = useLocale();

  const handleToggleItem = async (itemId: number, unpick: boolean) => {
    try {
      // const unpick = items.find((item) => item.id === itemId)?.picked;
      togglePickState(itemId);
      vibrate();
      const result = await withError(pickOrderItem(itemId, unpick));
      if (result) {
        return toast.success(unpick ? "Unpicked" : "Picked");
      }

      togglePickState(itemId);
      toast.error(`Failed to ${unpick ? "unpick" : "pick"} item`);
    } catch (error) {
      toast.error("Something went wrong!");
    }
  };

  return (
    <div className="flex-1 mb-40">
      {groupedItems.map(({ groupName, items }) => (
        <div key={groupName} className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-medium text-sm">
              {groupName} ({items.length})
            </h3>
            <Button
              variant="outline"
              size="sm"
              // onClick={() => selectAllInGroup(groupName)}
              className="h-7 text-xs"
            >
              Select All
            </Button>
          </div>
          <div className="space-y-3">
            {items.map((item) => (
              <Card
                key={item.id}
                className={`transition-colors ${
                  item.picked ? "bg-muted/50 border-muted" : ""
                }`}
              >
                <CardContent className="p-4 flex items-center">
                  <Checkbox
                    checked={item.picked}
                    onCheckedChange={() =>
                      handleToggleItem(item.id, item.picked)
                    }
                    className="h-5 w-5 mr-4 flex-shrink-0"
                  />
                  <div className="flex items-center flex-1">
                    {item.product?.images?.[0]?.url && (
                      <div className="h-12 w-12 rounded-md overflow-hidden mr-3 flex-shrink-0">
                        <Image
                          src={item.product?.images?.[0]?.url}
                          alt={item.product.name}
                          width={48}
                          height={48}
                          className="h-full w-full object-cover"
                        />
                      </div>
                    )}
                    <div className="flex-1">
                      <h4
                        className={`font-medium ${
                          item.picked
                            ? "line-through text-muted-foreground"
                            : ""
                        }`}
                      >
                        <Locale bn={item.product.nam}>
                          {item.product.name}
                        </Locale>
                      </h4>
                      <div className="flex items-center justify-between mt-1">
                        <p className="text-sm text-muted-foreground">
                          {UnitUtil.getAmountUnit(
                            item.product.amount * item.quantity,
                            item.product.unit,
                            locale
                          )}{" "}
                          <Hide open={item.quantity > 1}>
                            x {item.quantity.toLocaleString(locale)}
                          </Hide>
                        </p>
                        <div className="flex gap-1">
                          <p className="text-sm font-medium text-muted-foreground">
                            ৳ {item.product.sourcePrice.toLocaleString(locale)}
                          </p>
                          <Hide open={item.quantity > 1}>
                            <p className="text-sm text-muted-foreground">|</p>
                            <p className="text-sm font-medium">
                              ৳{" "}
                              {(
                                item.product.sourcePrice * item.quantity
                              ).toLocaleString(locale)}
                            </p>
                          </Hide>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}
