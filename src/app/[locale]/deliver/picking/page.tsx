import { Suspense } from "react";
import { PickingPageClient } from "./components/PickingClient";
import { getPrisma } from "@udoy/utils/db-utils";
import { OrderWithItems } from "../types";
import { notFound } from "next/navigation";
import { Provider } from "jotai";
import Loading from "./loading";

interface PickingPageProps {
  searchParams: Promise<{ orders?: string }>;
}

export default async function PickingPage({ searchParams }: PickingPageProps) {
  const { orders: orderIdsParam } = await searchParams;

  if (!orderIdsParam) {
    notFound();
  }

  const orderIds = orderIdsParam.split(",").map((id) => parseInt(id, 10));

  // Fetch orders from database
  const prisma = getPrisma();

  const orders = (await prisma.order.findMany({
    where: {
      id: {
        in: orderIds,
      },
    },
    include: {
      buyer: true,
      address: {
        include: {
          zone: true,
        },
      },
      orderItems: {
        include: {
          product: {
            include: {
              images: true,
              unit: true,
              shop: true,
            },
          },
        },
      },
    },
  })) as OrderWithItems[];

  // If any order is not found, return 404
  if (orders.length !== orderIds.length) {
    notFound();
  }

  return (
    <Suspense key={orderIdsParam} fallback={<Loading />}>
      <Provider>
        <PickingPageClient orders={orders} />
      </Provider>
    </Suspense>
  );
}
