import { Skeleton } from "@udoy/components/ui/skeleton";
import { Card } from "@udoy/components/ui/card";

export default function Loading() {
  return (
    <main className="container mx-auto px-4 py-6 max-w-md">
      <div className="flex flex-col min-h-[calc(100vh-2rem)]">
        {/* Header Skeleton */}
        <div className="sticky top-0 z-10 bg-background pb-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <Skeleton className="h-10 w-10 mr-2" />
              <Skeleton className="h-8 w-[180px]" />
            </div>
            <Skeleton className="h-10 w-10" />
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <Skeleton className="h-4 w-[120px]" />
              <Skeleton className="h-4 w-[40px]" />
            </div>
            <Skeleton className="h-2 w-full" />
          </div>
        </div>

        {/* Product List Skeleton */}
        <div className="flex-1 mb-20">
          {Array.from({ length: 3 }).map((_, groupIndex) => (
            <div key={groupIndex} className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <Skeleton className="h-5 w-[150px]" />
                <Skeleton className="h-7 w-[100px]" />
              </div>
              <div className="space-y-3">
                {Array.from({ length: 2 }).map((_, itemIndex) => (
                  <Card key={itemIndex} className="transition-colors">
                    <div className="p-4 flex items-center">
                      <Skeleton className="h-5 w-5 mr-4 flex-shrink-0" />
                      <div className="flex items-center flex-1">
                        <Skeleton className="h-12 w-12 rounded-md mr-3 flex-shrink-0" />
                        <div className="flex-1">
                          <Skeleton className="h-5 w-[180px] mb-2" />
                          <div className="flex items-center justify-between mt-1">
                            <Skeleton className="h-4 w-[80px]" />
                            <Skeleton className="h-4 w-[60px]" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Footer Skeleton */}
        <div className="fixed bottom-0 left-0 right-0 bg-background border-t p-4">
          <div className="container mx-auto max-w-md">
            <div className="flex justify-between items-center mb-3">
              <Skeleton className="h-4 w-[100px]" />
              <Skeleton className="h-4 w-[60px]" />
            </div>
            <div className="flex justify-between items-center mb-4">
              <Skeleton className="h-4 w-[100px]" />
              <Skeleton className="h-4 w-[60px]" />
            </div>
            <Skeleton className="h-12 w-full" />
          </div>
        </div>
      </div>
    </main>
  );
}
