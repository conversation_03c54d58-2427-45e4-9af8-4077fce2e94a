import { OrderWithItems } from "../types";

export interface GroupedItems {
    groupName: string,
    items: OrderWithItems["orderItems"];
}

export function groupByShop(orders: OrderWithItems[]) {
  const grouped: { [key: string]: OrderWithItems["orderItems"]  } = {};
  orders.forEach((order) => {
    order.orderItems.forEach((item) => {
      const shopName = item.product.shop?.name || "Unknown Shop";
      if (!grouped[shopName]) {
        grouped[shopName] = [];
      }
      grouped[shopName].push(item);
    });
  });
  return Object.entries(grouped).map(([groupName, groupItems]) => ({
    groupName,
    items: groupItems,
  }));
}

export function groupByProduct(orders: OrderWithItems[]) {
  const grouped: { [key: string]: OrderWithItems["orderItems"] } = {};
  orders.forEach((order) => {
    order.orderItems.forEach((item) => {
      if (!grouped[item.product.id]) {
        grouped[item.product.id] = [];
      }
      grouped[item.product.id].push(item);
    });
  });
  return Object.entries(grouped).map(([groupName, groupItems]) => ({
    groupName,
    items: groupItems,
  }));
}

export function groupByCustomer(orders: OrderWithItems[]) {
  const grouped: { [key: string]: OrderWithItems["orderItems"] } = {};
  orders.forEach((order) => {
    const customerName = order.buyer.name;
    grouped[customerName] = order.orderItems;
  });
  return Object.entries(grouped).map(([groupName, groupItems]) => ({
    groupName,
    items: groupItems,
  }));
}
