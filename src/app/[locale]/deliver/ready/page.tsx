import { Suspense } from "react";
import { DeliveryPageClient } from "./components/DeliveryPageView";
import { getPrisma } from "@udoy/utils/db-utils";
import { OrderWithItems } from "../types";
import { notFound } from "next/navigation";
import Loading from "./loading";

interface DeliveryPageProps {
  searchParams: Promise<{ order?: string }>;
}

export default async function DeliveryPage({
  searchParams,
}: DeliveryPageProps) {
  const { order: orderId } = await searchParams;

  if (!orderId) {
    notFound();
  }

  const id = parseInt(orderId);

  // Fetch orders from database
  const prisma = getPrisma();

  const order = await prisma.order.findUnique({
    where: {
      id,
    },
    include: {
      buyer: true,
      address: {
        include: {
          zone: true,
        },
      },
      deliveryMan: true,
      orderItems: {
        include: {
          product: {
            include: {
              images: true,
              unit: true,
            },
          },
        },
      },
    },
  });

  // If any order is not found, return 404
  if (!order) {
    notFound();
  }

  return (
    <Suspense fallback={<Loading />} key={orderId}>
      <DeliveryPageClient order={order} />
    </Suspense>
  );
}
