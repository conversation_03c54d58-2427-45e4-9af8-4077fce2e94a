import { Skeleton } from "@udoy/components/ui/skeleton";
import { Card, CardContent } from "@udoy/components/ui/card";

export default function Loading() {
  return (
    <main className="container mx-auto px-4 py-6 max-w-md">
      <div className="flex flex-col min-h-[calc(100vh-2rem)]">
        {/* Header Skeleton */}
        <div className="sticky top-0 z-10 bg-background pb-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <Skeleton className="h-10 w-10 mr-2" />
              <Skeleton className="h-8 w-[180px]" />
            </div>
            <Skeleton className="h-10 w-10" />
          </div>

          {/* Delivery Progress Skeleton */}
          <div className="mb-6">
            <Skeleton className="h-10 w-full rounded-md" />
          </div>
        </div>

        {/* Delivery Steps Content Skeleton */}
        <div className="flex-1 mb-20">
          <div className="space-y-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center mb-3">
                  <Skeleton className="h-5 w-5 mr-2" />
                  <Skeleton className="h-5 w-[150px]" />
                </div>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-3/4 mb-4" />
                <Skeleton className="h-10 w-full" />
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center mb-3">
                  <Skeleton className="h-5 w-5 mr-2" />
                  <Skeleton className="h-5 w-[150px]" />
                </div>
                <div className="space-y-3">
                  {Array.from({ length: 3 }).map((_, index) => (
                    <div key={index} className="flex items-center">
                      <Skeleton className="h-16 w-16 rounded-md mr-3" />
                      <div className="flex-1">
                        <Skeleton className="h-5 w-[180px] mb-2" />
                        <Skeleton className="h-4 w-[120px]" />
                      </div>
                      <div className="text-right">
                        <Skeleton className="h-5 w-[60px] mb-1" />
                        <Skeleton className="h-4 w-[80px]" />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Footer Action Bar Skeleton */}
        <div className="fixed bottom-0 left-0 right-0 bg-background border-t p-4">
          <Skeleton className="h-12 w-full" />
        </div>
      </div>
    </main>
  );
}
