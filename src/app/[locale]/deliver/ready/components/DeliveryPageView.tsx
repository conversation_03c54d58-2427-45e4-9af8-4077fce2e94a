"use client";

import { useRouter } from "next/navigation";
import { OrderWithItems } from "../../types";
import { toast } from "sonner";
import { withError } from "@udoy/utils/app-error";
import { completeDelivery, startDelivery } from "../actions";
import { useState } from "react";
import { OrderStatus } from "@prisma/client";
import { Card, CardContent } from "@udoy/components/ui/card";
import { Button } from "@udoy/components/ui/button";
import { Badge } from "@udoy/components/ui/badge";
import {
  MapPin,
  Phone,
  ArrowLeft,
  Settings,
  Navigation,
  Package,
  Package2,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetFooter,
} from "@udoy/components/ui/sheet";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@udoy/components/ui/tabs";
import { ScrollArea } from "@udoy/components/ui/scroll-area";
import Image from "next/image";
import Locale from "@udoy/components/Locale/Client";
import { UnitUtil } from "@udoy/utils/product-unit";
import { useLocale } from "next-intl";

interface DeliveryPageClientProps {
  order: OrderWithItems;
}

export function DeliveryPageClient({ order }: DeliveryPageClientProps) {
  const router = useRouter();
  const [activeStep, setActiveStep] = useState<"pickup" | "delivery">(
    order.status === OrderStatus.PACKED ? "pickup" : "delivery"
  );
  const [settingsOpen, setSettingsOpen] = useState(false);
  const totalAmount = order.subTotal + order.shipping;
  const locale = useLocale();

  async function handleConfirmPickup() {
    try {
      await withError(startDelivery(order.id));
      toast.success("Delivery started");
      setActiveStep("delivery");
    } catch (error: any) {
      toast.error(error?.messge || "Failed to start delivery");
    }
  }

  async function handleConfirmDelivery() {
    try {
      await withError(completeDelivery(order.id));
      toast.success("Delivery completed");
      router.push("/deliver");
    } catch (error: any) {
      toast.error(error?.messge || "Failed to complete delivery");
    }
  }

  return (
    <main className="container mx-auto px-4 py-6 max-w-md">
      <div className="flex flex-col min-h-[calc(100vh-2rem)]">
        {/* Header */}
        <div className="sticky top-0 z-10 bg-background pb-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => router.push("/deliver")}
                className="mr-2"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <h1 className="text-xl font-bold">Order #${order.id}</h1>
            </div>
            <Sheet open={settingsOpen} onOpenChange={setSettingsOpen}>
              <SheetTrigger asChild>
                <Button variant="outline" size="icon">
                  <Settings className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent>
                <SheetHeader>
                  <SheetTitle>Delivery Details</SheetTitle>
                </SheetHeader>

                <Tabs defaultValue="customers" className="mt-6">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="customers">Customers</TabsTrigger>
                    <TabsTrigger value="items">Items</TabsTrigger>
                  </TabsList>

                  {/* Customer Information Tab */}
                  <TabsContent value="customers" className="mt-4">
                    <ScrollArea className="h-[calc(100vh-14rem)] pr-4">
                      {[order].map((order, index) => (
                        <Card
                          key={order.id}
                          className={index > 0 ? "mt-4" : ""}
                        >
                          <CardContent className="p-4">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center">
                                <Badge className="mr-2">#{order.id}</Badge>
                                <h3 className="font-medium">
                                  {order.buyer.name}
                                </h3>
                              </div>
                            </div>

                            <div className="space-y-2 mt-3 text-sm">
                              <div className="flex items-start">
                                <MapPin className="h-4 w-4 mr-2 mt-0.5 text-muted-foreground" />
                                <div>
                                  <p>{order.address?.home}</p>
                                  <p className="text-muted-foreground">
                                    <Locale bn={order.address?.zone?.nam}>
                                      {order.address?.zone?.name}
                                    </Locale>
                                  </p>
                                </div>
                              </div>
                              {order.buyer.phone && (
                                <div className="flex items-center">
                                  <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                                  <p>{order.buyer.phone}</p>
                                </div>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </ScrollArea>
                  </TabsContent>

                  {/* Items Tab */}
                  {/* <TabsContent value="items" className="mt-4">
                  <ScrollArea className="h-[calc(100vh-14rem)] pr-4">
                    {order.orderItems.map((item, index) => (
                      <Card key={item.id} className={index > 0 ? "mt-4" : ""}>
                        <CardContent className="p-4 flex items-center">
                          {item.product.images[0]?.url && (
                            <div className="h-12 w-12 rounded-md overflow-hidden mr-3 flex-shrink-0">
                              <Image
                                src={item.product.images[0].url}
                                alt={item.product.name}
                                width={48}
                                height={48}
                                className="h-full w-full object-cover"
                              />
                            </div>
                          )}
                          <div className="flex-1">
                            <h4 className="font-medium">{item.product.name}</h4>
                            <div className="flex items-center justify-between mt-1">
                              <p className="text-sm text-muted-foreground">
                                {item.quantity}{" "}
                                {(item as any).product.unit?.name || "piece"}
                              </p>
                              <p className="text-sm font-medium">
                                ৳{item.price * item.quantity}
                              </p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </ScrollArea>
                </TabsContent> */}
                </Tabs>

                <SheetFooter className="mt-6">
                  <Button onClick={() => setSettingsOpen(false)}>Done</Button>
                </SheetFooter>
              </SheetContent>
            </Sheet>
          </div>

          {/* Delivery Progress */}
          <div className="mb-6">
            <Tabs value={activeStep} className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger
                  value="pickup"
                  onClick={() => setActiveStep("pickup")}
                >
                  Pickup
                </TabsTrigger>
                <TabsTrigger
                  value="delivery"
                  onClick={() => setActiveStep("delivery")}
                >
                  Delivery
                </TabsTrigger>
                {/* <TabsTrigger value="complete" onClick={() => setActiveStep("complete")}>
                Complete
              </TabsTrigger> */}
              </TabsList>
            </Tabs>
          </div>
        </div>

        {/* Delivery Steps Content */}
        <div className="flex-1 mb-20">
          {activeStep === "pickup" && (
            <div className="space-y-4">
              {/* <Card>
                <CardContent className="p-4">
                  <div className="flex items-center mb-3">
                    <Package className="h-5 w-5 mr-2 text-primary" />
                    <h3 className="font-medium">Pickup Instructions</h3>
                  </div>
                  <p className="text-sm text-muted-foreground mb-4">
                    Confirm that you have all the items for delivery. Check the
                    items list in the settings panel.
                  </p>
                  <Button className="w-full" onClick={handleConfirmPickup}>
                    Confirm Pickup
                  </Button>
                </CardContent>
              </Card> */}

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center mb-3">
                    <Package2 className="h-5 w-5 mr-2 text-primary" />
                    <h3 className="font-medium mt-0.5">Order Items</h3>
                  </div>
                  <div className="space-y-3">
                    {order.orderItems.map((item, index) => (
                      <div key={item.id} className="flex items-center">
                        <div className="h-16 w-16 relative rounded-md overflow-hidden bg-muted mr-3 flex-shrink-0">
                          <Image
                            src={
                              item.product.images[0]?.url || "/placeholder.svg"
                            }
                            alt={item.product.name}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <div className="flex-1">
                          <div className="font-medium">
                            <Locale bn={item.product.nam}>
                              {item.product.name}
                            </Locale>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {UnitUtil.getAmountUnit(
                              item.product.amount * item.quantity,
                              item.product.unit,
                              locale
                            )}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">৳{item.price}</div>
                          <div className="text-sm text-muted-foreground">
                            ৳{(item.price ) * item.quantity}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {activeStep === "delivery" && (
            <div className="space-y-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center mb-3">
                    <MapPin className="h-5 w-5 mr-2 text-primary" />
                    <h3 className="font-medium">Delivery Address</h3>
                  </div>
                  <div className="text-sm mb-4">
                    <p className="font-medium">{order.buyer.name}</p>
                    <p>{order.address?.home}</p>
                    <p className="text-muted-foreground">
                      {order.address?.zone?.name}
                    </p>
                    {order.buyer.phone && (
                      <p className="mt-2 flex items-center">
                        <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                        {order.buyer.phone}
                      </p>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <Button
                      className="flex-1"
                      variant="outline"
                      onClick={() => {
                        // In a real app, you would open the phone app
                        if (order.address.phone) {
                          window.location.href = `tel:${order.address.phone}`;
                        }
                      }}
                    >
                      <Phone className="h-4 w-4 mr-2" />
                      Call
                    </Button>
                    <Button
                      className="flex-1"
                      variant="outline"
                      onClick={() => {
                        // In a real app, you would open maps with the address
                        const address = encodeURIComponent(
                          order.address.location || order.address.home
                        );
                        window.open(
                          `https://maps.google.com/?q=${address}`,
                          "_blank"
                        );
                      }}
                    >
                      <Navigation className="h-4 w-4 mr-2" />
                      Navigate
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* <Card>
              <CardContent className="p-4">
                <div className="flex items-center mb-3">
                  <Check className="h-5 w-5 mr-2 text-primary" />
                  <h3 className="font-medium">Complete Delivery</h3>
                </div>
                <p className="text-sm text-muted-foreground mb-4">
                  Confirm that you have delivered all items to the customer and
                  collected payment.
                </p>
                <div className="space-y-4">
                  <Button
                    className="w-full"
                    variant="outline"
                    onClick={() => {
                      // In a real app, you would open the camera
                    }}
                  >
                    <Camera className="h-4 w-4 mr-2" />
                    Take Delivery Photo
                  </Button>
                  <Button className="w-full" onClick={handleComplete}>
                    Complete Delivery
                  </Button>
                </div>
              </CardContent>
            </Card> */}

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center mb-3">
                    <Package className="h-5 w-5 mr-2 text-primary" />
                    <h3 className="font-medium">Order Summary</h3>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">
                        Items
                      </span>
                      <span className="text-sm">{order.orderItems.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">
                        Total Amount
                      </span>
                      <span className="text-sm font-medium">
                        ৳ {totalAmount}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">
                        Payment Method
                      </span>
                      <span className="text-sm">Cash on Delivery</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        {/* Footer Action Bar */}
        <div className="fixed bottom-0 left-0 right-0 bg-background border-t p-4">
          <div className="">
            {activeStep === "pickup" && (
              <Button className="w-full" onClick={handleConfirmPickup}>
                Confirm Pickup
              </Button>
            )}
            {activeStep === "delivery" && (
              <Button className="w-full" onClick={handleConfirmDelivery}>
                Confirm Delivery
              </Button>
            )}
          </div>
        </div>
      </div>
    </main>
  );
}
