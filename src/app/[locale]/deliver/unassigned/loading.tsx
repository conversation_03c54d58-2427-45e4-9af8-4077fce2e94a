import { Skeleton } from "@udoy/components/ui/skeleton";
import { Card, CardContent, CardHeader } from "@udoy/components/ui/card";
import { Separator } from "@udoy/components/ui/separator";

export default function Loading() {
  return (
    <main className="container mx-auto px-4 py-6 max-w-md">
      <div className="flex flex-col min-h-[calc(100vh-2rem)]">
        {/* Header Skeleton */}
        <div className="sticky top-0 z-10 bg-background pb-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <Skeleton className="h-10 w-10 mr-2" />
              <Skeleton className="h-8 w-[180px]" />
            </div>
            <Skeleton className="h-6 w-24 rounded-full" />
          </div>
        </div>

        {/* Order Details Skeleton */}
        <div className="space-y-4 mb-20">
          {/* Customer & Delivery Info Card Skeleton */}
          <Card className="border-l-4 border-l-orange-400 overflow-hidden">
            <div className="bg-gradient-to-r from-orange-50 to-transparent p-4">
              <div className="flex justify-between items-start mb-3">
                <div>
                  <Skeleton className="h-6 w-[150px] mb-1" />
                  <Skeleton className="h-4 w-[100px]" />
                </div>
                <Skeleton className="h-6 w-16" />
              </div>

              <div className="space-y-3">
                <div className="flex items-start">
                  <Skeleton className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
                  <div>
                    <Skeleton className="h-5 w-[200px] mb-1" />
                    <Skeleton className="h-4 w-[150px]" />
                  </div>
                </div>

                <div className="flex items-center">
                  <Skeleton className="h-5 w-5 mr-2 flex-shrink-0" />
                  <Skeleton className="h-5 w-[120px]" />
                </div>
              </div>
            </div>

            <Separator />

            <div className="p-4 grid grid-cols-2 gap-4">
              <div className="flex flex-col">
                <Skeleton className="h-4 w-[80px] mb-1" />
                <Skeleton className="h-5 w-[120px]" />
              </div>
              <div className="flex flex-col">
                <Skeleton className="h-4 w-[80px] mb-1" />
                <Skeleton className="h-5 w-[120px]" />
              </div>
            </div>

            <Separator />
            <div className="p-4">
              <div className="flex items-center mb-1">
                <Skeleton className="h-4 w-4 mr-1" />
                <Skeleton className="h-4 w-[150px]" />
              </div>
              <Skeleton className="h-16 w-full rounded-md" />
            </div>
          </Card>

          {/* Items List Skeleton */}
          <Card>
            <CardHeader className="pb-2">
              <Skeleton className="h-5 w-[150px]" />
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {Array.from({ length: 3 }).map((_, index) => (
                  <div key={index} className="flex items-center">
                    <Skeleton className="h-16 w-16 rounded-md mr-3 flex-shrink-0" />
                    <div className="flex-1">
                      <Skeleton className="h-5 w-[180px] mb-2" />
                      <Skeleton className="h-4 w-[120px]" />
                    </div>
                    <div className="text-right">
                      <Skeleton className="h-5 w-[60px] mb-1" />
                      <Skeleton className="h-4 w-[80px]" />
                    </div>
                  </div>
                ))}
              </div>

              <Separator className="my-3" />

              <div className="space-y-2">
                <div className="flex justify-between">
                  <Skeleton className="h-4 w-[80px]" />
                  <Skeleton className="h-4 w-[60px]" />
                </div>
                <div className="flex justify-between">
                  <Skeleton className="h-4 w-[80px]" />
                  <Skeleton className="h-4 w-[60px]" />
                </div>
                <div className="flex justify-between">
                  <Skeleton className="h-5 w-[60px]" />
                  <Skeleton className="h-5 w-[80px]" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Footer Action Bar Skeleton */}
        <div className="fixed bottom-0 left-0 right-0 bg-background border-t p-4">
          <div className="container mx-auto max-w-md flex gap-3">
            <Skeleton className="h-12 flex-1" />
            <Skeleton className="h-12 flex-1" />
          </div>
        </div>
      </div>
    </main>
  );
}
