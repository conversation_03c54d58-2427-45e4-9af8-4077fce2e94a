import { Suspense } from "react";
import { getPrisma } from "@udoy/utils/db-utils";
import { notFound } from "next/navigation";
import { UnassignedOrderView } from "./components/UnassignedOrderView";
import Loading from "./loading";
import { OrderStatus } from "@prisma/client";

export default async function UnassignedOrderPage({
  searchParams,
}: {
  searchParams: Promise<{ order: string }>;
}) {
  const { order: orderId } = await searchParams;

  if (!orderId || isNaN(parseInt(orderId))) {
    notFound();
  }

  // Fetch order from database
  const prisma = getPrisma();

  const order = await prisma.order.findUnique({
    where: {
      id: parseInt(orderId),
    },
    include: {
      buyer: true,
      address: {
        include: {
          zone: true,
        },
      },
      deliveryMan: true,
      orderItems: {
        include: {
          product: {
            include: {
              images: true,
              unit: true,
            },
          },
        },
      },
    },
  });

  const orderCount = await prisma.order.count({
    where: {
      buyerId: order?.buyerId,
      status: OrderStatus.DELIVERED,
    },
  });

  if (!order) {
    notFound();
  }

  return (
    <Suspense key={orderId} fallback={<Loading />}>
      <UnassignedOrderView order={order} isFirstOrder={orderCount === 0} />
    </Suspense>
  );
}
