"use server";

import { OrderStatus } from "@prisma/client";
import { sendPushNotification } from "@udoy/libs/backend/push-service";
import { ActionError } from "@udoy/utils/app-error";
import { CookieUtil } from "@udoy/utils/cookie-util";
import { getPrisma } from "@udoy/utils/db-utils";
import { revalidatePath } from "next/cache";

export async function assignOrderToMe(orderId: number) {
  try {
    const prisma = getPrisma();
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const order = await prisma.order.findUnique({
      where: { id: orderId },
    });

    if (!order) {
      return ActionError("Order not found");
    }

    if (order.status !== OrderStatus.PENDING) {
      return ActionError("Order is not pending");
    }

    await prisma.order.update({
      where: { id: orderId },
      data: {
        deliveryManId: userId,
        status: OrderStatus.CONFIRMED,
        timeline: {
          create: {
            status: OrderStatus.CONFIRMED,
            note: "Order Confirmed and Assigned to Delivery Person",
          },
        },
      },
    });

    await sendPushNotification(order.buyerId, {
      title: `অর্ডার কনফার্ম। অর্ডার নংঃ ${order.id}`,
      body: `আপনার অর্ডার কনফার্ম করা হয়েছে এবং দ্রুত প্রসেসিং করা হবে। আপনার অর্ডার নংঃ ${
        order.id
      }, টাকার পরিমাণ: ${(order.subTotal + order.shipping).toLocaleString(
        "bn"
      )} টাকা`,
      data: {
        url: `/orders`,
        tag: order.id,
      },
    });

    revalidatePath("/deliver");
    return true;
  } catch (error) {
    console.log(error);
    return ActionError(`Failed to assign order`);
  }
}
