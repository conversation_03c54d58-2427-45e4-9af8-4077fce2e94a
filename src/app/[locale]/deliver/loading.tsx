import React from "react";
import { Skeleton } from "@udoy/components/ui/skeleton";
import { Card } from "@udoy/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@udoy/components/ui/tabs";

export default function Loading() {
  return (
    <main className="container mx-auto px-4 py-6 max-w-md">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Skeleton className="h-10 w-10 mr-2" />
          <Skeleton className="h-8 w-[120px]" />
        </div>
        <Skeleton className="h-10 w-10" />
      </div>

      <Tabs defaultValue="assigned">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="assigned">Assigned</TabsTrigger>
          <TabsTrigger value="ready">Ready</TabsTrigger>
          <TabsTrigger value="unassigned">Unassigned</TabsTrigger>
        </TabsList>

        <TabsContent value="assigned" className="mt-6">
          <OrderListSkeleton />
        </TabsContent>

        <TabsContent value="ready" className="mt-6">
          <OrderListSkeleton />
        </TabsContent>

        <TabsContent value="unassigned" className="mt-6">
          <OrderListSkeleton />
        </TabsContent>
      </Tabs>
    </main>
  );
}

function OrderListSkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 3 }).map((_, index) => (
        <Card key={index} className="p-4">
          <div className="flex justify-between mb-2">
            <Skeleton className="h-5 w-[120px]" />
            <Skeleton className="h-5 w-[80px]" />
          </div>
          <div className="flex items-start gap-3">
            <Skeleton className="h-16 w-16 rounded-md flex-shrink-0" />
            <div className="flex-1">
              <Skeleton className="h-5 w-full mb-2" />
              <Skeleton className="h-4 w-3/4 mb-2" />
              <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-4 rounded-full" />
                <Skeleton className="h-4 w-[100px]" />
              </div>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
}
