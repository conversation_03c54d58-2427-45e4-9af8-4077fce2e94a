import { Role } from "@prisma/client";
import { Toaster } from "@udoy/components/ui/sonner";
import { <PERSON>ieUtil } from "@udoy/utils/cookie-util";
import { getPrisma } from "@udoy/utils/db-utils";
import { notFound } from "next/navigation";
import React, { Fragment } from "react";

async function Layout({ children }: { children: React.ReactNode }) {
  const userId = await CookieUtil.userId();

  if (!userId) {
    return notFound();
  }

  const prisma = getPrisma();
  const user = await prisma.user.findUnique({
    where: {
      id: userId,
      role: { in: [Role.DELIVERY_MAN, Role.SUPER_ADMIN, Role.ADMIN] },
    },
  });

  if (!user) {
    return notFound();
  }

  return children;
}

export default Layout;
