"use client"

import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@udoy/components/ui/card"
import { Button } from "@udoy/components/ui/button"
import { Badge } from "@udoy/components/ui/badge"
import { MapPin, Package } from "lucide-react"
import { OrderWithItems } from "../types"

interface UnassignedOrdersTabProps {
  orders: OrderWithItems[]
}

export function UnassignedOrdersTab({ orders }: UnassignedOrdersTabProps) {
  const router = useRouter()

  const handleViewOrder = (orderId: number) => {
    router.push(`/deliver/unassigned?order=${orderId}`)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-medium">Unassigned Orders</h2>
        <Badge variant="outline" className="px-2 py-1">
          {orders.length} Orders
        </Badge>
      </div>

      {orders.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">No unassigned orders</div>
      ) : (
        <div className="space-y-3">
          {orders.map((order) => (
            <Card key={order.id} className="border-l-4 border-l-yellow-500">
              <CardHeader className="p-4 pb-2">
                <div className="flex items-center justify-between">
                  <div className="w-full">
                    <div className="flex items-center gap-2 justify-between">
                      <CardTitle className="text-base">Order #{order.id}</CardTitle>
                      <Badge variant="outline" className="text-xs">
                        {order.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">{order.buyer.name}</p>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-4 pt-0 pb-2">
                <div className="flex items-center text-sm text-muted-foreground">
                  <MapPin className="h-4 w-4 mr-1" />
                  <span>{order.address?.zone?.name || "No zone"}</span>
                </div>
                <div className="flex items-center text-sm mt-1">
                  <Package className="h-4 w-4 mr-1" />
                  <span>{order.orderItems.length} items</span>
                </div>
              </CardContent>
              <CardFooter className="p-2 flex ">
                <Button onClick={() => handleViewOrder(order.id)} className="w-full" size="sm">
                  View Details
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}