"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON>oot<PERSON>, CardHeader, CardTitle } from "@udoy/components/ui/card"
import { <PERSON><PERSON> } from "@udoy/components/ui/button"
import { Checkbox } from "@udoy/components/ui/checkbox"
import { Badge } from "@udoy/components/ui/badge"
import { MapPin } from "lucide-react"

// Mock data based on the Prisma schema
const mockOrders = [
  {
    id: 1,
    buyerName: "John Doe",
    zone: "Downtown Core",
    itemCount: 12,
    status: "CONFIRMED",
    address: "123 Main St, Apt 4B",
  },
  {
    id: 2,
    buyerName: "Jane Smith",
    zone: "Westside",
    itemCount: 8,
    status: "CONFIRMED",
    address: "456 Oak Ave",
  },
  {
    id: 3,
    buyerName: "Robert Johnson",
    zone: "Eastside",
    itemCount: 15,
    status: "CONFIRMED",
    address: "789 Pine Blvd",
  },
  {
    id: 4,
    buyerName: "<PERSON>",
    zone: "Downtown Core",
    itemCount: 5,
    status: "CONFIRMED",
    address: "321 Elm St",
  },
]

export function AssignedOrdersDashboard() {
  const [selectedOrders, setSelectedOrders] = useState<number[]>([])
  const router = useRouter()

  const handleOrderSelect = (orderId: number) => {
    setSelectedOrders((prev) => (prev.includes(orderId) ? prev.filter((id) => id !== orderId) : [...prev, orderId]))
  }

  const handlePickSelected = () => {
    const orderIds = selectedOrders.join(",")
    router.push(`/deliver/picking?orders=${orderIds}`)
  }

  const handleOrderClick = (orderId: number) => {
    router.push(`/deliver/picking?orders=${orderId}`)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-medium">Assigned for Picking</h2>
        <Badge variant="outline" className="px-2 py-1">
          {mockOrders.length} Orders
        </Badge>
      </div>

      {mockOrders.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">No orders assigned for picking</div>
      ) : (
        <>
          <div className="space-y-3">
            {mockOrders.map((order) => (
              <Card key={order.id} className="border-l-4 border-l-primary">
                <CardHeader className="p-4 pb-2 flex flex-row items-start">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-base">Order #{order.id}</CardTitle>
                      <Badge variant={order.status === "PENDING" ? "secondary" : "default"} className="text-xs">
                        {order.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">{order.buyerName}</p>
                  </div>
                  <Checkbox
                    checked={selectedOrders.includes(order.id)}
                    onCheckedChange={() => handleOrderSelect(order.id)}
                    onClick={(e) => e.stopPropagation()}
                    className="h-5 w-5"
                  />
                </CardHeader>
                <CardContent className="p-4 pt-0 pb-2">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <MapPin className="h-4 w-4 mr-1" />
                    <span>{order.zone}</span>
                  </div>
                  <p className="text-sm mt-1">
                    <span className="font-medium">{order.itemCount}</span> items to pick
                  </p>
                </CardContent>
                <CardFooter className="p-2 flex justify-end">
                  <Button variant="ghost" size="sm" onClick={() => handleOrderClick(order.id)}>
                    View Details
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>

          {selectedOrders.length > 0 && (
            <div className="sticky bottom-4 flex justify-center">
              <Button onClick={handlePickSelected} className="shadow-lg" size="lg">
                Pick {selectedOrders.length} Selected Order{selectedOrders.length > 1 ? "s" : ""}
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  )
}
