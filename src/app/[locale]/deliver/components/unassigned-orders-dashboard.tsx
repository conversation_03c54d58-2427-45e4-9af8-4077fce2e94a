"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON>ooter, CardHeader, CardTitle } from "@udoy/components/ui/card"
import { <PERSON><PERSON> } from "@udoy/components/ui/button"
import { Checkbox } from "@udoy/components/ui/checkbox"
import { Badge } from "@udoy/components/ui/badge"
import { MapPin, AlertCircle, Eye } from "lucide-react"

// Mock data for unassigned orders
const mockOrders = [
  {
    id: 8,
    buyerName: "Jennifer Taylor",
    zone: "Downtown Core",
    itemCount: 9,
    status: "PENDING",
    address: "345 Walnut St",
  },
  {
    id: 9,
    buyerName: "Thomas Anderson",
    zone: "Eastside",
    itemCount: 6,
    status: "PENDING",
    address: "678 Pine Ave",
  },
]

export function UnassignedOrdersDashboard() {
  const [selectedOrders, setSelectedOrders] = useState<number[]>([])
  const router = useRouter()

  const handleOrderSelect = (orderId: number) => {
    setSelectedOrders((prev) => (prev.includes(orderId) ? prev.filter((id) => id !== orderId) : [...prev, orderId]))
  }

  const handleAssignToMe = () => {
    // In a real app, this would call an API to assign the orders to the current delivery person
    alert(`Assigned orders ${selectedOrders.join(", ")} to you`)
    // Then refresh the data
    setSelectedOrders([])
  }

  const handleViewOrder = (orderId: number) => {
    router.push(`/deliver/unassigned/${orderId}`)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-medium">Unassigned Orders</h2>
        <Badge variant="outline" className="px-2 py-1">
          {mockOrders.length} Orders
        </Badge>
      </div>

      {mockOrders.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">No unassigned orders available</div>
      ) : (
        <>
          <div className="space-y-3">
            {mockOrders.map((order) => (
              <Card key={order.id} className="border-l-4 border-l-orange-400">
                <CardHeader className="p-4 pb-2 flex flex-row items-start">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-base">Order #{order.id}</CardTitle>
                      <Badge variant="outline" className="text-xs bg-orange-100 text-orange-800 border-orange-200">
                        {order.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">{order.buyerName}</p>
                  </div>
                  <Checkbox
                    checked={selectedOrders.includes(order.id)}
                    onCheckedChange={() => handleOrderSelect(order.id)}
                    onClick={(e) => e.stopPropagation()}
                    className="h-5 w-5"
                  />
                </CardHeader>
                <CardContent className="p-4 pt-0 pb-2">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <MapPin className="h-4 w-4 mr-1" />
                    <span>{order.zone}</span>
                  </div>
                  <div className="flex items-center text-sm mt-1">
                    <AlertCircle className="h-4 w-4 mr-1 text-orange-500" />
                    <span>
                      <span className="font-medium">{order.itemCount}</span> items waiting
                    </span>
                  </div>
                </CardContent>
                <CardFooter className="p-2 flex justify-between">
                  <Button variant="ghost" size="sm" onClick={() => handleViewOrder(order.id)}>
                    <Eye className="h-4 w-4 mr-1" />
                    View Details
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleOrderSelect(order.id)
                    }}
                  >
                    {selectedOrders.includes(order.id) ? "Selected" : "Select"}
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>

          {selectedOrders.length > 0 && (
            <div className="sticky bottom-4 flex justify-center">
              <Button onClick={handleAssignToMe} className="shadow-lg bg-orange-500 hover:bg-orange-600" size="lg">
                Assign {selectedOrders.length} Order{selectedOrders.length > 1 ? "s" : ""} to Me
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  )
}
