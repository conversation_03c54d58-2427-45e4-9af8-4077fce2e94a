"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@udoy/components/ui/card";
import { Button } from "@udoy/components/ui/button";
import { Checkbox } from "@udoy/components/ui/checkbox";
import { Badge } from "@udoy/components/ui/badge";
import { MapPin } from "lucide-react";
import { OrderWithItems } from "../types";

interface AssignedOrdersTabProps {
  orders: OrderWithItems[];
}

export function AssignedOrdersTab({ orders }: AssignedOrdersTabProps) {
  const [selectedOrders, setSelectedOrders] = useState<number[]>([]);
  const router = useRouter();

  const handleOrderSelect = (orderId: number) => {
    setSelectedOrders((prev) =>
      prev.includes(orderId)
        ? prev.filter((id) => id !== orderId)
        : [...prev, orderId]
    );
  };

  const handlePickSelected = () => {
    const orderIds = selectedOrders.join(",");
    router.push(`/deliver/picking?orders=${orderIds}`);
  };

  const handleOrderClick = (orderId: number) => {
    router.push(`/deliver/picking?orders=${orderId}`);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-medium">Assigned for Picking</h2>
        <Badge variant="outline" className="px-2 py-1">
          {orders.length} Orders
        </Badge>
      </div>

      {orders.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          No orders assigned for picking
        </div>
      ) : (
        <>
          <div className="space-y-3">
            {orders.map((order) => (
              <Card key={order.id} className="border-l-4 border-l-primary">
                <CardHeader className="p-4 pb-2 flex flex-row items-start">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-base">
                        Order #{order.id}
                      </CardTitle>
                      <Badge
                        variant={
                          order.status === "PENDING" ? "secondary" : "default"
                        }
                        className="text-xs"
                      >
                        {order.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      {order.buyer.name}
                    </p>
                  </div>
                  <Checkbox
                    checked={selectedOrders.includes(order.id)}
                    onCheckedChange={() => handleOrderSelect(order.id)}
                    onClick={(e) => e.stopPropagation()}
                    className="h-5 w-5"
                  />
                </CardHeader>
                <CardContent className="p-4 pt-0 pb-2">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <MapPin className="h-4 w-4 mr-1" />
                    <span>{order.address?.zone?.name || "No zone"}</span>
                  </div>
                  <p className="text-sm mt-1">
                    <span className="font-medium">
                      {order.orderItems.length}
                    </span>{" "}
                    items to pick
                  </p>
                </CardContent>
                <CardFooter className="p-2 flex justify-end">
                  <Button
                    className="w-full"
                    size="sm"
                    onClick={() => handleOrderClick(order.id)}
                  >
                    Pick Items
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>

          {selectedOrders.length > 0 && (
            <div className="sticky bottom-4 flex justify-center">
              <Button
                onClick={handlePickSelected}
                className="shadow-lg"
                size="lg"
              >
                Pick {selectedOrders.length} Selected Order
                {selectedOrders.length > 1 ? "s" : ""}
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
