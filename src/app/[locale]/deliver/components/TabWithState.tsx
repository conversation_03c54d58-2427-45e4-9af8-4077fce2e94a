"use client";

import { Tabs } from "@udoy/components/ui/tabs";
import { useAtom } from "jotai";
import React from "react";
import { activeTabAtom } from "../state";

function TabWithState({ children }: { children: React.ReactNode }) {
  const [active, set] = useAtom(activeTabAtom);

  return (
    <Tabs value={active} className="w-full" onValueChange={set as any}>
      {children}
    </Tabs>
  );
}

export default TabWithState;
