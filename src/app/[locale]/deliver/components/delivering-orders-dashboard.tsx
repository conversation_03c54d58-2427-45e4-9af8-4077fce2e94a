"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@udoy/components/ui/card";
import { But<PERSON> } from "@udoy/components/ui/button";
import { Checkbox } from "@udoy/components/ui/checkbox";
import { Badge } from "@udoy/components/ui/badge";
import { MapPin, Package } from "lucide-react";

// Mock data for orders ready for delivery (already picked)
const mockOrders = [
  {
    id: 5,
    buyerName: "Michael Brown",
    zone: "Northside",
    itemCount: 7,
    status: "PICKED",
    address: "567 Maple Dr",
    pickedAt: "2023-05-08T14:30:00Z",
  },
  {
    id: 6,
    buyerName: "Emily Davis",
    zone: "Southside",
    itemCount: 10,
    status: "PICKED",
    address: "890 Cedar Ln",
    pickedAt: "2023-05-08T15:15:00Z",
  },
  {
    id: 7,
    buyerName: "<PERSON>",
    zone: "Westside",
    itemCount: 4,
    status: "PICKED",
    address: "234 Birch Ave",
    pickedAt: "2023-05-08T16:00:00Z",
  },
];

export function DeliveringOrdersDashboard() {
  const [selectedOrders, setSelectedOrders] = useState<number[]>([]);
  const router = useRouter();

  const handleOrderSelect = (orderId: number) => {
    setSelectedOrders((prev) =>
      prev.includes(orderId)
        ? prev.filter((id) => id !== orderId)
        : [...prev, orderId]
    );
  };

  const handleDeliverSelected = () => {
    const orderIds = selectedOrders.join(",");
    router.push(`/deliver/ready?orders=${orderIds}`);
  };

  const handleOrderClick = (orderId: number) => {
    router.push(`/deliver/ready?orders=${orderId}`);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-medium">Ready for Delivery</h2>
        <Badge variant="outline" className="px-2 py-1">
          {mockOrders.length} Orders
        </Badge>
      </div>

      {mockOrders.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          No orders ready for delivery
        </div>
      ) : (
        <>
          <div className="space-y-3">
            {mockOrders.map((order) => (
              <Card key={order.id} className="border-l-4 border-l-green-500">
                <CardHeader className="p-4 pb-2 flex flex-row items-start">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-base">
                        Order #{order.id}
                      </CardTitle>
                      <Badge
                        variant="secondary"
                        className="text-xs bg-green-500"
                      >
                        {order.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      {order.buyerName}
                    </p>
                  </div>
                  <Checkbox
                    checked={selectedOrders.includes(order.id)}
                    onCheckedChange={() => handleOrderSelect(order.id)}
                    onClick={(e) => e.stopPropagation()}
                    className="h-5 w-5"
                  />
                </CardHeader>
                <CardContent className="p-4 pt-0 pb-2">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <MapPin className="h-4 w-4 mr-1" />
                    <span>{order.zone}</span>
                  </div>
                  <div className="flex items-center text-sm mt-1">
                    <Package className="h-4 w-4 mr-1 text-green-500" />
                    <span>
                      <span className="font-medium">{order.itemCount}</span>{" "}
                      items picked
                    </span>
                  </div>
                </CardContent>
                <CardFooter className="p-2 flex justify-end">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleOrderClick(order.id)}
                  >
                    View Details
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>

          {selectedOrders.length > 0 && (
            <div className="sticky bottom-4 flex justify-center">
              <Button
                onClick={handleDeliverSelected}
                className="shadow-lg bg-green-500 hover:bg-green-600"
                size="lg"
              >
                Deliver {selectedOrders.length} Selected Order
                {selectedOrders.length > 1 ? "s" : ""}
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
