import { MetadataRoute } from "next";
import { Env } from "@udoy/libs/env";

export default function robots(): MetadataRoute.Robots {
  const baseUrl = Env.NEXT_PUBLIC_FRONTEND_URL;

  return {
    rules: {
      userAgent: "*",
      allow: "/",
      disallow: [
        "/api/",
        "/shop/",
        "/dashboard/",
        "/deliver/",
      ],
    },
    sitemap: `${baseUrl}/sitemap.xml`,
  };
}
