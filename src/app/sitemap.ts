import { MetadataRoute } from "next";
import { Env } from "@udoy/libs/env";
import { routing } from "@udoy/i18n/routing";
import { getPrisma } from "@udoy/utils/db-utils";

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = Env.NEXT_PUBLIC_FRONTEND_URL;
  const currentDate = new Date();
  const categories = await getPrisma().category.findMany({
    where: { hide: false },
    select: {
      slug: true,
    },
  });

  // Base routes that exist for all locales
  const routes = [
    "",
    "checkout",
    ...categories.map((category) => category.slug),
  ];

  const sitemapEntries: MetadataRoute.Sitemap = [];

  // Generate entries for each locale and route
  routing.locales.forEach((locale) => {
    routes.forEach((route) => {
      const path = route
        ? locale === routing.defaultLocale
          ? `/${route}`
          : `/${locale}/${route}`
        : `/`;
      sitemapEntries.push({
        url: `${baseUrl}${path}`,
        lastModified: currentDate,
        changeFrequency: route === "" ? "daily" : "weekly",
        priority: route === "" ? 1.0 : 0.8,
      });
    });
  });

  return sitemapEntries;
}
