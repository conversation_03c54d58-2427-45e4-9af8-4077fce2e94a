import { MeiliSearch } from "meilisearch";
import { Env } from "../env";

export function getMeiliSearchClient() {
  return new MeiliSearch({
    host: Env.MEILI_HOST,
    apiKey: Env.MEILI_MASTER_KEY,
  });
}

export async function searchMeiliProducts(query: string) {
  const client = getMeiliSearchClient();
  const index = client.index<{
    id: string;
    name: string;
    nam: string;
  }>("products");

  // Improve search relevance with these settings
  const searchResults = await index.search(query, {
    limit: 30,
  });

  return searchResults.hits;
}
