import { FormError } from "@udoy/utils/form-error";
import { phoneSchema } from "@udoy/utils/zod";
import { z } from "zod";

export const AddressInputSchema = z.object({
  zoneId: z.string().cuid({ message: FormError.InvalidZone }),
  name: z.string().min(3),
  home: z.string().min(3),
  label: z.string().min(2),
  location: z.string().optional(),
  phone: phoneSchema,
});

export type AddressInputSchema = z.infer<typeof AddressInputSchema>;

export const ZoneInputSchema = z.object({
  name: z.string(),
  charge: z.coerce.number().min(0),
  isBase: z.nullable(z.string()).transform((arg) => arg === "on"),
  action: z.enum(["create", "remove", "update"]),
  parentId: z.string().cuid().nullable(),
  slug: z.string().min(3),
  zoneId: z.string().cuid({ message: FormError.InvalidZone }).nullable(),
});
