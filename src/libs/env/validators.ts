

function stringValidator(argument: any, key: string) {
  if (typeof argument === 'string') {
    return argument;
  }
  throw new Error(
    `Environment variable ${key} is not a valid 'String' found ${argument}`
  );
}

function numberValidator(argument: any, key: string) {
  if (!isNaN(argument)) {
    return Number(argument);
  }

  throw new Error(
    `Environment variable ${key} is not a valid 'Number' found ${argument}`
  );
}

function urlValidator(argument: any, key: string) {
  try {
    new URL(argument);
    return argument;
  } catch (error) {
    throw new Error(
      `Environment variable ${key} is not a valid 'URL' found ${argument}`
    );
  }
}


export const Validators = {
  string: stringValidator,
  number: numberValidator,
  url: urlValidator,
//   keypair: keypairValidator,
};

export type CheckKey = keyof typeof Validators;