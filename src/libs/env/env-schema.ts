import { Check } from "./decorators";

export class Env {
  @Check("string")
  static readonly APP_SECRET = process.env["APP_SECRET"]!;

  @Check("url")
  static readonly DISCORD_WEBHOOK_URL = process.env["DISCORD_WEBHOOK_URL"]!;

  @Check("url")
  static readonly DISCORD_SUMMARY_WEBHOOK_URL =
    process.env["DISCORD_SUMMARY_WEBHOOK_URL"]!;

  @Check("string")
  static readonly GOOGLE_CLIENT_SECRET = process.env["GOOGLE_CLIENT_SECRET"]!;

  @Check("string")
  static readonly GOOGLE_CLIENT_ID =
    process.env["NEXT_PUBLIC_GOOGLE_CLIENT_ID"]!;

  @Check("string")
  static readonly NEXT_PUBLIC_GOOGLE_ANALYTICS_ID =
    process.env["NEXT_PUBLIC_GOOGLE_ANALYTICS_ID"]!;

  @Check("string")
  static readonly STATIC_DIR = process.env["STATIC_DIR"]!;

  @Check("string")
  static readonly NEXT_PUBLIC_FRONTEND_URL =
    process.env["NEXT_PUBLIC_FRONTEND_URL"]!;

  @Check("string")
  static readonly IMAGE_STORE = process.env["IMAGE_STORE"]! as
    | "LOCAL"
    | "UTFS"
    | undefined;

  @Check("string")
  static readonly MEILI_MASTER_KEY = process.env["MEILI_MASTER_KEY"]!;

  @Check("string")
  static readonly MEILI_HOST = process.env["MEILI_HOST"]!;

  /**-------------------------*
   * Basic Envs
   *--------------------------*/
}
