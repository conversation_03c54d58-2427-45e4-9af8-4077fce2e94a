declare global {
  namespace NodeJS {
    interface ProcessEnv {
      POSTGRES_PRISMA_URL: string;
      POSTGRES_URL_NON_POOLING: string;
      POSTGRES_USERNAME: string;
      POSTGRES_DATABASE: string;
      POSTGRES_PASS: string;
      POSTGRES_ADMIN: string;
      APP_SECRET: string;
      GOOGLE_CLIENT_ID: string;
      GOOGLE_CLIENT_SECRET: string;
      GOOGLE_PROJECT_ID: string;
      GOOGLE_AUTH_URI: string;
      GOOGLE_TOKEN_URI: string;
      NEXT_PUBLIC_GOOGLE_ANALYTICS_ID: string;
      STATIC_DIR: string;
      DOCKER_HOST_IP: string;
      NGINX_PORT: string;
      NEXT_PUBLIC_FRONTEND_URL: string;
      UPLOADTHING_SECRET: string;
      DISCORD_WEBHOOK_URL: string;
      DISCORD_CICD_WEBHOOK: string;
      MEILI_MASTER_KEY: string;
      COMPOSE_PROFILES: string;
      NEXT_PUBLIC_VAPID_PUBLIC_KEY: string;
      VAPID_PRIVATE_KEY: string;
    }
  }
}

export {}
