import "reflect-metadata";
import { Valida<PERSON>, CheckK<PERSON> } from "./validators";


export function Check(checkKey: CheckKey): PropertyDecorator {
  return (target: any, key): void => {
    let value = target[key];
    const getter = () => {
      const validator = Validators[checkKey];
      if (!validator) {
        throw new Error(`Invalid Env Vlidator Name: ${validator}`);
      }

      if (value === undefined) {
        throw new Error(
          `Environment variable ${String(
            key
          )} not found, Maybe its not set in .env or you trying to access secret env in frontend`
        );
      }

      return validator(value, String(key));
    };

    Object.defineProperty(target, key, {
      get: getter,
    });
  };
}
