import { getPrisma } from "@udoy/utils/db-utils";
import { CartItemWithProduct, OrderItemWithProduct } from "@udoy/utils/types";

/**
 * Gets the most recent orders with customer information
 * @param limit Number of orders to return
 * @returns Array of recent orders with customer details
 */
export async function getRecentOrders(limit = 5) {
  const prisma = getPrisma();

  const orders = await prisma.order.findMany({
    take: limit,
    orderBy: {
      createdAt: "desc",
    },
    include: {
      buyer: true,
      orderItems: {
        include: {
          product: true,
        },
      },
    },
  });

  // Transform the data to match the component's expected format
  return orders.map((order) => {
    // Calculate total from order items
    const total = order.subTotal + order.shipping;

    return {
      id: order.id.toString(),
      customer: {
        name: order.buyer.name,
        email: order.buyer.email,
        avatar: order.buyer.avatar,
      },
      status: order.status,
      total: total,
      date: order.createdAt.toISOString(),
    };
  });
}

/**
 * Gets the count of orders for a specific time period
 * @param period Time period to count orders for
 * @returns Number of orders in the specified period
 */
export async function getOrdersCount(period: "today" | "week" | "month") {
  const prisma = getPrisma();

  let dateFilter: any = {};
  const now = new Date();

  if (period === "today") {
    // Start of today
    const startOfDay = new Date(now);
    startOfDay.setHours(0, 0, 0, 0);

    dateFilter = {
      gte: startOfDay,
    };
  } else if (period === "week") {
    // Start of this week (last 7 days)
    const startOfWeek = new Date(now);
    startOfWeek.setDate(startOfWeek.getDate() - 7);

    dateFilter = {
      gte: startOfWeek,
    };
  } else if (period === "month") {
    // Start of this month
    const startOfMonth = new Date(now);
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    dateFilter = {
      gte: startOfMonth,
    };
  }

  const count = await prisma.order.count({
    where: {
      createdAt: dateFilter,
    },
  });

  return count;
}

export function auditStock(cartItems: CartItemWithProduct[]) {
  let inStock = true;
  const stockChange = [] as { productId: string; decrement: number }[];

  for (const item of cartItems) {
    const supply = item.product.supply;
    const quantity = item.quantity;

    if (quantity > supply) {
      inStock = false;
      continue;
    }

    stockChange.push({
      productId: item.productId,
      decrement: quantity,
    });
  }

  return {
    inStock,
    stockChange,
  };
}
