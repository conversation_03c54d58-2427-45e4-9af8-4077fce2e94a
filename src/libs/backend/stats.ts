import { getPrisma } from "@udoy/utils/db-utils";
import { <PERSON><PERSON><PERSON><PERSON>, DashboardStatType } from "@udoy/utils/cache-key";
import {
  unstable_cacheTag as cacheTag,
  unstable_cacheLife as cacheLife,
} from "next/cache";
import { OrderStatus } from "@prisma/client";

/**
 * Gets the total revenue from all orders
 */
export async function getTotalRevenue() {
  const prisma = getPrisma();
  const result = await prisma.orderItem.aggregate({
    _sum: {
      price: true,
    },
  });

  return result._sum.price || 0;
}

/**
 * Calculates the revenue change between current and previous month
 * @returns Object containing current month revenue and percentage change
 */
export async function getRevenueChange() {
  const prisma = getPrisma();
  const { lastMonth, twoMonthsAgo } = getMonthRanges();

  // Get revenues for comparison
  const [currentRevenue, previousRevenue] = await Promise.all([
    calculateRevenueByDateRange(prisma, lastMonth),
    calculateRevenueByDateRange(prisma, twoMonthsAgo, lastMonth),
  ]);

  // Calculate percentage change
  const percentChange = calculatePercentChange(currentRevenue, previousRevenue);

  return {
    current: currentRevenue,
    percentChange: parseFloat(percentChange.toFixed(1)),
  };
}

/**
 * Helper function to get date ranges for monthly comparisons
 */
function getMonthRanges() {
  const now = new Date();
  const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
  const twoMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 2, 1);

  return { lastMonth, twoMonthsAgo };
}

/**
 * Gets revenue for a specific time period
 */
async function calculateRevenueByDateRange(prisma: any, startDate: Date, endDate?: Date) {
  const whereClause = endDate
    ? { createdAt: { gte: startDate, lt: endDate } }
    : { createdAt: { gte: startDate } };

  const revenue = await prisma.orderItem.aggregate({
    _sum: { price: true },
    where: {
      order: whereClause,
    },
  });

  return revenue._sum.price || 0;
}

/**
 * Calculates percentage change between two values
 */
function calculatePercentChange(current: number, previous: number): number {
  if (previous <= 0) return 0;
  return ((current - previous) / previous) * 100;
}

/**
 * Gets the total number of orders
 */
export async function getTotalOrders() {
  const prisma = getPrisma();
  const result = await prisma.order.count();

  return result;
}

/**
 * Calculates the change in orders between current and previous month
 * @returns Object containing current month orders and percentage change
 */
export async function getOrdersChange() {
  const prisma = getPrisma();
  const { lastMonth, twoMonthsAgo } = getMonthRanges();

  // Get order counts for comparison
  const currentMonthOrders = await prisma.order.count({
    where: {
      createdAt: { gte: lastMonth },
    },
  });

  const previousMonthOrders = await prisma.order.count({
    where: {
      createdAt: {
        gte: twoMonthsAgo,
        lt: lastMonth,
      },
    },
  });

  // Calculate percentage change
  const percentChange = calculatePercentChange(
    currentMonthOrders,
    previousMonthOrders
  );

  return {
    current: currentMonthOrders,
    percentChange: parseFloat(percentChange.toFixed(1)),
  };
}

/**
 * Gets the average delivery time in minutes
 * @returns Average delivery time in minutes
 */
export async function getAverageDeliveryTime() {
  const prisma = getPrisma();

  // Get orders that have been delivered with their timeline
  const orders = await prisma.order.findMany({
    where: {
      status: OrderStatus.DELIVERED,
    },
    include: {
      timeline: {
        where: {
          status: {
            in: [OrderStatus.CONFIRMED, OrderStatus.DELIVERED]
          }
        }
      }
    }
  });

  // Filter orders that have both confirmed and delivered timeline entries
  const validOrders = orders.filter(order => {
    const confirmedEntry = order.timeline.find(t => t.status === OrderStatus.CONFIRMED);
    const deliveredEntry = order.timeline.find(t => t.status === OrderStatus.DELIVERED);
    return confirmedEntry && deliveredEntry;
  });

  if (validOrders.length === 0) return 0;

  // Calculate average delivery time in minutes
  const totalMinutes = validOrders.reduce((total, order) => {
    const confirmedEntry = order.timeline.find(t => t.status === OrderStatus.CONFIRMED)!;
    const deliveredEntry = order.timeline.find(t => t.status === OrderStatus.DELIVERED)!;
    const deliveryTime = deliveredEntry.createdAt.getTime() - confirmedEntry.createdAt.getTime();
    return total + deliveryTime / (1000 * 60); // Convert ms to minutes
  }, 0);

  return Math.round(totalMinutes / validOrders.length);
}

/**
 * Gets the change in average delivery time compared to previous month
 * @returns Object with current delivery time and change in minutes
 */
export async function getDeliveryTimeChange() {
  const prisma = getPrisma();
  const { lastMonth, twoMonthsAgo } = getMonthRanges();

  // Get all delivered orders with their timeline
  const orders = await prisma.order.findMany({
    where: {
      status: OrderStatus.DELIVERED,
    },
    include: {
      timeline: {
        where: {
          status: {
            in: [OrderStatus.CONFIRMED, OrderStatus.DELIVERED]
          }
        }
      }
    }
  });

  // Filter and group orders by time period
  const currentMonthOrders = [];
  const previousMonthOrders = [];

  for (const order of orders) {
    const deliveredEntry = order.timeline.find(t => t.status === OrderStatus.DELIVERED);
    const confirmedEntry = order.timeline.find(t => t.status === OrderStatus.CONFIRMED);
    
    if (!deliveredEntry || !confirmedEntry) continue;
    
    if (deliveredEntry.createdAt >= lastMonth) {
      currentMonthOrders.push({
        confirmedAt: confirmedEntry.createdAt,
        deliveredAt: deliveredEntry.createdAt
      });
    } else if (deliveredEntry.createdAt >= twoMonthsAgo && deliveredEntry.createdAt < lastMonth) {
      previousMonthOrders.push({
        confirmedAt: confirmedEntry.createdAt,
        deliveredAt: deliveredEntry.createdAt
      });
    }
  }

  // Calculate average times
  const currentAvg = calculateAverageMinutes(currentMonthOrders);
  const previousAvg = calculateAverageMinutes(previousMonthOrders);

  // Calculate change in minutes
  const changeInMinutes = previousAvg - currentAvg;

  return {
    current: currentAvg,
    changeInMinutes,
  };
}

/**
 * Helper function to calculate average delivery time in minutes
 */
function calculateAverageMinutes(orders: any[]) {
  if (orders.length === 0) return 0;

  const totalMinutes = orders.reduce((total, order) => {
    const deliveryTime =
      order.deliveredAt.getTime() - order.confirmedAt.getTime();
    return total + deliveryTime / (1000 * 60); // Convert ms to minutes
  }, 0);

  return Math.round(totalMinutes / orders.length);
}

/**
 * Gets the count of active customers (users who placed an order in the last 30 days)
 * @returns Number of active customers
 */
export async function getActiveCustomers() {
  const prisma = getPrisma();
  
  // Get date 30 days ago
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  
  // Count unique users who placed an order in the last 30 days
  const activeCustomers = await prisma.user.count({
    where: {
      orders: {
        some: {
          createdAt: {
            gte: thirtyDaysAgo
          }
        }
      }
    }
  });
  
  return activeCustomers;
}

/**
 * Gets the change in active customers compared to previous month
 * @returns Object with current active customers and percentage change
 */
export async function getCustomersChange() {
  const prisma = getPrisma();
  
  // Current period (last 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  
  // Previous period (30-60 days ago)
  const sixtyDaysAgo = new Date();
  sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);
  
  // Count active customers in current period
  const currentActiveCustomers = await prisma.user.count({
    where: {
      orders: {
        some: {
          createdAt: {
            gte: thirtyDaysAgo
          }
        }
      }
    }
  });
  
  // Count active customers in previous period
  const previousActiveCustomers = await prisma.user.count({
    where: {
      orders: {
        some: {
          createdAt: {
            gte: sixtyDaysAgo,
            lt: thirtyDaysAgo
          }
        }
      }
    }
  });
  
  // Calculate percentage change
  const percentChange = calculatePercentChange(currentActiveCustomers, previousActiveCustomers);
  
  return {
    current: currentActiveCustomers,
    percentChange: parseFloat(percentChange.toFixed(1))
  };
}

/**
 * Gets monthly revenue data for the current year
 * @returns Array of monthly revenue data
 */
export async function getMonthlyRevenue() {
  const prisma = getPrisma();
  
  // Get current date
  const now = new Date();
  const currentYear = now.getFullYear();
  
  // Array to store monthly revenue data
  const monthlyRevenue = [];
  
  // Month names for the chart
  const monthNames = [
    "Jan", "Feb", "Mar", "Apr", "May", "Jun", 
    "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
  ];
  
  // Get data for each month of the current year
  for (let month = 0; month < 12; month++) {
    const startDate = new Date(currentYear, month, 1);
    const endDate = new Date(currentYear, month + 1, 0);
    
    // Query orders for this month
    const orders = await prisma.order.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate
        }
      },
      include: {
        orderItems: true
      }
    });
    
    // Calculate total revenue for the month
    const total = orders.reduce((sum, order) => {
      return sum + order.subTotal + order.shipping;
    }, 0);
    
    // Add to monthly data
    monthlyRevenue.push({
      name: monthNames[month],
      total
    });
  }
  
  return monthlyRevenue;
}
