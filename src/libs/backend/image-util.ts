import axios from "axios";
import { URL } from "url";
import { dirname, extname, resolve } from "path";
import { Env } from "../env";
import { existsSync, mkdirSync, rmSync, writeFileSync } from "fs";
import { UTApi } from "uploadthing/server";
// http://localhost:2090/_next/image?url=/uploads/products/cloeh6e970001qf2wys5q8pnq/0.png&w=384&q=75
const utapi = new UTApi();
export class FileManager {
  static async imageUpload(image: File, path: string): Promise<string> {
    if (Env.IMAGE_STORE === "UTFS") {
      const response = await utapi.uploadFiles(image);

      if (response.error) {
        throw new Error(response.error.message);
      }

      return response.data.url;
    }
    // const response = await utapi.uploadFiles(image);

    const fileName = image.name;
    const filePath = resolve(`${Env.STATIC_DIR}/uploads`, path, fileName);

    if (!existsSync(dirname(filePath))) {
      mkdirSync(dirname(filePath), { recursive: true });
    }

    const img = await image.arrayBuffer();
    writeFileSync(filePath, new Uint8Array(img));

    const virtualPath = resolve(`/uploads`, path, fileName);

    return virtualPath;
  }

  static async imageRemove(url: string) {
    if (Env.IMAGE_STORE === "UTFS" && url.startsWith("https://")) {
      return await utapi.deleteFiles(url.split("/").at(-1)!);
    }

    const filePath = resolve(`${Env.STATIC_DIR}/${url}`);

    return rmSync(filePath);
  }
}

export async function downloadImage(url: string): Promise<File> {
  try {
    const response = await axios({
      method: "get",
      url,
      responseType: "arraybuffer",
    });

    const contentType = response.headers["content-type"];
    const mimeType = contentType
      ? contentType.split(";")[0].trim()
      : "application/octet-stream";

    const parsedUrl = new URL(url);
    const pathParts = parsedUrl.pathname.split("/");
    let filename = pathParts.pop() || "download";

    const mimeToExt: Record<string, string> = {
      "image/jpeg": "jpg",
      "image/png": "png",
      "image/gif": "gif",
      "image/webp": "webp",
      "image/svg+xml": "svg",
    };

    const currentExt = filename.split(".").pop() || "";
    const ext = mimeToExt[mimeType] || currentExt || "bin";
    filename = filename.replace(/\.[^/.]+$/, "") + `.${ext}`;

    return new File([response.data], filename, {
      type: mimeType,
      lastModified: Date.now(),
    });
  } catch (error: any) {
    throw new Error(`Failed to download image: ${error?.message}`);
  }
}
