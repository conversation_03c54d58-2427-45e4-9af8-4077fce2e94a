import { PrismaClient } from "@prisma/client";
import { getPrisma } from "@udoy/utils/db-utils";
import webpush from "web-push";

// Configure VAPID keys (you'll need to set these in your environment)
webpush.setVapidDetails(
  "mailto:<EMAIL>",
  process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY!,
  process.env.VAPID_PRIVATE_KEY!
);

interface PushPayload {
  title: string;
  body: string;
  imageUrl?: string | null;
  targetUrl?: string | null;
  iconUrl?: string | null;
  data?: any;
  notificationId?: string;
}

export async function notifyUsers(userIds: number[], payload: PushPayload) {
  const promises = userIds.map(async (userId) => {
    try {
      const success = await sendPushNotification(userId, payload);
      return { userId, success };
    } catch (error) {
      console.error(
        `Failed to send push notification to user ${userId}:`,
        error
      );
      return { userId, success: false };
    }
  });

  const results = await Promise.all(promises);
  return results;
}

export async function sendPushNotification(
  userId: number,
  payload: PushPayload
): Promise<boolean> {
  try {
    // Get user's push subscriptions
    const prisma = getPrisma();
    const subscriptions = await prisma.pushSubscription.findMany({
      where: { userId },
    });

    if (subscriptions.length === 0) {
      console.log(`No push subscriptions found for user ${userId}`);
      return false;
    }

    const pushPayload = JSON.stringify({
      title: payload.title,
      body: payload.body,
      data: payload.data || {},
      image: payload.imageUrl,
      icon: payload.iconUrl,
      url: payload.targetUrl,
      notificationId: payload.notificationId,
    });

    // Send to all user's devices
    const promises = subscriptions.map(async (subscription) => {
      try {
        await webpush.sendNotification(
          {
            endpoint: subscription.endpoint,
            keys: {
              p256dh: subscription.p256dh,
              auth: subscription.auth,
            },
          },
          pushPayload,
          {
            urgency: "high",
          }
        );
        return true;
      } catch (error: any) {
        console.error(
          `Failed to send push notification to subscription ${subscription.id}:`,
          error
        );
        // Remove invalid subscriptions
        if (error.statusCode === 410) {
          await prisma.pushSubscription
            .delete({
              where: { id: subscription.id },
            })
            .catch(() => {});
        }
        return false;
      }
    });

    const results = await Promise.all(promises);
    return results.some((result) => result === true);
  } catch (error) {
    console.error("Error sending push notification:", error);
    return false;
  }
}
