import jwt from "jsonwebtoken";
import { Env } from "../env";

export interface JwtData {
  id: number;
}

export class JWT {
  static sign(data: JwtData) {
    const token = jwt.sign(data, Env.APP_SECRET, { expiresIn: "10d" });

    return token;
  }

  static verify(token?: string) {
    try {
      return jwt.verify(token || "", Env.APP_SECRET) as { id: number };
    } catch (error) {
      return null;
    }
  }
}
