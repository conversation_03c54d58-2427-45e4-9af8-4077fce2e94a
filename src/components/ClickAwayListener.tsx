import { classUtil } from "@udoy/utils/class-util";
import { ReactElement, useEffect, useRef } from "react";

interface Props {
  children: ReactElement;
  onClickAway?: () => void;
  className?: string;
}

function ClickAwayListener({
  children,
  onClickAway = () => {},
  className,
}: Props) {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        onClickAway();
      }
    }

    document.addEventListener("click", handleClickOutside);

    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, [onClickAway]);

  return (
    <div className={classUtil(className)} ref={ref}>
      {children}
    </div>
  );
}

export default ClickAwayListener;
