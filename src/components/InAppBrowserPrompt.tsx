"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import { X, Globe } from "lucide-react";
import { useInAppBrowser } from "@udoy/hooks/useInAppBrowser";
import { Button } from "./ui/button";
import { cn } from "@udoy/utils/shadcn";
import Locale from "./Locale/Client";

const OpenInBrowserPrompt = () => {
  const { isFacebookBrowser } = useInAppBrowser();
  const [isVisible, setIsVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (isFacebookBrowser) {
      setIsVisible(true);
      // Add entrance animation
      setTimeout(() => setIsAnimating(true), 100);
    }
  }, [isFacebookBrowser]);

  const handleDismiss = () => {
    setIsAnimating(false);
    // Allow animation to complete before hiding the component for this session
    setTimeout(() => setIsVisible(false), 300);
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 right-4 z-[60] md:hidden">
      <Card
        className={cn(
          "shadow-lg border-blue-500/20 transition-all duration-300 transform",
          isAnimating ? "translate-y-0 opacity-100" : "translate-y-8 opacity-0"
        )}
      >
        <CardHeader className="pb-3">
          <div className="flex justify-between items-start">
            <div className="flex items-center gap-3">
              <div className="bg-blue-500/10 p-2 rounded-full">
                <Globe className="h-5 w-5 text-blue-500" />
              </div>
              <div>
                <CardTitle className="text-lg">
                  <Locale bn="ব্রাউজারে ওপেন করুন">Open in Your Browser</Locale>
                </CardTitle>
                <CardDescription className="text-sm">
                  <Locale bn="সেরা অভিজ্ঞতার জন্য, আপনার ব্রাউজারে ওপেন করুন।">
                    For the best experience, open in your browser.
                  </Locale>
                </CardDescription>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleDismiss}
              className="h-8 w-8"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardFooter className="flex justify-end gap-2 pt-0">
          <a
            href="intent://udoymart.com#Intent;scheme=https;end"
            target="_blank"
            className="w-full"
          >
            <Button className="text-sm h-9 w-full bg-blue-500 hover:bg-blue-600">
              Open In Browser
            </Button>
          </a>
        </CardFooter>
      </Card>
    </div>
  );
};

export default OpenInBrowserPrompt;
