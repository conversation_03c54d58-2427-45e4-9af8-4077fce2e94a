import React from "react";
import CartStatusClient from "./client";
import { <PERSON><PERSON>Util } from "@udoy/utils/cookie-util";
import { getPrisma } from "@udoy/utils/db-utils";
import { Context } from "@udoy/state/context";

async function getData() {
  const userId = await CookieUtil.userId();
  let cartId = await CookieUtil.cartId();
  const prisma = getPrisma();
  let user = null;

  if (userId) {
    user = await prisma.user.findUnique({
      where: { id: userId },
      include: { cart: true, shop: true, pushSubscriptions: true },
    });

    if (user) {
      cartId = user.cart?.id;
    }
  }

  if (!cartId) {
    return {
      user,
      cartItems: {},
      cartInfo: null,
    };
  }

  const cart = await prisma.cart.findUnique({
    where: { id: cartId },
    include: {
      items: {
        include: {
          product: {
            include: {
              images: true,
              unit: true,
            },
          },
        },
      },
    },
  });

  const data = {} as Context["cartItems"];

  for (const item of cart?.items || []) {
    data[item.productId] = {
      count: item.quantity,
      product: item.product,
    };
  }

  return { user, cartItems: data, cartInfo: user?.cart! };
}

async function SyncCart() {
  const data = await getData();
  return (
    <CartStatusClient
      cartInfo={data.cartInfo}
      cartItems={data.cartItems || []}
      user={data.user}
    />
  );
}

export default SyncCart;
