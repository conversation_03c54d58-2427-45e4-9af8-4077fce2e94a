"use client";

import { PushSubscription, User } from "@prisma/client";
import { store } from "@udoy/state";
import { Context } from "@udoy/state/context";

function SyncCartClient({
  cartInfo,
  cartItems,
  user,
}: {
  cartItems: Context["cartItems"];
  cartInfo: Context["cartInfo"];
  user: (User & { pushSubscriptions: PushSubscription[] }) | null;
}) {
  store.send({ type: "setCart", items: cartItems, info: cartInfo });
  store.send({ type: "setMe", user: user });

  return null;
}

export default SyncCartClient;
