"use client";

import { classUtil } from "@udoy/utils/class-util";
import { ReactElement, useId } from "react";
import { Label } from "./ui/label";
import { Input as InputBase } from "./ui/input";
import { cn } from "@udoy/utils/shadcn";

type InputProps = React.DetailedHTMLProps<
  React.InputHTMLAttributes<HTMLInputElement>,
  HTMLInputElement
>;

interface Props extends InputProps {
  label?: string;
}
function Input({ className, label, disabled, ...props }: Props) {
  const id = useId();

  return (
    <div className="grid w-full  gap-1.5">
      <Label className={cn("")} htmlFor={id}>
        {label}
      </Label>
      <InputBase
        className={cn(className)}
        {...props}
        disabled={disabled}
        id={id}
      />
    </div>
  );
}

export default Input;
