"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import { X, Download, Smartphone } from "lucide-react";
import { useIsMobile } from "@udoy/hooks/use-mobile";
import { usePwaInstall } from "@udoy/hooks/usePwaInstall";
import { Button } from "./ui/button";
import { cn } from "@udoy/utils/shadcn";
import Locale from "./Locale/Client";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "./ui/dialog";

const PWAInstallPrompt = () => {
  const isMobile = useIsMobile();
  const { isInstallable, handleInstall } = usePwaInstall();
  const [isVisible, setIsVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    const lastDismissed = localStorage.getItem("pwaInstallDismissed");
    if (lastDismissed) {
      const sixHoursInMillis = 6 * 60 * 60 * 1000;
      if (Date.now() - parseInt(lastDismissed, 10) < sixHoursInMillis) {
        return;
      }
    }

    if (isMobile && isInstallable) {
      setIsVisible(true);
      // Add entrance animation
      setTimeout(() => setIsAnimating(true), 100);
    }
  }, [isMobile, isInstallable]);

  const handleDismiss = () => {
    setIsAnimating(false);
    localStorage.setItem("pwaInstallDismissed", Date.now().toString());
    // Allow animation to complete before hiding
    setTimeout(() => setIsVisible(false), 300);
  };

  const onInstall = () => {
    handleInstall();
    setIsVisible(false);
  };

  return (
    <Dialog open={isVisible} onOpenChange={handleDismiss}>
      <DialogContent hideIcon className="max-w-sm rounded-md">
        <DialogHeader className="flex">
          <div className="bg-brand/10 p-2 rounded-full flex justify-between pl-3">
            <Smartphone className="h-5 w-5 text-brand animate-bounce" />

            <DialogTitle className="pt-1">
              <Locale bn="উদয় মার্ট অ্যাপ ইনস্টল করুন">
                Install UdoyMart App
              </Locale>
            </DialogTitle>

            <div className=""></div>
          </div>
        </DialogHeader>
        <div className="text-sm text-muted-foreground">
          <Locale bn="সহজে কেনাকাটা এবং নোটিফিকেশনসমূহ পাওয়ার জন্য">
            Faster shopping, offline access & notifications
          </Locale>
        </div>
        <DialogFooter className="flex flex-row gap-2 pt-2">
          <Button
            variant="outline"
            onClick={handleDismiss}
            className="text-sm h-9 flex-1"
          >
            <Locale bn="বাতিল করুন">Cancel</Locale>
          </Button>
          <Button onClick={onInstall} className=" text-sm h-9 gap-1 flex-1">
            <Download className="h-4 w-4 -mt-0.5 " />
            Install App
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default PWAInstallPrompt;
