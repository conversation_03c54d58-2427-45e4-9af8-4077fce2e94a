"use client";

import { classUtil } from "@udoy/utils/class-util";
import Hide from "./Hide";
import { IconsId } from "@udoy/assets/font-icons/icons";
import Icon from "./Icon";
import { ReactElement } from "react";
import Spinner from "./Spinner";
import { twMerge } from "tailwind-merge";
import { Button as Btn, ButtonProps } from "@udoy/components/ui/button";
import { Loader } from "lucide-react"

interface Props extends ButtonProps {
  // className?: string;
  // name?: string;
  // value?: string;
  label?: string;
  icon?: IconsId;
  iconClass?: string;
  // children?: ReactElement;
  loading?: boolean;
  // disabled?: boolean;
  // onClick?: () => void;
}

function Button({
  label,
  icon,
  iconClass,
  children,
  loading,
  ...props
}: Props) {
  return (
    <Btn {...props}>
      <Hide open={icon}>
        <Icon
          icon={icon!}
          className={classUtil(iconClass, props.disabled && "opacity-50")}
        />
      </Hide>
      {children}
      <Hide open={label}>
        <span className={classUtil("", props.disabled && "opacity-50")}>
          {label}
        </span>
      </Hide>
      <Hide open={loading}>
        <Spinner className="w-4 ml-2" />
      </Hide>
    </Btn>
  );
}

export default Button;
