"use client";

import { Key, useId } from "react";
import { twMerge } from "tailwind-merge";

interface Props<T extends string | number> {
  name?: string;
  label?: string;
  defaultSelected?: T;
  className?: string;
  onSelect?: (arg: T) => void;
  options: {
    key: T;
    label: string;
  }[];
}

function Dropdown<T extends string | number>({
  name,
  options,
  className,
  defaultSelected,
  onSelect,
  label,
}: Props<T>) {
  const id = useId();
  return (
    <div className="grid rounded">
      <label htmlFor={id}>{label}</label>
      <select
        name={name}
        id={id}
        className={twMerge(
          "bg-transparent border border-white/10 h-10 px-2 mt-1",
          className
        )}
        onChange={(e) => onSelect?.(e.target.value as any)}
      >
        {options.map((item) => (
          <option
            // onClick={() => onSelect?.(item.key)}
            key={item.key}
            value={item.key}
            selected={item.key === defaultSelected}
          >
            {item.label}
          </option>
        ))}
      </select>
    </div>
  );
}

export default Dropdown;
