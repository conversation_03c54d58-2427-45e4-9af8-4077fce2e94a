"use client";

import React, { Fragment, ReactElement, useState } from "react";
import { usePopper } from "react-popper";
import Portal from "./Portal";
import Hide from "./Hide";
import ClickAwayListener from "./ClickAwayListener";

function Popper({
  trigger,
  children,
}: {
  trigger: ReactElement;
  children: ReactElement;
}) {
  const [referenceElement, setReferenceElement] =
    useState<HTMLDivElement | null>(null);
  const [popperElement, setPopperElement] = useState<HTMLDivElement | null>(
    null
  );
  const [open, setOpen] = useState(false);
  const { styles, attributes } = usePopper(referenceElement, popperElement, {
    placement: "left",
    modifiers: [
      {
        name: "offset",
        options: {
          offset: [0, 10],
        },
      },
    ],
  });

  return (
    <Fragment>
      <div
        ref={setReferenceElement}
        onClick={() => setOpen(!open)}
        onMouseEnter={() => setOpen(true)}
        onMouseLeave={() => setOpen(false)}
      >
        {trigger}
      </div>

      <Portal>
        <Hide open={open}>
          <ClickAwayListener onClickAway={() => setOpen(false)}>
            <div
              className="bg-slate-800 rounded border-white/10 boder"
              ref={setPopperElement}
              style={styles.popper}
              {...attributes.popper}
            >
              {children}
            </div>
          </ClickAwayListener>
        </Hide>
      </Portal>
    </Fragment>
  );
}

export default Popper;
