import Button from "@udoy/components/Button";
import { classUtil } from "@udoy/utils/class-util";
import { useFormStatus } from "react-dom";

interface Props {
  label: string;
  name?: string;
  value?: string;
  className?: string;
}

function Submit({ value, label, name, className }: Props) {
  const status = useFormStatus();

  return (
    <Button
      name={name}
      value={value}
      loading={status.pending}
      className={classUtil("mx-auto mt-6 w-full ", className)}
      label={label}
    />
  );
}

export default Submit;
