import { ReactElement, ReactNode } from "react";
import ClickAwayListener from "./ClickAwayListener";
import { classUtil } from "@udoy/utils/class-util";
import Portal from "./Portal";

interface Props {
  open: boolean;
  children?: ReactNode;
  onClose?: () => void;
  className?: string;
}

function Modal({ open, onClose, children, className }: Props) {
  if (!open) {
    return null;
  }

  return (
    <Portal>
      <div className="absolute inset-0 bg-black/40 flex justify-center items-center">
        <ClickAwayListener
          className={classUtil("flex  justify-center items-center")}
          onClickAway={onClose}
        >
          <div
            className={classUtil(
              "flex-1 border-white/10 block border rounded-md p-6",
              className
            )}
          >
            {children}
          </div>
        </ClickAwayListener>
      </div>
    </Portal>
  );
}

export default Modal;
