import {
  ForwardedRef,
  Fragment,
  MutableRefObject,
  Ref,
  forwardRef,
  useMemo,
  useRef,
  useState,
} from "react";
import Input from "./Input";
import { classUtil } from "@udoy/utils/class-util";
import Icon from "./Icon";
import Hide from "./Hide";
import Image from "next/image";
import { ProductImage } from "@prisma/client";

function getImageUrl(source: File | string) {
  if (typeof source === "string") {
    return source;
  }

  return URL.createObjectURL(source);
}

interface Props {
  images?: string[];
  onRemove?: (url: string) => void;
}

const ImagePicker = forwardRef(
  ({ images, onRemove }: Props, ref: ForwardedRef<(File | string)[]>) => {
    // const ref = useRef(null);
    const [index, setIndex] = useState(0);
    const [state, setState] = useState<(File | string)[]>(images || []);
    const { image, previews } = useMemo(() => {
      const selected = state[index];
      if (typeof ref === "object" && ref?.current) {
        ref.current = state;
      }

      // if (typeof selected === 'string') {
      //   return
      // }

      return {
        image: selected && getImageUrl(selected),
        previews: state.map(getImageUrl),
      };
    }, [index, state]);

    function handleDelete() {
      const selected = state[index];
      setState((pre) => pre.filter((_, i) => index !== i));
      onRemove && typeof selected === "string" && onRemove(selected);
    }

    return (
      <div className=" flex flex-col w-full h-full">
        <div className="border group relative border-white/10 rounded p-1 flex-1 w-full grid place-content-center">
          <Hide
            open={image}
            fallback={<Icon className="text-9xl opacity-30" icon="image" />}
          >
            <Fragment>
              <button
                onClick={handleDelete}
                type="button"
                className="absolute group-hover:opacity-100 active:!opacity-50 cursor-pointer  opacity-0 top-4 right-4"
              >
                <Icon icon="close" className="text-5xl pointer-events-none" />
              </button>
              <Image width={300} height={300} alt="nO apt" src={image} />
            </Fragment>
          </Hide>
        </div>
        <div className="flex gap-3 mt-5">
          {previews.map((image, i) => (
            <div
              key={i}
              className={classUtil(
                "border-white/30 border p-2 rounded active:opacity-50 cursor-pointer",
                i === index && "!border-blue-600"
              )}
              onClick={() => setIndex(i)}
            >
              <Image alt="Imae" width={60} height={60} src={image} />
            </div>
          ))}

          <div className="border border-white/10 cursor-pointer h-full relative rounded grid place-items-center w-24 h-16 ">
            <Icon
              icon="plus-circle"
              className="text-3xl opacity-20 pointer-events-none cursor-pointer"
            />
            <input
              className="opacity-0 absolute cursor-pointer w-full h-full"
              type="file"
              onChange={(e) =>
                setState((pre) => [...pre, ...Array.from(e.target.files || [])])
              }
              multiple
            />
          </div>
        </div>
      </div>
    );
  }
);

ImagePicker.displayName = "ImagePicker";

export default ImagePicker;
