"use client";

import { useState, useEffect, Fragment } from "react";
import {
  Card,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import { X, <PERSON>, Edit } from "lucide-react";
import { Button } from "./ui/button";
import { cn } from "@udoy/utils/shadcn";
import Locale from "./Locale/Client";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "./ui/dialog";
import { useWindowSize } from "react-use";
import { subscribeUser } from "../actions/notification";
import useUser from "@udoy/hooks/useUser";
import { useSelector } from "@xstate/store/react";
import { store } from "@udoy/state";

function NotificationCard({
  isAnimating,
  handleDismiss,
  handleSubscribe,
}: {
  isAnimating: boolean;
  handleDismiss: () => void;
  handleSubscribe: () => void;
}) {
  return (
    <Card
      className={cn(
        "shadow-lg border-purple-500/20 transition-all duration-300 transform",
        isAnimating ? "translate-y-0 opacity-100" : "translate-y-8 opacity-0"
      )}
    >
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-3">
            <div className="bg-purple-500/10 p-2 rounded-full">
              <Bell className="h-5 w-5 text-purple-500" />
            </div>
            <div>
              <CardTitle className="text-lg">
                <Locale bn="নোটিফিকেশন চালু করুন">Get Notifications</Locale>
              </CardTitle>
              <CardDescription className="text-sm">
                <Locale bn="অর্ডার আপডেট, মূল্য ছাড় অফার এবং অন্যান্য প্রয়োজনীয় সকল তথ্য সহজে পেতে নোটিফিকেশন চালু করুন।">
                  Enable notifications for offers and order updates.
                </Locale>
              </CardDescription>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleDismiss}
            className="h-8 w-8"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardFooter className="flex justify-end gap-2 pt-0">
        <Button
          onClick={handleSubscribe}
          className="text-sm h-9 w-full bg-purple-500 hover:bg-purple-600"
        >
          <Locale bn="চালু করুন">Enable Notifications</Locale>
        </Button>
      </CardFooter>
    </Card>
  );
}

const PushNotificationPrompt = () => {
  const [isVisible, setIsVisible] = useState(false);
  const me = useSelector(store, (state) => state.context.me);

  useEffect(() => {
    if ("serviceWorker" in navigator && "PushManager" in window && me) {
      // Force update service worker on each load
      navigator.serviceWorker.getRegistrations().then(registrations => {
        registrations.forEach(registration => registration.update());
      });
      registerServiceWorker();
    }
  }, [me]);

  async function registerServiceWorker() {
    const registration = await navigator.serviceWorker.register("/sw.js", {
      scope: "/",
      updateViaCache: "none",
    });
    const sub = await registration.pushManager.getSubscription();

    if (sub) {
      const exist = me?.pushSubscriptions.find(
        (sub) => sub.endpoint === sub.endpoint
      );
      if (!exist) {
        const result = await subscribeUser(sub);
        if (result.success) {
          return;
        }
      } else {
        return;
      }
    }

    const lastDismissed = localStorage.getItem("pushNotificationDismissed");
    if (lastDismissed) {
      const sixHoursInMillis = 6 * 60 * 60 * 1000;
      if (Date.now() - parseInt(lastDismissed, 10) < sixHoursInMillis) {
        return;
      }
    }

    // Check if permission is granted
    const permission = Notification.permission;
    if (permission === "denied") {
      return;
    }

    setIsVisible(true);
  }

  const handleDismiss = () => {
    // Store dismissal time
    localStorage.setItem("pushNotificationDismissed", Date.now().toString());
    // Allow animation to complete before hiding
    setIsVisible(false);
  };

  const handleSubscribe = async () => {
    try {
      const registration = await navigator.serviceWorker.ready;

      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(
          process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY!
        ),
      });

      // Store subscription in database
      const result = await subscribeUser(subscription);

      if (result.success) {
        handleDismiss();
      } else {
        console.error("Failed to store subscription:", result.error);
      }
    } catch (error) {
      console.error("Failed to subscribe:", error);
    }
  };

  // Helper function from your PushNotificationManager
  function urlBase64ToUint8Array(base64String: string) {
    const padding = "=".repeat((4 - (base64String.length % 4)) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, "+")
      .replace(/_/g, "/");
    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);
    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }

  return (
    <Fragment>
      <Dialog open={isVisible} onOpenChange={handleDismiss}>
        <DialogContent hideIcon className="max-w-sm rounded-md">
          <DialogHeader className="flex">
            <div className="bg-purple-500/10 p-2 rounded-full flex justify-between pl-3">
              <Bell className="h-5 w-5 text-purple-500" />
              <DialogTitle className="pt-1">
                <Locale bn="নোটিফিকেশন চালু করুন">Get Notifications</Locale>
              </DialogTitle>
              <div className=""></div>
            </div>
          </DialogHeader>
          <div className="text-sm text-muted-foreground">
            <Locale bn="অর্ডার আপডেট, মূল্য ছাড় অফার এবং অন্যান্য প্রয়োজনীয় সকল তথ্য সহজে পেতে নোটিফিকেশন চালু করুন।">
              Enable notifications for offers and order updates.
            </Locale>
          </div>
          <DialogFooter className="flex flex-row gap-2">
            <Button
              tabIndex={-1}
              variant="outline"
              onClick={handleDismiss}
              className="text-sm h-9 flex-1"
            >
              <Locale bn="বাতিল করুন">Cancel</Locale>
            </Button>
            <Button
              onClick={handleSubscribe}
              className="text-sm h-9 w-full bg-purple-500 hover:bg-purple-600 flex-1"
            >
              <Locale bn="চালু করুন">Enable Notifications</Locale>
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Fragment>
  );
};

export default PushNotificationPrompt;
