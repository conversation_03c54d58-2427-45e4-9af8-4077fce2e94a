"use client";

import { useControlledState } from "@udoy/hooks/useControlledState";
import { classUtil } from "@udoy/utils/class-util";
import { useId, useState } from "react";

function Switch({
  defaultChecked = false,
  name,
  label,
  checked: checkedProp = defaultChecked,
  onChange,
}: {
  label?: string;
  name?: string;
  defaultChecked?: boolean;
  checked?: boolean;
  onChange?: (arg: boolean) => void;
}) {
  const [checked, setChecked] = useControlledState(
    Boolean(checkedProp),
    onChange
  );
  const id = useId();

  return (
    <label
      htmlFor={id}
      className="cursor-pointer flex justify-between items-center mt-3"
    >
      <span>{label}</span>
      <input
        name={name}
        id={id}
        checked={checked}
        type="checkbox"
        className="invisible absolute pointer-events-none"
        onChange={(e) => setChecked(e.target.checked)}
      />
      <div
        className={classUtil(
          "switch border border-white/10",
          checked && "active"
        )}
      >
        <div className=" "></div>
      </div>
    </label>
  );
}

export default Switch;
