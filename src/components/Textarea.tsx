"use client";

import { classUtil } from "@udoy/utils/class-util";
import { ReactElement, useId } from "react";

type InputProps = React.DetailedHTMLProps<
  React.InputHTMLAttributes<HTMLTextAreaElement>,
  HTMLTextAreaElement
>;

interface Props extends InputProps {
  label?: string;
  //   wraperClass?: string;
  //   children?: ReactElement;
}
function Textarea({ className, label, ...props }: Props) {
  const id = useId();

  return (
    <div className="flex flex-col">
      <label htmlFor={id}>{label}</label>
      <textarea
        className={classUtil(
          "bg-transparent border border-white/10 rounded mt-1 outline-none px-2 py-2",
          className
        )}
        {...props}
        id={id}
      />
    </div>
  );
}

export default Textarea;
