'use client';

import { createContext, useContext } from "react";

const LanguageContext = createContext("en" as "en" | "bn");

export function useLanguage() {
  return useContext(LanguageContext);
}

function LanguageProvider({
  children,
  lang,
}: {
  children: React.ReactNode;
  lang: "en" | "bn";
}) {
  return (
    <LanguageContext.Provider value={lang}>{children}</LanguageContext.Provider>
  );
}

export default LanguageProvider;
