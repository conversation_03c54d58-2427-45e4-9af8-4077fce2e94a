import { createStoreWithProducer } from "@xstate/store";
import { produce } from "immer";
import { context, dbContext } from "./context";
import { dbEvents, events } from "./events";

export const store = createStoreWithProducer(produce, {
  context,
  on: events,
});

export const dbStore = createStoreWithProducer(produce, {
  context: dbContext,
  on: dbEvents,
});

// const sub = store.inspect((inspectionEvent) => {
//   console.log(inspectionEvent);
// });
