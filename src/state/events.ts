import {
  Address,
  Order,
  Product,
  PushSubscription,
  User,
} from "@prisma/client";
import { Context, DbStore } from "./context";
import { AddressWithZone, OrderWithItems } from "@udoy/utils/types";

export const events = {
  updateActivePath: (context: Context, event: { path: string[] }) => {
    context.activePath = event.path;
  },

  toggleSidebar: (context: Context) => {
    context.sideBarOpen = !context.sideBarOpen;
  },

  addToCart: (context: Context, event: { product: Product }) => {
    const id = event.product.id;

    if (context.cartItems[id]) {
      context.cartItems[id].count++;
    } else {
      context.cartItems[id] = {
        count: 1,
        product: event.product,
      };
    }
  },

  removeFromCart: (context: Context, event: { productId: string }) => {
    const id = event.productId;

    if (context.cartItems[id]) {
      context.cartItems[id].count--;
    }

    if (context.cartItems[id]?.count === 0) {
      delete context.cartItems[id];
    }
  },

  setCart: (
    context: Context,
    event: { items: Context["cartItems"]; info: Context["cartInfo"] }
  ) => {
    context.cartItems = event.items;
    context.cartInfo = event.info;
  },

  clearCartItems: (context: Context) => {
    context.cartItems = {};
  },

  toggleCart: (context: Context, event: { open?: boolean }) => {
    if (event.open !== undefined) {
      context.cartViewOpen = event.open;
    } else {
      context.cartViewOpen = !context.cartViewOpen;
    }
  },

  setAddresses: (context: Context, event: { addresses: any }) => {
    context.addresses = event.addresses;
  },
  setZones: (context: Context, event: { zones: any }) => {
    context.zones = event.zones;
  },

  setMe: (
    context: Context,
    event: {
      user:
        | (User & { shop?: null; pushSubscriptions: PushSubscription[] })
        | null;
    }
  ) => {
    context.me = event.user;
  },

  addNewAddress(context: Context, event: { address: AddressWithZone }) {
    context.addresses.unshift(event.address);
    if (context.cartInfo) {
      context.cartInfo.addressId = event.address.id;
    }
  },

  updateAddress(context: Context, event: { address: AddressWithZone }) {
    context.addresses = context.addresses.map((address) => {
      if (address.id === event.address.id) {
        return event.address;
      }
      return address;
    });
  },

  changeAddress(context: Context, event: { addressId: string }) {
    if (context.cartInfo) {
      context.cartInfo.addressId = event.addressId;
    }
  },

  deleteAddress(context: Context, event: { addressId: string }) {
    context.addresses = context.addresses.filter(
      (address) => address.id !== event.addressId
    );
  },

  setLoginPopup(context: Context, event: { open: boolean }) {
    context.loginPopup = event.open;
  },

  setOrders(
    context: Context,
    event: { orders: (OrderWithItems & { address: Address })[] }
  ) {
    context.orders = event.orders;
  },
};

export const dbEvents = {
  setDeliveryMen(context: DbStore, event: { deliveryMen: User[] }) {
    context.deliveryMen = event.deliveryMen;
  },
};
