import {
  Address,
  Cart,
  Product,
  PushSubscription,
  Shop,
  User,
} from "@prisma/client";
import {
  AddressWithZone,
  OrderWithItems,
  ZoneWithSubzones,
} from "@udoy/utils/types";

export const context = {
  activePath: [] as string[],
  sideBarOpen: false,
  cartInfo: null as Cart | null,
  cartItems: {} as Record<string, { count: number; product: Product }>,
  cartViewOpen: false,
  addresses: [] as AddressWithZone[],
  zones: [] as ZoneWithSubzones[],
  me: null as (User & { shop?: Shop | null; pushSubscriptions: PushSubscription[] }) | null,
  loginPopup: false,
  orders: [] as (OrderWithItems & { address: Address })[],
};

export type Context = typeof context;

export const dbContext = {
  deliveryMen: [] as User[],
};

export type DbStore = typeof dbContext;
