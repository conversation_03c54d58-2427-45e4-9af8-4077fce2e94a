"use client";

import { useSelector } from "@xstate/store/react";
import isEqual from "lodash/isEqual";
import { store } from ".";

export const useCart = () => {
  const cart = useSelector(
    store,
    (state) => {
      let totalPrice = 0;
      let itemsCount = 0;
      let totalDiscount = 0;
      const items = [];
      const address = state.context.addresses.find(
        (address) => address.id === state.context.cartInfo?.addressId
      );

      for (const { count, product } of Object.values(state.context.cartItems)) {
        totalPrice += product.price * count;
        totalDiscount += product.discount * count;
        itemsCount++;
        items.push({ count, product });
      }

      return {
        items,
        discountedPrice: totalPrice - totalDiscount,
        totalPrice,
        totalDiscount,
        itemsCount,
        deliveryFee: address?.zone.charge || 0,
      };
    },
    isEqual
  );

  return cart;
};
