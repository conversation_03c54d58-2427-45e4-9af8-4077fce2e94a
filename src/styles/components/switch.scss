.switch {
  --bg-active-color: #f0f1f2d4;
  --bg-inactive-color: #363636;

  position: relative;
  width: 60px;
  height: 28px;
  border-radius: 99px;

  //   background: #000;
  // display: block;

  //   &::before {
  //     content: "" !important;
  //     position: absolute !important;
  //     inset: -1px;
  //     background: radial-gradient(87.23% 87.23% at 50% 12.77%, #8E8E8E 0%, rgba(81, 81, 81, 0) 100%);

  //     // z-index: -1;
  //     display: block !important;
  //     border-radius: inherit;
  //     transition: all 150ms;
  //   }
  background: var(--bg-inactive-color);
  //    z-index: 9999;
  &::after {
    content: "" !important;
    position: absolute !important;
    inset: 0px;
    // z-index: -1;
    display: block !important;
    border-radius: inherit;
    transition: all 150ms ease-in;
    opacity: 0;
  }

  & > div {
    width: 22px;
    height: 22px;
    position: absolute;
    background: gray;
    z-index: 10;
    border-radius: 99px;
    top: 50%;
    transform: translateY(-50%) translateX(4px);
    transition: all 150ms ease-in;
  }
  &.active {
    & > div {
      transform: translateY(-50%) translateX(calc(60px - 100% - 4px));
      background-color: #2654ec;
    }

    &::after {
      background: var(--bg-active-color);
      opacity: 1;
    }
  }
}
