"use server";

import { AddressInputSchema, ZoneInputSchema } from "@udoy/libs/zod-schema";
import { ActionError } from "@udoy/utils/app-error";
import { CookieUtil } from "@udoy/utils/cookie-util";
import { getPrisma } from "@udoy/utils/db-utils";
import { z } from "zod";

export async function zoneAction(_: any, form: FormData) {
  const { action, zoneId, ...data } = ZoneInputSchema.parse({
    name: form.get("name"),
    charge: form.get("charge"),
    isBase: form.get("isBase"),
    action: form.get("action"),
    parentId: form.get("parentId"),
    slug: form.get("slug"),
    zoneId: form.get("zoneId"),
  });

  const prisma = getPrisma();

  if (action === "create") {
    const zone = await prisma.deliveryZone.create({
      data,
    });

    return console.log(zone);
  }

  if (!zoneId) {
    return ActionError("Invalid ZoneID");
  }

  if (action === "remove") {
    return await prisma.deliveryZone.delete({ where: { id: zoneId } });
  }

  if (action === "update") {
    const { parentId, ...filtered } = data;
    return await prisma.deliveryZone.update({
      where: { id: zoneId },
      data: filtered,
    });
  }
}

export async function createAddress(data: AddressInputSchema) {
  try {
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("User Not Logged In");
    }

    const { zoneId, name, home, phone, label, location } =
      AddressInputSchema.parse(data);

    const prisma = getPrisma();

    const cart = await prisma.cart.update({
      where: {
        userId,
      },
      data: {
        address: {
          create: {
            home,
            name,
            label,
            phone,
            location,
            userId,
            zoneId,
          },
        },
      },
      include: {
        address: {
          include: {
            zone: true,
          },
        },
      },
    });

    return cart.address;
  } catch (error: any) {
    return ActionError(error?.message || "Failed To Add Address");
  }
}

export async function cahngeUserAddress(addressId: string) {
  try {
    const userId = await CookieUtil.userId();
    const addressIdParsed = z.string().parse(addressId);

    if (!userId) {
      return ActionError("Your are not logged in");
    }

    const prisma = getPrisma();

    prisma.cart.update({
      where: {
        userId,
      },
      data: {
        addressId: addressIdParsed,
      },
    });
  } catch (error) {
    return ActionError("Failed To Change User Address");
  }
}

export async function deleteUserAddress(addressId: string) {
  try {
    const userId = await CookieUtil.userId();
    const addressIdParsed = z.string().parse(addressId);

    if (!userId) {
      return ActionError("Your are not logged in");
    }

    const prisma = getPrisma();

    const address = await prisma.address.findUnique({
      where: {
        id: addressIdParsed,
        userId,
      },
      include: {
        cart: {
          // Include related carts
          select: { id: true },
        },
      },
    });

    if (!address) return ActionError("Address not found");
    if (address.cart.length > 0) {
      return ActionError("Address is linked to existing carts");
    }

    const deleted = await prisma.address.delete({
      where: { id: addressIdParsed },
    });

    return Boolean(deleted);
  } catch (error) {
    console.log(error);
    return ActionError("Failed To Delete User Address");
  }
}

export async function updateAddress(addressId: string, data: any) {
  try {
    const userId = await CookieUtil.userId();
    const addressIdParsed = z.string().parse(addressId);

    if (!userId) {
      return ActionError("Your are not logged in");
    }

    const { zoneId, name, home, phone, label, location } =
      AddressInputSchema.parse(data);

    const prisma = getPrisma();

    const address = await prisma.address.update({
      where: {
        id: addressIdParsed,
        userId,
      },
      data: {
        home,
        name,
        label,
        phone,
        location,
        zoneId,
      },
      include: {
        zone: true,
      },
    });

    return address;
  } catch (error) {
    return ActionError("Failed To Update User Address");
  }
}
