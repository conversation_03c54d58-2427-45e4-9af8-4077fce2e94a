"use server";

import { getPrisma } from "@udoy/utils/db-utils";
import { Role } from "@prisma/client";
import { revalidatePath } from "next/cache";

/**
 * Updates the role of one or more users
 * @param userIds Array of user IDs to update
 * @param role New role to assign
 */
export async function updateUserRoles(userIds: number[], role: Role) {
  try {
    const prisma = getPrisma();
    
    // Update all users in the list
    await prisma.user.updateMany({
      where: {
        id: {
          in: userIds
        }
      },
      data: {
        role
      }
    });
    
    // Revalidate the customers page to reflect changes
    revalidatePath("/dashboard/customers");
    
    return { success: true };
  } catch (error) {
    console.error("Failed to update user roles:", error);
    return { success: false, error: "Failed to update user roles" };
  }
}