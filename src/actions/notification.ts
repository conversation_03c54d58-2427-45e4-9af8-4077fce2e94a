"use server";

import webpush from "web-push";
import { getPrisma } from "@udoy/utils/db-utils";
import { cookies } from "next/headers";
import { <PERSON>ieUtil } from "@udoy/utils/cookie-util";

webpush.setVapidDetails(
  "mailto:<EMAIL>",
  process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY!,
  process.env.VAPID_PRIVATE_KEY!
);

export async function subscribeUser(sub: PushSubscriptionJSON) {
  try {
    const prisma = getPrisma();
    const userId = await CookieUtil.userId();

    if (!userId) {
      return { success: false, error: "User not authenticated" };
    }

    const existingSubscription = await prisma.pushSubscription.findFirst({
      where: {
        userId,
        endpoint: sub.endpoint!,
      },
    });

    if (existingSubscription) {
      return { success: true };
    }

    // Store subscription in database
    await prisma.pushSubscription.create({
      data: {
        userId,
        endpoint: sub.endpoint!,
        p256dh: sub.keys?.p256dh!,
        auth: sub.keys?.auth!,
      },
    });

    return { success: true };
  } catch (error) {
    console.error("Error storing subscription:", error);
    return { success: false, error: "Failed to store subscription" };
  }
}

export async function unsubscribeUser(endpoint: string) {
  try {
    const prisma = getPrisma();
    const userId = await CookieUtil.userId();

    if (!userId) {
      return { success: false, error: "User not authenticated" };
    }

    // Remove subscription from database
    await prisma.pushSubscription.deleteMany({
      where: {
        userId,
        endpoint,
      },
    });

    return { success: true };
  } catch (error) {
    console.error("Error removing subscription:", error);
    return { success: false, error: "Failed to remove subscription" };
  }
}

// export async function sendNotification(message: string) {
//   if (!subscription) {
//     throw new Error("No subscription available");
//   }

//   console.log({ subscription });

//   try {
//     await webpush.sendNotification(
//       subscription as any,
//       JSON.stringify({
//         title: "বিশল মূল্য ছাড়",
//         body: "আপনার পছন্দের পণ্যের মূল্য ছাড়া আছে! অর্ডার করুন আজই!",
//         icon: "https://udoymart.com/icon-192x192.png",
//         image:
//           "https://udoymart.com/_next/image?url=%2Fuploads%2Fproducts%2Fgmz3e05eo1icrjlaema2q88i.jpeg&w=640&q=75",
//       })
//     );
//     return { success: true };
//   } catch (error) {
//     console.error("Error sending push notification:", error);
//     return { success: false, error: "Failed to send notification" };
//   }
// }
