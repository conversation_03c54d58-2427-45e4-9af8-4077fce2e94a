"use server";

import { User, Role } from "@prisma/client";
import { ActionError } from "@udoy/utils/app-error";
import { CookieUtil } from "@udoy/utils/cookie-util";
import { getPrisma } from "@udoy/utils/db-utils";
import { revalidatePath } from "next/cache";

export async function changeRole(userId: number, role: Role) {
  try {
    const adminId = await CookieUtil.userId();

    if (!adminId) {
      return ActionError("Unauthorized");
    }

    if (adminId === userId) {
      return ActionError("Can't change own role");
    }

    const prsima = getPrisma();
    const user = await prsima.user.findUnique({ where: { id: adminId } });

    if (!user) {
      return ActionError("Unauthorized");
    }

    if (role === "SUPER_ADMIN") {
      return ActionError("Super Admin can't be changed");
    }

    if (user.role !== "SUPER_ADMIN") {
      return ActionError("Unauthorized");
    }

    const resp = await prsima.user.update({
      where: { id: userId },
      data: { role },
    });

    revalidatePath("/dashboard/customers");

    return Boolean(resp);
  } catch (error) {
    console.log(error);
    return ActionError("Failed to change role");
  }
}
