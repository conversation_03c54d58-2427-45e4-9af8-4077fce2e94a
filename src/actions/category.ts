"use server";

import { FileManager } from "@udoy/libs/backend/image-util";
import { ActionError } from "@udoy/utils/app-error";
import { createSmallId } from "@udoy/utils/cuid";
import { getPrisma } from "@udoy/utils/db-utils";
import { zImage } from "@udoy/utils/zod";
import { revalidateTag } from "next/cache";
import { z } from "zod";

const CategorySchema = z.object({
  name: z.string().min(3),
  nam: z.nullable(z.string()),
  slug: z.string().min(3),
  isBase: z.nullable(z.string()).transform((arg) => arg === "on"),
  parentId: z.string().cuid().nullable(),
  image: zImage.nullable(),
  categoryId: z.string().nullable(),
  action: z.enum(["create", "remove", "update"]),
});

export async function categoryAction(_: any, data: FormData) {
  const { name, nam, slug, isBase, parentId, image, action, categoryId } =
    CategorySchema.parse({
      name: data.get("name"),
      nam: data.get("nam"),
      slug: data.get("slug"),
      categoryId: data.get("categoryId"),
      isBase: data.get("isBase"),
      parentId: data.get("parentId"),
      image: data.get("image"),
      action: data.get("action"),
    });

  const prisma = getPrisma();

  if (action === "remove") {
    const category = await prisma.category.findUnique({
      where: { id: categoryId || undefined },
    });

    if (!category) {
      return ActionError("Invalid category Id");
    }

    await prisma.category.delete({ where: { id: category.id } });
    if (category.image) {
      await FileManager.imageRemove(category.image);
    }

    return;
  }

  if (parentId) {
    const parentCtg = await prisma.category.findUnique({
      where: { id: parentId },
    });

    if (parentCtg?.isBase) {
      return ActionError("Subcategory Not Allowed");
    }
  }

  // const prisma = getPrisma();
  const category = await prisma.category.upsert({
    where: { id: categoryId || "" },
    create: {
      name,
      nam,
      slug,
      isBase,
      parentId,
    },
    update: {
      name,
      nam,
      slug,
      isBase,
      // parentId,
    },
  });

  if (image) {
    const id = createSmallId();
    if (category.image) {
      await FileManager.imageRemove(category.image);
    }
    const url = await FileManager.imageUpload(image, `categories/${id}`);

    await prisma.category.update({
      where: { id: category.id },
      data: { image: url },
    });
  }

  revalidateTag("sidebar-categories");
}
