import { QuantityUnit } from "@prisma/client";
import { localeNumber } from ".";

export class UnitUtil {
  static getAmountUnit(
    am: number,
    unit: QuantityUnit,
    locale?: string
  ): string {
    const isBangla = locale === "bn";
    let unitLabel = isBangla
      ? unit?.bangla || unit.slug.toLowerCase()
      : unit.slug.toLowerCase();
    let amount = am;

    if (unit.slug === "GM") {
      if (amount >= 1000) {
        amount = amount / 1000;
        unitLabel = isBangla ? "কেজি" : "kg";
      } else {
        unitLabel = isBangla ? "গ্রাম" : "gm";
      }
    }

    if (unit.slug === "ML") {
      if (amount >= 1000) {
        amount = amount / 1000;
        unitLabel = isBangla ? "লিটার" : "ltr";
      } else {
        unitLabel = isBangla ? "মিলি" : "ml";
      }
    }

    if (unit.slug === "PCS") {
      unitLabel = isBangla ? "টি" : "pcs";
    }

    return `${amount.toLocaleString(locale)} ${unitLabel}`;
  }
}
