export function localeNumber(num: number, locale: string) {
  const intl = new Intl.NumberFormat(locale);
  return intl.format(num);
}

export function isValidPlusCode(code?: string) {
  if (!code) {
    return false;
  }
  const SEPARATOR = "+";
  const SEPARATOR_POSITION = 8;
  const PADDING_CHARACTER = "0";
  const CODE_ALPHABET = "23456789CFGHJMPQRVWX";
  // The separator is required.
  if (code.indexOf(SEPARATOR) == -1) {
    return false;
  }
  if (code.indexOf(SEPARATOR) != code.lastIndexOf(SEPARATOR)) {
    return false;
  }
  // Is it the only character?
  if (code.length == 1) {
    return false;
  }
  // Is it in an illegal position?
  if (
    code.indexOf(SEPARATOR) > SEPARATOR_POSITION ||
    code.indexOf(SEPARATOR) % 2 == 1
  ) {
    return false;
  }
  // We can have an even number of padding characters before the separator,
  // but then it must be the final character.
  if (code.indexOf(PADDING_CHARACTER) > -1) {
    // Short codes cannot have padding
    if (code.indexOf(SEPARATOR) < SEPARATOR_POSITION) {
      return false;
    }
    // Not allowed to start with them!
    if (code.indexOf(PADDING_CHARACTER) == 0) {
      return false;
    }
    // There can only be one group and it must have even length.
    var padMatch = code.match(new RegExp("(" + PADDING_CHARACTER + "+)", "g"));
    if (
      padMatch?.length! > 1 ||
      padMatch?.[0].length! % 2 == 1 ||
      padMatch?.[0].length! > SEPARATOR_POSITION - 2
    ) {
      return false;
    }
    // If the code is long enough to end with a separator, make sure it does.
    if (code.charAt(code.length - 1) != SEPARATOR) {
      return false;
    }
  }
  // If there are characters after the separator, make sure there isn't just
  // one of them (not legal).
  if (code.length - code.indexOf(SEPARATOR) - 1 == 1) {
    return false;
  }

  // Strip the separator and any padding characters.
  code = code
    .replace(new RegExp("\\" + SEPARATOR + "+"), "")
    .replace(new RegExp(PADDING_CHARACTER + "+"), "");
  // Check the code contains only valid characters.
  for (var i = 0, len = code.length; i < len; i++) {
    var character = code.charAt(i).toUpperCase();
    if (character != SEPARATOR && CODE_ALPHABET.indexOf(character) == -1) {
      return false;
    }
  }
  return true;
}

export function vibrate(duration = 100) {
  try {
    if (typeof navigator.vibrate === "function") {
      navigator.vibrate(duration);
    }
  } catch (error) {}
}
