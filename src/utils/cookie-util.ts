import { JWT } from "@udoy/libs/auth/jwt";
import { cookies, type UnsafeUnwrappedCookies } from "next/headers";
import { z } from "zod";

export class CookieUtil {
  static async userId() {
    const cookieStore = await cookies();
    const token = cookieStore.get("token")?.value;

    if (!token) {
      return null;
    }

    const info = JWT.verify(token);

    return info && info.id;
  }

  static async cartId() {
    const cookieStore = await cookies();
    const cartId = cookieStore.get("cartId")?.value;
  
    const result = z.string().cuid().safeParse(cartId);
    return result.success ? result.data : undefined;
  }

  static async isBangla() {
    const cookieStore = await cookies();
    const lang = cookieStore.get("lang")?.value;

    return lang === "bn";
  }
}
