import { PrismaClient } from "@prisma/client";
import { JWT } from "@udoy/libs/auth/jwt";
import { cookies } from "next/headers";
import { CookieUtil } from "./cookie-util";

const prisma = new PrismaClient();

export function getPrisma() {
  return prisma;
}

export async function getUser(id?: number | null) {
  if (!id) {
    return null;
  }

  return await getPrisma().user.findUnique({ where: { id } });
}
