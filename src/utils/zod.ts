import {
  <PERSON>od<PERSON><PERSON><PERSON>,
  Zod<PERSON><PERSON>ber,
  ZodObject,
  ZodOptional,
  ZodRawShape,
  object,
  z,
} from "zod";
import { FormError } from "./form-error";

const MB_BYTES = 1000000; // Number of bytes in a megabyte.
const ACCEPTED_MIME_TYPES = [
  "image/gif",
  "image/jpeg",
  "image/png",
  "image/webp",
];
export const zImage = z.custom<File>().superRefine((f, ctx) => {
  // First, add an issue if the mime type is wrong.

  if (!f.size) {
    return null;
  }

  if (!ACCEPTED_MIME_TYPES.includes(f.type)) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: `File must be one of [${ACCEPTED_MIME_TYPES.join(
        ", "
      )}] but was ${f.type}`,
    });
  }
  // Next add an issue if the file size is too large.
  if (f.size > 4 * MB_BYTES) {
    ctx.addIssue({
      code: z.ZodIssueCode.too_big,
      type: "array",
      message: `The file must not be larger than ${3 * MB_BYTES} bytes: ${
        f.size
      }`,
      maximum: 3 * MB_BYTES,
      inclusive: true,
    });
  }
});

export function parseForm<T extends ZodRawShape>(
  schema: ZodObject<T>,
  form: FormData
) {
  const keys = Object.keys(schema.shape);
  const data: Record<string, any> = {};

  for (let key of keys) {
    const value = form.get(key);
    let validator = schema.shape[key];

    if (validator instanceof ZodOptional) {
      validator = validator._def.innerType;
    }

    if (value) {
      if (validator instanceof ZodNumber) {
        data[key] = Number(value);
        continue;
      }

      if (validator instanceof ZodArray) {
        data[key] = form.getAll(key);
        continue;
      }

      data[key] = value;
    }
  }

  return data;
}

export const phoneSchema = z
  .string()
  // Step 1: Remove all non-digit characters (e.g., spaces, +, -)
  .transform((val) => val.replace(/[^\d]/g, ""))
  // Step 2: Check length and operator prefix
  .refine(
    (val) => {
      // Allow international format (e.g., +88017... becomes 88017...)
      if (val.startsWith("880") && val.length === 13) {
        val = "0" + val.slice(3); // Convert to local format (880171... → 0171...)
      }
      // Validate 11 digits starting with 01[3-9]
      return /^01[3-9]\d{8}$/.test(val);
    },
    {
      message: FormError.InvalidMobileNumber,
    }
  );
