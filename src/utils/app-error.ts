export interface ActionErrorType {
  error: {
    message: string;
    appError: true;
  };
}

export function ActionError(message: string): ActionErrorType {
  return { error: { message, appError: true } };
}

export function isActionError(result: any): result is ActionErrorType {
  if (result?.error?.appError) {
    return true;
  }

  return false;
}

export async function withError<T>(
  action: Promise<T>,
  message?: string
): Promise<Exclude<T, ActionErrorType>> {
  const result = (await action) as any;

  if (isActionError(result)) {
    throw new Error(result.error.message);
  }

  return result;
}
