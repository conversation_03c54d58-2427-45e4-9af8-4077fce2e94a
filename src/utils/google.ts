import { Env } from "@udoy/libs/env";
import { google } from "googleapis";

export function getGoogleClient() {
  const client = new google.auth.OAuth2({
    clientId: Env.GOOGLE_CLIENT_ID,
    clientSecret: Env.GOOGLE_CLIENT_SECRET,
    redirectUri: Env.NEXT_PUBLIC_FRONTEND_URL,
  });

  return client;
}

export async function getGoogleProfile(code: string) {
  const client = getGoogleClient();
  const token = await client.getToken(code);
  const ticket = await client.verifyIdToken({
    idToken: token.tokens.id_token!,
    audience: Env.GOOGLE_CLIENT_ID, // Use Env instead of process.env
  });
  return ticket.getPayload();
}
