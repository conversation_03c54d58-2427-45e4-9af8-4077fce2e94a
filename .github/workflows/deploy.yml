name: Deploy to Production
concurrency: production

on:
  push:
    branches: [dev]

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: production

    steps:
      - name: SSH into Server & Deploy
        uses: appleboy/ssh-action@v1
        with:
          host: ${{ secrets.SERVER_IP }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SERVER_SSH_KEY }}
          script: |
            eval $(ssh-agent -s)

            echo "${{ secrets.SERVER_SSH_KEY }}" > ~/.ssh/temp_deploy_key
            chmod 600 ~/.ssh/temp_deploy_key
            ssh-add ~/.ssh/temp_deploy_key
            rm ~/.ssh/temp_deploy_key # Clean up the temporary key file

            # Optional: Verify the key is added
            ssh-add -l

            export NVM_DIR=~/.nvm
            source ~/.nvm/nvm.sh
            cd ~/udoymart

            git remote -v

            npx jiti tools/deploy.ts
